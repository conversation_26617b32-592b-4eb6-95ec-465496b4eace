<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.7.1</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>
    <groupId>com.pacto</groupId>
    <artifactId>adm-core-ms</artifactId>
    <version>1.0.0</version>
    <name>AdmCoreMS</name>
    <description>Microsserviço referente ao core do ZW-Adm</description>

    <properties>
        <app.config.debug>false</app.config.debug>
        <java.version>8</java.version>
        <artifact.name>adm-core-ms</artifact.name>
        <hibernate.version>5.4.4.Final</hibernate.version>
        <app.deploy.datacenter></app.deploy.datacenter>
        <discovery.url>http://host.docker.internal:8101</discovery.url>
        <url.fotos.nuvem>https://cdn1.pactorian.net</url.fotos.nuvem>
        <autenticacao.url>http://localhost:8088</autenticacao.url>
        <media.url>http://localhost:28099/media-ms</media.url>

        <hostOAMD>localhost</hostOAMD>
        <sufixoInfra></sufixoInfra>
        <sufixoPorta>32</sufixoPorta>
        <contexto></contexto>
        <maxMemory>1g</maxMemory>
        <host></host>
        <sshPort></sshPort>
        <sshUser>root</sshUser>

        <oamd.app.url>https://app.pactosolucoes.com.br/oamd</oamd.app.url>
        <oamd.db.url>jdbc:postgresql://${hostOAMD}:54${sufixoPorta}/OAMD</oamd.db.url>
        <oamd.db.username>postgres</oamd.db.username>
        <oamd.db.password>pactodb</oamd.db.password>

        <oamd2.db.url>jdbc:postgresql://${hostOAMD}:54${sufixoPorta}/OAMD2</oamd2.db.url>
        <oamd2.db.username>postgres</oamd2.db.username>
        <oamd2.db.password>pactodb</oamd2.db.password>

        <org.mapstruct.version>1.5.2.Final</org.mapstruct.version>
        <org.projectlombok.version>1.18.20</org.projectlombok.version>
        <lombok-mapstruct-binding.version>0.2.0</lombok-mapstruct-binding.version>

        <openapi.version>1.8.0</openapi.version>
        <org.json.version>20220320</org.json.version>
        <modelmapper.version>3.1.0</modelmapper.version>
        <handlebars.version>4.1.2</handlebars.version>
        <gson.version>2.8.6</gson.version>


    </properties>

    <profiles>
        <profile>
            <id>desenv</id>
            <properties>
                <server.port>8111</server.port>
                <AUTH_SECRET_PATH>C:\\git\pacto-solucoes\adm-core-ms\docker\keys\auth-secret</AUTH_SECRET_PATH>
                <AUTH_SECRET_ZW_PATH>C:\\git\pacto-solucoes\adm-core-ms\docker\keys\auth-secret</AUTH_SECRET_ZW_PATH>

                <oamd.app.url>http://localhost:8202/NewOAMD</oamd.app.url>

                <profile>desenv</profile>
                <environment>dev</environment>
            </properties>
        </profile>

        <profile>
            <id>desenv-windows</id>
            <properties>
                <server.port>8203</server.port>
                <AUTH_SECRET_PATH>C:\\opt\\secret\\secret.txt</AUTH_SECRET_PATH>
                <AUTH_SECRET_ZW_PATH>C:\\opt\\secret\\secret.txt</AUTH_SECRET_ZW_PATH>
                <discovery.url>http://localhost:8101</discovery.url>
                <profile>desenv</profile>
                <environment>dev</environment>
            </properties>
        </profile>

        <profile>
            <id>docker</id>
            <properties>
                <server.port>8080</server.port>
                <AUTH_SECRET_PATH>${project.basedir}/docker/keys/auth-secret</AUTH_SECRET_PATH>
                <AUTH_SECRET_ZW_PATH>${project.basedir}/docker/keys/auth-secret</AUTH_SECRET_ZW_PATH>


                <contexto></contexto>
                <environment>dev</environment>
            </properties>
        </profile>

        <profile>
            <id>local</id>
            <properties>
                <host>zw${sufixoInfra}.pactosolucoes.com.br</host>
                <server.port>8111</server.port>
                <AUTH_SECRET_PATH>${project.basedir}/docker/keys/auth-secret</AUTH_SECRET_PATH>
                <discovery.url>http://localhost:8101</discovery.url>
                <autenticacao.url>http://localhost:8100</autenticacao.url>
                <media.url>http://localhost:8107</media.url>
                <contexto></contexto>
            </properties>
        </profile>

        <profile>
            <id>producao</id>
            <properties>
                <host>zw${sufixoInfra}.pactosolucoes.com.br</host>
                <sshUser>root</sshUser>
                <sshPort>22${sufixoInfra}</sshPort>
                <sshPwd></sshPwd>
                <keyfile>/root/.ssh/id_rsa</keyfile>
                <contexto>adm-core-ms</contexto>

                <server.port>28101</server.port>
                <maxMemory>1g</maxMemory>
                <AUTH_SECRET_PATH>/home/<USER>/.ssh/auth-secret</AUTH_SECRET_PATH>
                <AUTH_SECRET_ZW_PATH>/home/<USER>/.ssh/auth-secret-zw</AUTH_SECRET_ZW_PATH>
                <discovery.url>https://discovery.ms.pactosolucoes.com.br</discovery.url>
                <url.fotos.nuvem>https://cdn1.pactorian.net</url.fotos.nuvem>
                <autenticacao.url>https://auth.ms.pactosolucoes.com.br</autenticacao.url>
            </properties>
        </profile>

        <profile>
            <id>scp</id>
            <properties>
                <sufixoInfra></sufixoInfra>
                <host>zw${sufixoInfra}.pactosolucoes.com.br</host>
                <sshUser>root</sshUser>
                <sshPort>22${sufixoInfra}</sshPort>
                <sshPwd></sshPwd>
                <keyfile>/root/.ssh/id_rsa</keyfile>
            </properties>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-antrun-plugin</artifactId>
                        <version>1.8</version>
                        <dependencies>
                            <dependency>
                                <groupId>org.apache.ant</groupId>
                                <artifactId>ant-jsch</artifactId>
                                <version>1.9.4</version>
                            </dependency>
                            <dependency>
                                <groupId>com.jcraft</groupId>
                                <artifactId>jsch</artifactId>
                                <version>0.1.54</version>
                            </dependency>
                        </dependencies>
                        <executions>
                            <execution>
                                <id>scp-exec</id>
                                <phase>pre-integration-test</phase>
                                <goals>
                                    <goal>run</goal>
                                </goals>
                                <configuration>
                                    <target name="task-scp">
                                        <tstamp>
                                            <format property="now" pattern="yyyy-MM-dd HH:mm:ss"/>
                                        </tstamp>
                                        <echo message="${now} Transferindo ${sshUser}@${host}:/opt/${artifact.name}/${artifact.name}.jar..."/>
                                        <sshexec port="${sshPort}" verbose="false"
                                                 trust="true" host="${host}"
                                                 username="${sshUser}"
                                                 passphrase="${sshPwd}"
                                                 keyfile="${keyfile}"
                                                 command="mkdir -p /opt/${artifact.name}"/>
                                        <scp trust="true" file="${basedir}/target/${artifact.name}.jar"
                                             port="${sshPort}"
                                             verbose="false"
                                             passphrase="${sshPwd}"
                                             keyfile="${keyfile}"
                                             todir="${sshUser}@${host}:/opt/${artifact.name}"/>

                                        <sshexec port="${sshPort}" verbose="false"
                                                 trust="true" host="${host}"
                                                 username="${sshUser}"
                                                 passphrase="${sshPwd}"
                                                 keyfile="${keyfile}"
                                                 command="chmod +x /opt/${artifact.name}/${artifact.name}.jar &amp;&amp; chown glassfish -R /opt/${artifact.name} &amp;&amp;  if [ ! -f /var/log/${artifact.name}.log ]; then touch /var/log/${artifact.name}.log; fi &amp;&amp; chown glassfish -R /var/log/${artifact.name}.log"/>
                                        <tstamp>
                                            <format property="end" pattern="yyyy-MM-dd HH:mm:ss"/>
                                        </tstamp>
                                        <echo message="${end} Transferencia concluida!"/>
                                    </target>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>

        <profile>
            <id>redeploy</id>
            <build>
                <finalName>${artifact.name}</finalName>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-antrun-plugin</artifactId>
                        <version>1.8</version>
                        <dependencies>
                            <dependency>
                                <groupId>org.apache.ant</groupId>
                                <artifactId>ant-jsch</artifactId>
                                <version>1.9.4</version>
                            </dependency>
                            <dependency>
                                <groupId>com.jcraft</groupId>
                                <artifactId>jsch</artifactId>
                                <version>0.1.54</version>
                            </dependency>
                            <dependency>
                                <groupId>ant-contrib</groupId>
                                <artifactId>ant-contrib</artifactId>
                                <version>1.0b3</version>
                                <exclusions>
                                    <exclusion>
                                        <artifactId>ant</artifactId>
                                        <groupId>ant</groupId>
                                    </exclusion>
                                </exclusions>
                            </dependency>
                        </dependencies>
                        <executions>
                            <execution>
                                <id>redeploy-exec</id>
                                <phase>post-integration-test</phase>
                                <goals>
                                    <goal>run</goal>
                                </goals>
                                <configuration>
                                    <target name="task-redeploy">
                                        <tstamp>
                                            <format property="now" pattern="yyyy-MM-dd HH:mm:ss"/>
                                        </tstamp>
                                        <echo message="${now} Implantar Serviço..."/>
                                        <sshexec port="${sshPort}" verbose="false"
                                                 trust="true" host="${host}"
                                                 username="${sshUser}"
                                                 passphrase="${sshPwd}"
                                                 keyfile="${keyfile}"
                                                 command="ln -sf /opt/${artifact.name}/${artifact.name}.jar /etc/init.d/${artifact.name} &amp;&amp; chmod a+x /etc/init.d/${artifact.name} &amp;&amp; /etc/init.d/${artifact.name} force-stop  &amp;&amp; /etc/init.d/${artifact.name} start"/>
                                        <echo message="${end} OK!"/>
                                    </target>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>

    <dependencies>
        <dependency>
            <groupId>com.auth0</groupId>
            <artifactId>java-jwt</artifactId>
            <version>3.19.1</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.json/json -->
        <dependency>
            <groupId>org.json</groupId>
            <artifactId>json</artifactId>
            <version>20190722</version>
        </dependency>

        <dependency>
            <groupId>org.hibernate</groupId>
            <artifactId>hibernate-entitymanager</artifactId>
            <version>${hibernate.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.hibernate.javax.persistence</groupId>
                    <artifactId>hibernate-jpa-2.0-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>javassist</groupId>
                    <artifactId>javassist</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.hibernate</groupId>
            <artifactId>hibernate-core</artifactId>
            <version>${hibernate.version}</version>
        </dependency>

        <dependency>
            <groupId>org.hibernate</groupId>
            <artifactId>hibernate-hikaricp</artifactId>
            <version>${hibernate.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-logging</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
            <scope>runtime</scope>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.junit.vintage</groupId>
                    <artifactId>junit-vintage-engine</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.projectlombok/lombok -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpcore</artifactId>
            <version>4.4.14</version>
        </dependency>

        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <version>4.5.13</version>
        </dependency>

        <dependency>
            <groupId>com.pacto</groupId>
            <artifactId>pacto-commons-old-ms</artifactId>
            <version>1.0.23</version>
        </dependency>



        <!-- SWAGGER -->
        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-ui</artifactId>
            <version>${openapi.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-hateoas</artifactId>
            <version>${openapi.version}</version>
        </dependency>


        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-hateoas</artifactId>
            <version>${openapi.version}</version>
        </dependency>

        <dependency>
            <groupId>org.json</groupId>
            <artifactId>json</artifactId>
            <version>${org.json.version}</version>
        </dependency>

        <dependency>
            <groupId>org.modelmapper</groupId>
            <artifactId>modelmapper</artifactId>
            <version>${modelmapper.version}</version>
        </dependency>

        <dependency>
            <groupId>com.github.jknack</groupId>
            <artifactId>handlebars</artifactId>
            <version>${handlebars.version}</version>
        </dependency>


        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
        </dependency>

        <dependency>
            <groupId>javax.validation</groupId>
            <artifactId>validation-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-core</artifactId>
        </dependency>


    </dependencies>

    <build>
        <finalName>adm-core-ms</finalName>

        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${project.parent.version}</version>
                <configuration>
                    <executable>true</executable>
                    <fork>true</fork>
                    <executable>true</executable>
                    <excludes>
                        <exclude>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                        </exclude>
                    </excludes>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <executable>true</executable>
                    <fork>true</fork>
                    <executable>true</executable>
                    <jvmArguments>-Xmx${maxMemory}</jvmArguments>
                </configuration>
            </plugin>
            <plugin>

                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <source>1.8</source> <!-- depending on your project -->
                    <target>1.8</target> <!-- depending on your project -->
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${org.mapstruct.version}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${org.projectlombok.version}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok-mapstruct-binding</artifactId>
                            <version>${lombok-mapstruct-binding.version}</version>
                        </path>
                        <!-- other annotation processors -->
                    </annotationProcessorPaths>
                </configuration>
            </plugin>

        </plugins>
    </build>

</project>
