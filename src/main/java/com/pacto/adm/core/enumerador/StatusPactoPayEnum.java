package com.pacto.adm.core.enumerador;

public enum StatusPactoPayEnum {

    NENHUM(                     0, "NENHUM", ""),
    ERRO                       (1, "ERRO", ""),
    APROVADA_AGUARDANDO_CAPTURA(2, "APROVADA_AGUARDANDO_CAPTURA", ""),
    NAO_APROVADA(               3, "NAO_APROVADA", ""),
    ENVIADA(                    4, "ENVIADA", ""),
    CONCLUIDA_COM_SUCESSO(      5, "CONCLUIDA_COM_SUCESSO", "PAGA"),
    CANCELAMENTO_SOLICITADO(    6, "CANCELAMENTO_SOLICITADO", ""),
    CANCELADA(                  7, "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>AD<PERSON>"),
    PENDENTE(                   8, "PENDENTE", ""),
    GERADA(                     9, "GERADA", "GERADA"),
    ESTORNADA(                  10, "ESTORNADA", ""),
    PROCESSADO(                 11, "PROCESSADO", ""),
    ERRO_RETORNO(               12, "ERRO_RETORNO", ""),
    AGUARDANDO(                 13, "AGUAR<PERSON><PERSON><PERSON>", "ATIVA"),
    EXPIRADO(                   14, "EXPIRADO", "EXPIRADA");

    private Integer codigo;
    private String descricao;
    private String descricaoPix;

    StatusPactoPayEnum(Integer codigo, String descricao, String descricaoPix) {
        this.codigo = codigo;
        this.descricao = descricao;
        this.descricaoPix = descricaoPix;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public static StatusPactoPayEnum obterPorCodigo(Integer codigo) {
        for (StatusPactoPayEnum situacaoTransacaoEnum : StatusPactoPayEnum.values()) {
            if (situacaoTransacaoEnum.getCodigo().equals(codigo)) {
                return situacaoTransacaoEnum;
            }
        }
        return null;
    }

    public String getDescricaoPix() {
        return descricaoPix;
    }
}
