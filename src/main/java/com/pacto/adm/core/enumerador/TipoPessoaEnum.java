package com.pacto.adm.core.enumerador;

public enum TipoPessoaEnum {

    NENHUM(0, ""),
    CLIENTE(1, "Cliente"),
    COLABORADOR(2, "Colaborador");

    private Integer codigo;
    private String descricao;

    TipoPessoaEnum(Integer codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public String getDescricao() {
        return descricao;
    }
}
