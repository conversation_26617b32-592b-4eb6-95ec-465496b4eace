package com.pacto.adm.core.enumerador;

public enum TipoComprovanteVacinaEnum {

    COVID_PRIMEIRA_DOSE(1, "1° Dose"),
    COVID_SEGUNDA_DOSE(2, "2° Dose"),
    COVID_TERCEIRA_DOSE(3, "3° Dose");

    private Integer codigo;
    private String descricao;

    TipoComprovanteVacinaEnum(Integer codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public static TipoComprovanteVacinaEnum getInstance(String codigo) {
        if (codigo != null && !codigo.isEmpty()) {
            for (TipoComprovanteVacinaEnum situacaoAluno : TipoComprovanteVacinaEnum.values()) {
                if (codigo.equals(situacaoAluno.getCodigo())) {
                    return situacaoAluno;
                }
            }
        }
        return null;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
}
