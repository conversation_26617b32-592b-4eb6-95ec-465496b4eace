/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package com.pacto.adm.core.enumerador;

public enum TipoRemessaEnum {

    DESCONHECIDO(0, "Desconhecido", "", TipoTransacaoEnum.NENHUMA),
    APROVA_FACIL(1, "Aprova Fácil", "", TipoTransacaoEnum.NENHUMA),
    EDI_CIELO(2, "Cielo DCC", "CIELOEDIDCC", TipoTransacaoEnum.NENHUMA),
    BB_DCO(3, "BB DCO", "BBDCO", TipoTransacaoEnum.NENHUMA),
    BRADESCO_DCO(4, "<PERSON><PERSON><PERSON><PERSON> DCO", "BRAD<PERSON>CODCO", TipoTransacaoEnum.NENHUMA),
    ITAU_DCO(5, "ITAU DCO", "ITAUDCO", TipoTransacaoEnum.NENHUMA),
    CAIXA_DCO(6, "CAIXA DCO", "<PERSON><PERSON>AD<PERSON>", TipoTransacaoEnum.NENHUMA),
    HSBC_DCO(7, "HSBC DCO", "HSBCDCO", TipoTransacaoEnum.NENHUMA),
    GET_NET(8, "GET NET DCO", "GETNETDCC", TipoTransacaoEnum.NENHUMA),
    ITAU_BOLETO(9, "ITAU BOLETO", "ITAUBOLETO", TipoTransacaoEnum.NENHUMA),
    ITAU_CNAB400(25, "ITAU BOLETO CNAB400", "ITAUBOLETOCNAB400", TipoTransacaoEnum.NENHUMA),
    BOLETO(10, "Boleto Bancário", "BOLETOBANCARIO", TipoTransacaoEnum.NENHUMA),
    SANTANDER_DCO(11, "SANTANDER DCO", "SANTANDERDCO", TipoTransacaoEnum.NENHUMA),
    DCC_BIN(12, "BIN DCC", "BINDCC", TipoTransacaoEnum.NENHUMA),
    DCC_VINDI(13, "VINDI DCC","VINDIDCC", TipoTransacaoEnum.VINDI),
    DCC_CIELO_ONLINE(14, "DCC CIELO ONLINE","DCCCIELOONLINE", TipoTransacaoEnum.CIELO_ONLINE),
    DCC_MAXIPAGO(16, "MAXIPAGO DCC","MAXIPAGODCC", TipoTransacaoEnum.MAXIPAGO),
    DCC_E_REDE(17, "e-Rede DCC","EREDEDCC", TipoTransacaoEnum.E_REDE),
    DAYCOVAL_BOLETO(18, "DAYCOVAL BOLETO", "DAYCOVALBOLETO", TipoTransacaoEnum.NENHUMA),
    DCC_FITNESS_CARD(19, "DCC FITNESS CARD","DCCFITNESSCARD", TipoTransacaoEnum.FITNESS_CARD),
    DCC_GETNET_ONLINE(20, "DCC GETNET ONLINE","DCCGETNETONLINE", TipoTransacaoEnum.GETNET_ONLINE),
    DCC_STONE(21, "STONE DCC", "STONEDCC", TipoTransacaoEnum.STONE_ONLINE),
    CAIXA_SICOV_DCO(22, "CAIXA SICOV DCO", "CAIXASICOVDCO", TipoTransacaoEnum.NENHUMA),
    DCC_MUNDIPAGG(23, "DCC Mundipagg", "DCCMUNDIPAGG", TipoTransacaoEnum.MUNDIPAGG),
    DCC_PAGAR_ME(24, "DCC Pagar.me", "DCCPAGARME", TipoTransacaoEnum.PAGAR_ME),
    PIX(25, "Pix" ,"PIX", TipoTransacaoEnum.PACTO_PAY),
    DCC_STRIPE(26, "DCC Stripe", "DCCSTRIPE", TipoTransacaoEnum.STRIPE),
    DCC_PAGOLIVRE(27, "DCC PagoLivre","DCCPAGOLIVRE", TipoTransacaoEnum.PAGOLIVRE),
    DCC_VALORIBANK(28, "DCC ValoriBank","DCCVALORIBANK", TipoTransacaoEnum.VALORIBANK),
    DCC_ONE_PAYMENT(29, "DCC One Payment","DCCONEPAYMENT", TipoTransacaoEnum.ONE_PAYMENT),
    ;


    private int id;
    private String descricao;
    private String identificador;
    private TipoTransacaoEnum tipoTransacao;

    private TipoRemessaEnum(int id, String descricao, String identificador, TipoTransacaoEnum tipoTransacao) {
        this.id = id;
        this.descricao = descricao;
        this.identificador = identificador;
        this.tipoTransacao = tipoTransacao;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getIdentificador() {
        return identificador;
    }

    public void setIdentificador(String identificador) {
        this.identificador = identificador;
    }

    public static TipoRemessaEnum getTipoRemessaEnum(final int codigo) {
        for (TipoRemessaEnum tipo : TipoRemessaEnum.values()) {
            if (tipo.getId() == codigo) {
                return tipo;
            }
        }
        return TipoRemessaEnum.DESCONHECIDO;
    }
}
