package com.pacto.adm.core.services.implementations;

import com.pacto.adm.core.entities.MovPagamento;
import com.pacto.adm.core.services.interfaces.ProdutosPagosService;
import com.pacto.config.utils.Uteis;
import com.pacto.config.utils.UteisValidacao;
import org.springframework.stereotype.Service;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;

@Service
public class ProdutoPagosServiceImpl implements ProdutosPagosService {

    private static MovPagamento obterDadosPagamento(Connection con, Integer codigoPagamento, String tipoForma, Map<Integer, MovPagamento> mapaPagamentos) throws SQLException {

        if (!mapaPagamentos.containsKey(codigoPagamento)) {
            MovPagamento novo = new MovPagamento();
            novo.setCodigo(codigoPagamento);
            String consultaValores = "";

            if (tipoForma.equals("CA")) {
                consultaValores = "select valor,0 as valortotal, situacao from cartaocredito where movpagamento = " + codigoPagamento;
            } else if (tipoForma.equals("CH")) {
                consultaValores = "select valor,0 as valortotal, situacao from cheque where movpagamento = " + codigoPagamento;
            } else {
                consultaValores = "select valor,valortotal, 'NAORECEBIVEL' as situacao from movpagamento where codigo = " + codigoPagamento;
            }
            ResultSet valores = con.prepareStatement(consultaValores).executeQuery();
            while (valores.next()) {
                if (valores.getString("situacao").equals("CA")) {
                    novo.setValorPPCancelado(Uteis.arredondarForcando2CasasDecimais(novo.getValorPPCancelado() + valores.getDouble("valor")));
                } else if (valores.getString("situacao").equals("EA")) {
                    novo.setValorPP(Uteis.arredondarForcando2CasasDecimais(novo.getValorPP() + valores.getDouble("valor")));
                } else {
                    novo.setValorPPCancelado(Uteis.arredondarForcando2CasasDecimais(novo.getValorPPCancelado() + valores.getDouble("valortotal")));
                    novo.setValorPP(Uteis.arredondarForcando2CasasDecimais(novo.getValorPP() + valores.getDouble("valor")));
                }
            }
            mapaPagamentos.put(codigoPagamento, novo);

        }

        return mapaPagamentos.get(codigoPagamento);
    }

    public static class Parcela {
        int pagamentoParcela;
        int parcela;
        List<ProdutoParcela> produtos = new ArrayList<ProdutoParcela>();
        List<ProdutoParcela> produtosCancelados = new ArrayList<ProdutoParcela>();

        public Parcela(int pmp, int parc) {
            pagamentoParcela = pmp;
            parcela = parc;
        }
    }

    public static class ProdutoParcela {
        int produto;
        String tipo;
        double valorPago;
        double valorUsado = 0.0;
        int parcela;
        int contrato;
        String situacao;
        boolean pagamentoDebito = false;

        public ProdutoParcela(int prod, double pago, int parc, int cont, String t, String sit, boolean pgDebito) {
            valorPago = pago;
            valorUsado = pago;
            produto = prod;
            parcela = parc;
            contrato = cont;
            tipo = t;
            situacao = sit;
            pagamentoDebito = pgDebito;
        }
    }

    public void setarProdutosPagos(Connection con, Integer codigoRecibo) {
        try {
            List<ProdutoParcela> produtosParcela = new ArrayList<ProdutoParcela>();
            Map<Integer, List<Integer>> mapaProdParcelas = new HashMap<Integer, List<Integer>>();
            List<ProdutoParcela> produtosParcelaCancelados = new ArrayList<ProdutoParcela>();
            ResultSet produtos = con.prepareStatement("SELECT mpp.movparcela, mpp.movproduto, mpp.valorpago, (p.tipoproduto = 'PM') AS produtoplano,p.tipoproduto, mp.contrato,mp.situacao, mp.movpagamentocc  FROM movprodutoparcela mpp " +
                    " INNER JOIN movproduto mp ON mpp.movproduto = mp.codigo " +
                    " INNER JOIN produto p ON p.codigo = mp.produto" +
                    " where recibopagamento  = " + codigoRecibo + " order by produtoplano, movparcela ,movproduto ").executeQuery();
            while (produtos.next()) {
                if (produtos.getString("situacao").equals("CA") || produtos.getString("tipoproduto").equals("CC") || !UteisValidacao.emptyString(produtos.getString("movpagamentocc"))) {
                    produtosParcelaCancelados.add(new ProdutoParcela(produtos.getInt("movproduto"),
                            produtos.getDouble("valorpago"), produtos.getInt("movparcela"), produtos.getInt("contrato"), produtos.getString("tipoproduto"), produtos.getString("situacao"), !UteisValidacao.emptyString(produtos.getString("movpagamentocc"))));
                } else {
                    produtosParcela.add(new ProdutoParcela(produtos.getInt("movproduto"),
                            produtos.getDouble("valorpago"), produtos.getInt("movparcela"), produtos.getInt("contrato"), produtos.getString("tipoproduto"), produtos.getString("situacao"), !UteisValidacao.emptyString(produtos.getString("movpagamentocc"))));
                }
                if (!produtos.getString("tipoproduto").equals("PM")) {
                    if (!mapaProdParcelas.containsKey(produtos.getInt("movproduto"))) {
                        mapaProdParcelas.put(produtos.getInt("movproduto"), new ArrayList<Integer>());
                    }
                    if (!mapaProdParcelas.get(produtos.getInt("movproduto")).contains(produtos.getInt("movparcela"))) {
                        mapaProdParcelas.get(produtos.getInt("movproduto")).add(produtos.getInt("movparcela"));
                    }
                }
            }
            produtosParcela.addAll(produtosParcelaCancelados);

            Map<Integer, List<Parcela>> mapa = new HashMap<Integer, List<Parcela>>();
            Map<Integer, MovPagamento> mapaPagamentos = new HashMap<Integer, MovPagamento>();
            //obter as parcelas
            ResultSet rs = con.prepareStatement(" SELECT pmp.codigo, pmp.valorpago, pmp.movparcela, mp.descricao, mp.contrato, pmp.movpagamento,fp.tipoformapagamento "
                    + " from pagamentomovparcela pmp "
                    + " INNER JOIN movparcela mp ON pmp.movparcela = mp.codigo "
                    + " inner join movpagamento mov on mov.codigo = pmp.movpagamento "
                    + " inner join formapagamento fp on mov.formapagamento = fp.codigo "
                    + " WHERE pmp.recibopagamento = " + codigoRecibo + " order by pmp.codigo ").executeQuery();
            while (rs.next()) {
                Parcela parcela = new Parcela(rs.getInt("codigo"), rs.getInt("movparcela"));
                double valor = Uteis.arredondarForcando2CasasDecimais(rs.getDouble("valorpago"));
                MovPagamento pagamento = obterDadosPagamento(con, rs.getInt("movpagamento"), rs.getString("tipoformapagamento"), mapaPagamentos);

                for (ProdutoParcela proPa : produtosParcela) {
                    boolean produtoParcelaAdcionado = false;
                    if (proPa.tipo != null && proPa.tipo.equals("TP")) {
                        ProdutoParcela produtoDoPagamentoDaParcela = new ProdutoParcela(proPa.produto, proPa.valorPago,
                                rs.getInt("movparcela"), proPa.contrato, proPa.tipo, proPa.situacao, proPa.pagamentoDebito);
                        parcela.produtos.add(produtoDoPagamentoDaParcela);
                        produtoParcelaAdcionado = true;
                    }
                    if (proPa.parcela == parcela.parcela && Uteis.arredondarForcando2CasasDecimais(proPa.valorUsado) > 0.0) {
                        Double valorNaParcela = 0.0;
                        Double valorRetirar = 0.0;
                        if ((proPa.situacao.equals("CA") || proPa.tipo.equals("CC") || proPa.pagamentoDebito) && pagamento.getValorPPCancelado() > 0.0) {
                            if (pagamento.getValorPPCancelado() >= Uteis.arredondarForcando2CasasDecimais(proPa.valorUsado)) {
                                valorRetirar = Uteis.arredondarForcando2CasasDecimais(proPa.valorUsado);
                            } else {
                                valorRetirar = pagamento.getValorPPCancelado();
                            }
                        } else if (!proPa.situacao.equals("CA") && pagamento.getValorPP() > 0.0) {
                            if (pagamento.getValorPP() >= Uteis.arredondarForcando2CasasDecimais(proPa.valorUsado)) {
                                valorRetirar = Uteis.arredondarForcando2CasasDecimais(proPa.valorUsado);
                            } else {
                                valorRetirar = pagamento.getValorPP();
                            }
                        }
                        if (valorRetirar == 0.0) {
                            continue;
                        }
                        if (Uteis.arredondarForcando2CasasDecimais(valorRetirar) > Uteis.arredondarForcando2CasasDecimais(valor)) {
                            valorRetirar = valor;
                        }
                        if (valorRetirar >= Uteis.arredondarForcando2CasasDecimais(proPa.valorUsado)) {
                            valorNaParcela = proPa.valorUsado;
                            valor = Uteis.arredondarForcando2CasasDecimais(valor - proPa.valorUsado);
                            proPa.valorUsado = 0.0;
                        } else {
                            valorNaParcela = valorRetirar;
                            proPa.valorUsado = Uteis.arredondarForcando2CasasDecimais(proPa.valorUsado - valorRetirar);
                            valor = Uteis.arredondarForcando2CasasDecimais(valor - valorRetirar);
                        }
                        ProdutoParcela produtoDoPagamentoDaParcela = new ProdutoParcela(proPa.produto, valorNaParcela,
                                rs.getInt("movparcela"), proPa.contrato, proPa.tipo, proPa.situacao, proPa.pagamentoDebito);
                        if (proPa.situacao.equals("CA") || proPa.pagamentoDebito || proPa.tipo.equals("CC")) {
                            parcela.produtosCancelados.add(produtoDoPagamentoDaParcela);
                            pagamento.setValorPPCancelado(Uteis.arredondarForcando2CasasDecimais(pagamento.getValorPPCancelado() - valorNaParcela));
                        } else if (!produtoParcelaAdcionado) {
                            parcela.produtos.add(produtoDoPagamentoDaParcela);
                            pagamento.setValorPP(Uteis.arredondarForcando2CasasDecimais(pagamento.getValorPP() - valorNaParcela));
                        }
                    }
                    if (valor <= 0.0) {
                        break;
                    }
                }
                List<Parcela> parcelasPagamento = mapa.get(rs.getInt("movpagamento"));
                if (parcelasPagamento == null) {
                    parcelasPagamento = new ArrayList<Parcela>();
                    mapa.put(rs.getInt("movpagamento"), parcelasPagamento);
                }
                parcelasPagamento.add(parcela);
            }
            Set<Integer> keySet = mapa.keySet();
            for (Integer key : keySet) {
                List<Parcela> parcelas = mapa.get(key);
                List<ProdutoParcela> produtosPagamento = new ArrayList<ProdutoParcela>();
                List<ProdutoParcela> produtosCanceladosPagamento = new ArrayList<ProdutoParcela>();
                String produtosPagos = "";
                for (Parcela parc : parcelas) {
                    for (ProdutoParcela prodPa : parc.produtos) {
                        produtosPagamento.add(prodPa);
                        produtosPagos += "|" + prodPa.produto + "," + prodPa.tipo + "," + prodPa.contrato + "," + Uteis.arredondarForcando2CasasDecimais(prodPa.valorPago);
                    }
                    for (ProdutoParcela prodPa : parc.produtosCancelados) {
                        produtosCanceladosPagamento.add(prodPa);
                        produtosPagos += "|" + prodPa.produto + "," + prodPa.tipo + "," + prodPa.contrato + "," + Uteis.arredondarForcando2CasasDecimais(prodPa.valorPago);
                    }
                }
                con.prepareStatement("UPDATE movpagamento SET produtospagos = '" + produtosPagos + "' WHERE codigo = " + key).execute();

                //cartoes
                ResultSet rsCartoes = con.prepareStatement("SELECT * FROM cartaocredito WHERE movpagamento = " + key + " ORDER BY datacompesancao").executeQuery();
                Integer nrCartoes = 0;
                ResultSet rsCountNrCartoes = con.createStatement().executeQuery("SELECT COUNT(*) as count FROM cartaocredito WHERE movpagamento = " + key);
                if (rsCountNrCartoes.next()) {
                    nrCartoes = rsCountNrCartoes.getInt("count");
                }

                while (rsCartoes.next()) {
                    Integer codigo = rsCartoes.getInt("codigo");
                    Double valor = rsCartoes.getDouble("valor");
                    String produtosPagosCartao = "";
                    if (rsCartoes.getString("situacao").equals("CA")) {
                        for (ProdutoParcela prodPa : produtosCanceladosPagamento) {
                            Double valorUsadoProduto = (prodPa.tipo.equals("PM") || mapaProdParcelas.get(prodPa.produto).size() > 1) ? prodPa.valorUsado : (prodPa.valorPago / nrCartoes);

                            if (Uteis.arredondarForcando2CasasDecimais(valor) > 0.0 && Uteis.arredondarForcando2CasasDecimais(prodPa.valorUsado) > 0.0) {

                                if (Uteis.arredondarForcando2CasasDecimais(valor) >= Uteis.arredondarForcando2CasasDecimais(valorUsadoProduto)) {
                                    produtosPagosCartao += "|" + prodPa.produto + "," + prodPa.tipo + "," + prodPa.contrato + "," + Uteis.arredondarForcando2CasasDecimais(valorUsadoProduto);
                                    valor = Uteis.arredondarForcando2CasasDecimais(valor) - Uteis.arredondarForcando2CasasDecimais(valorUsadoProduto);
                                    prodPa.valorUsado = prodPa.tipo.equals("PM") ? 0.0 : prodPa.valorUsado;
                                } else {
                                    produtosPagosCartao += "|" + prodPa.produto + "," + prodPa.tipo + "," + prodPa.contrato + "," + Uteis.arredondarForcando2CasasDecimais(valor);
                                    prodPa.valorUsado = Uteis.arredondarForcando2CasasDecimais(prodPa.valorUsado) - Uteis.arredondarForcando2CasasDecimais(valor);
                                    valor = 0.0;
                                    break;
                                }
                            }
                        }

                    } else {
                        for (ProdutoParcela prodPa : produtosPagamento) {
                            Double valorUsadoProduto = (prodPa.tipo.equals("PM") || mapaProdParcelas.get(prodPa.produto).size() > 1) ? prodPa.valorUsado : (prodPa.valorPago / nrCartoes);

                            if (Uteis.arredondarForcando2CasasDecimais(valor) > 0.0 && Uteis.arredondarForcando2CasasDecimais(prodPa.valorUsado) > 0.0) {

                                if (Uteis.arredondarForcando2CasasDecimais(valor) >= Uteis.arredondarForcando2CasasDecimais(valorUsadoProduto)) {
                                    produtosPagosCartao += "|" + prodPa.produto + "," + prodPa.tipo + "," + prodPa.contrato + "," + Uteis.arredondarForcando2CasasDecimais(valorUsadoProduto);
                                    valor = Uteis.arredondarForcando2CasasDecimais(valor) - Uteis.arredondarForcando2CasasDecimais(valorUsadoProduto);
                                    prodPa.valorUsado = (prodPa.tipo.equals("PM") || mapaProdParcelas.get(prodPa.produto).size() > 1) ? 0.0 : prodPa.valorUsado;
                                } else {
                                    produtosPagosCartao += "|" + prodPa.produto + "," + prodPa.tipo + "," + prodPa.contrato + "," + Uteis.arredondarForcando2CasasDecimais(valor);
                                    prodPa.valorUsado = Uteis.arredondarForcando2CasasDecimais(prodPa.valorUsado) - Uteis.arredondarForcando2CasasDecimais(valor);
                                    valor = 0.0;
                                    break;
                                }
                            }
                        }
                    }
                    con.prepareStatement("UPDATE cartaocredito SET produtospagos = '" + produtosPagosCartao + "' WHERE codigo = " + codigo).execute();
                }

                //cheques
                ResultSet rsCheques = con.prepareStatement("SELECT * FROM cheque WHERE movpagamento = " + key + " ORDER BY datacompesancao").executeQuery();
                while (rsCheques.next()) {
                    Integer codigo = rsCheques.getInt("codigo");
                    Double valor = rsCheques.getDouble("valor");
                    String produtosPagosCheque = "";
                    if (rsCheques.getString("situacao").equals("CA")) {
                        for (ProdutoParcela prodPa : produtosCanceladosPagamento) {
                            if (Uteis.arredondarForcando2CasasDecimais(valor) > 0.0 && Uteis.arredondarForcando2CasasDecimais(prodPa.valorUsado) > 0.0) {

                                if (Uteis.arredondarForcando2CasasDecimais(valor) >= Uteis.arredondarForcando2CasasDecimais(prodPa.valorUsado)) {
                                    produtosPagosCheque += "|" + prodPa.produto + "," + prodPa.tipo + "," + prodPa.contrato + "," + Uteis.arredondarForcando2CasasDecimais(prodPa.valorUsado);
                                    valor = Uteis.arredondarForcando2CasasDecimais(valor) - Uteis.arredondarForcando2CasasDecimais(prodPa.valorUsado);
                                    prodPa.valorUsado = 0.0;
                                } else {
                                    produtosPagosCheque += "|" + prodPa.produto + "," + prodPa.tipo + "," + prodPa.contrato + "," + Uteis.arredondarForcando2CasasDecimais(valor);
                                    prodPa.valorUsado = Uteis.arredondarForcando2CasasDecimais(prodPa.valorUsado) - Uteis.arredondarForcando2CasasDecimais(valor);
                                    break;
                                }
                            }
                        }
                    } else {
                        for (ProdutoParcela prodPa : produtosPagamento) {
                            if (Uteis.arredondarForcando2CasasDecimais(valor) > 0.0 && Uteis.arredondarForcando2CasasDecimais(prodPa.valorUsado) > 0.0) {

                                if (Uteis.arredondarForcando2CasasDecimais(valor) >= Uteis.arredondarForcando2CasasDecimais(prodPa.valorUsado)) {
                                    produtosPagosCheque += "|" + prodPa.produto + "," + prodPa.tipo + "," + prodPa.contrato + "," + Uteis.arredondarForcando2CasasDecimais(prodPa.valorUsado);
                                    valor = Uteis.arredondarForcando2CasasDecimais(valor) - Uteis.arredondarForcando2CasasDecimais(prodPa.valorUsado);
                                    prodPa.valorUsado = 0.0;
                                } else {
                                    produtosPagosCheque += "|" + prodPa.produto + "," + prodPa.tipo + "," + prodPa.contrato + "," + Uteis.arredondarForcando2CasasDecimais(valor);
                                    prodPa.valorUsado = Uteis.arredondarForcando2CasasDecimais(prodPa.valorUsado) - Uteis.arredondarForcando2CasasDecimais(valor);
                                    break;
                                }
                            }
                        }
                    }
                    con.prepareStatement("UPDATE cheque SET produtospagos = '" + produtosPagosCheque + "' WHERE codigo = " + codigo).execute();
                }

            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}
