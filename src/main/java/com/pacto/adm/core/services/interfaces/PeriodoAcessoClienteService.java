package com.pacto.adm.core.services.interfaces;

import com.pacto.adm.core.dto.AtestadoContratoDTO;
import com.pacto.adm.core.dto.LogTotalPassDTO;
import com.pacto.adm.core.dto.PeriodoAcessoClienteDTO;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.adm.core.entities.Cliente;
import com.pacto.adm.core.entities.contrato.PeriodoAcessoCliente;
import com.pacto.config.exceptions.ServiceException;

import java.time.LocalDate;
import java.util.List;

public interface PeriodoAcessoClienteService {

    void inicializarDadosPeriodoAcessoClienteAtestadoContrato(AtestadoContratoDTO atestadoContratoDTO) throws Exception;

    List<PeriodoAcessoClienteDTO> consultaPeriodoAcesso(Integer codPessoa, boolean gymPass, PaginadorDTO paginadorDTO) throws Exception;

    List<PeriodoAcessoClienteDTO> consultaPeriodoAcessoGoGood(Integer codPessoa, PaginadorDTO paginadorDTO) throws Exception;

    PeriodoAcessoCliente save(PeriodoAcessoClienteDTO periodoAcessoClienteDTO) throws ServiceException;

    List<PeriodoAcessoClienteDTO> findNextFreepass(Cliente cliente, LocalDate localDate) throws ServiceException;

    void delete(PeriodoAcessoClienteDTO periodoFreepass) throws ServiceException;

}
