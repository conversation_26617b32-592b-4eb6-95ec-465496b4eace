package com.pacto.adm.core.services.implementations;

import com.pacto.adm.core.adapters.FormaPagamentoAdapter;
import com.pacto.adm.core.adapters.MovPagamentoAdapter;
import com.pacto.adm.core.adapters.empresa.EmpresaAdapter;
import com.pacto.adm.core.dao.interfaces.ContratoDao;
import com.pacto.adm.core.dao.interfaces.EmpresaDao;
import com.pacto.adm.core.dao.interfaces.FormaPagamentoDao;
import com.pacto.adm.core.dao.interfaces.MovPagamentoDao;
import com.pacto.adm.core.dao.interfaces.nativerepositories.movpagamento.MovPagamentoNativeRepository;
import com.pacto.adm.core.dto.LogConciliadoraDTO;
import com.pacto.adm.core.dto.MovPagamentoDTO;
import com.pacto.adm.core.dto.filtros.FiltroBIControleOperacoesJSON;
import com.pacto.adm.core.dto.movpagamento.MovPagamentoTotaisDTO;
import com.pacto.adm.core.entities.MovPagamento;
import com.pacto.adm.core.enumerador.StatusPagamentoConciliadoraEnum;
import com.pacto.adm.core.services.interfaces.MovPagamentoService;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.utils.Uteis;
import com.pacto.config.utils.UteisValidacao;
import org.hibernate.engine.spi.SessionImplementor;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


@Service
public class MovPagamentoServiceImpl implements MovPagamentoService {

    @Autowired
    private MovPagamentoDao movPagamentoDao;
    @Autowired
    private MovPagamentoAdapter movPagamentoAdapter;
    @Autowired
    private ContratoDao contratoDao;
    @Autowired
    private EmpresaDao empresaDao;
    @Autowired
    private EmpresaAdapter empresaAdapter;
    @Autowired
    private FormaPagamentoAdapter formaPagamentoAdapter;
    @Autowired
    private FormaPagamentoDao formaPagamentoDao;
    @Autowired
    private MovPagamentoNativeRepository movPagamentoNativeRepository;

    public List<MovPagamentoDTO> findAllByCodPessoa(Integer codPessoa, JSONObject filtros, PaginadorDTO paginadorDTO) throws ServiceException {
        try {
            List<MovPagamento> movPagamentos;
            if (filtros != null && filtros.optBoolean("telaAluno")) {
                movPagamentos = buscarMovPagamentosTelaAluno(codPessoa, paginadorDTO);
            } else {
                movPagamentos = movPagamentoDao.findAllByPessoa(codPessoa, null, paginadorDTO);
            }
            if (filtros != null && filtros.optBoolean("buscarStatusConciliadora")) {
                preencherStatusConciliadora(movPagamentos);
            }
            return movPagamentoAdapter.toDtos(movPagamentos);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    private List<MovPagamento> buscarMovPagamentosTelaAluno(Integer codPessoa, PaginadorDTO paginadorDTO) throws Exception {
        List<Integer> codigos = new ArrayList<>();
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT ");
        sql.append(" distinct mp.codigo as movpagamento \n");
        sql.append(" FROM movpagamento mp \n");
        sql.append(" INNER JOIN recibopagamento rp on rp.codigo = mp.recibopagamento \n");
        sql.append(" INNER JOIN pagamentomovparcela pmp on rp.codigo = pmp.recibopagamento \n");
        sql.append(" INNER JOIN formapagamento fp ON fp.codigo = mp.formapagamento \n");
        sql.append(" INNER JOIN movparcela mparc ON pmp.movparcela = mparc.codigo AND mparc.pessoa =  ").append(codPessoa).append(" \n");
        sql.append(" LEFT JOIN negociacaoeventocontratopagamento necp ON necp.movpagamento = mp.codigo \n");
        sql.append(" INNER JOIN empresa emp ON mp.empresa = emp.codigo \n");
        sql.append(" INNER JOIN pessoa p ON p.codigo = mp.pessoa");
        sql.append(" WHERE (");
        sql.append(" (mp.pessoa = ").append(codPessoa).append(" AND mp.recibopagamento is not null) ");
        sql.append(" or (mparc.pessoa = ").append(codPessoa).append(")) \n");
        sql.append(" AND necp.contrato is null \n");
            try (SessionImplementor sessionImplementor = movPagamentoDao.createSessionCurrentWork()) {
                sessionImplementor.doWork(connection -> {
                    try {
                        ResultSet rs = movPagamentoDao.createStatement(connection, sql.toString());
                        while (rs.next()) {
                            codigos.add(rs.getInt("movpagamento"));
                        }
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    } finally {
                        connection.close();
                    }
                });
            }

            return movPagamentoDao.findAllByPessoa(null, codigos, paginadorDTO);
    }

    private void preencherStatusConciliadora(List<MovPagamento> lista) {
        if (UteisValidacao.emptyList(lista)){
            return;
        }

        StringBuilder codigos = new StringBuilder();
        for (MovPagamento obj : lista) {
            codigos.append(",").append(obj.getCodigo());
        }

        StringBuilder sql = new StringBuilder();
        sql.append("select \n");
        sql.append("mp.codigo as movpagamento, \n");
        sql.append("CASE \n");
        sql.append("WHEN fp.tipoformapagamento not in ('CA','CD') THEN ").append(StatusPagamentoConciliadoraEnum.OUTRO_FORMA_PG.getCodigo()).append(" \n");
        sql.append("WHEN mp.enviadoConciliadora THEN ").append(StatusPagamentoConciliadoraEnum.SUCESSO.getCodigo()).append(" \n");
        sql.append("WHEN not exists(select codigo from logconciliadora where movpagamento = mp.codigo) THEN ").append(StatusPagamentoConciliadoraEnum.AGUARDANDO.getCodigo()).append(" \n");
        sql.append("WHEN exists(select codigo from logconciliadora where sucesso = false and movpagamento = mp.codigo) THEN ").append(StatusPagamentoConciliadoraEnum.ERRO.getCodigo()).append(" \n");
        sql.append("ELSE ").append(StatusPagamentoConciliadoraEnum.NENHUM.getCodigo()).append(" END as statusConciliadora  \n");
        sql.append("from movpagamento mp \n");
        sql.append("inner join formapagamento fp on fp.codigo = mp.formapagamento  \n");
        sql.append("where mp.codigo in (").append(codigos.toString().replaceFirst(",", "")).append(") \n");
        try (SessionImplementor sessionImplementor = movPagamentoDao.createSessionCurrentWork()) {
            sessionImplementor.doWork(con -> {
                try {
                    PreparedStatement pst = con.prepareStatement(sql.toString());
                    ResultSet rs = pst.executeQuery();
                    while (rs.next()) {
                        Integer movPagamento = rs.getInt("movpagamento");
                        for (MovPagamento obj : lista) {
                            if (obj.getCodigo().equals(movPagamento)) {
                                obj.setStatusConciliadora(rs.getInt("statusConciliadora"));
                                break;
                            }
                        }
                    }
                } catch (Exception e) {
                    throw new RuntimeException(e);
                } finally {
                    con.close();
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public List<MovPagamentoDTO> findAllByCodContrato(Integer codContrato, PaginadorDTO paginadorDTO) throws ServiceException {
        List<MovPagamentoDTO> movPagamentoDTOS = buscaMovPagamentos(codContrato);
        return ordenaMovPagamento(paginadorDTO, movPagamentoDTOS);
    }

    private List<MovPagamentoDTO> buscaMovPagamentos(Integer codContrato) throws ServiceException {
        List<MovPagamentoDTO> movPagamentoDTOS = new ArrayList<>();

        try (SessionImplementor sessionImplementor = movPagamentoDao.createSessionCurrentWork()) {

            Integer codPessoa = contratoDao.findById(codContrato).getPessoa().getCodigo();

            sessionImplementor.doWork(connection -> {
                String sql = getSqlHistoricoPagamentos(codPessoa, codContrato);
                try {
                    ResultSet rs = movPagamentoDao.createStatement(connection, sql);
                    while (rs.next()) {
                        MovPagamentoDTO movPagamentoDTO = new MovPagamentoDTO();
                        movPagamentoDTO.setCodigo(rs.getInt("movpagamento"));
                        movPagamentoDTO.setReciboPagamento(rs.getInt("recibopagamento"));
                        movPagamentoDTO.setEmpresa(empresaAdapter.toDto(empresaDao.findById(rs.getInt("empresa"))));
                        movPagamentoDTO.setNomePessoaPagador(rs.getString("nomePagador"));
                        movPagamentoDTO.setDataLancamento(new Date(rs.getTimestamp("datalancamento").getTime()));
                        movPagamentoDTO.setValor(rs.getBigDecimal("valor"));
                        movPagamentoDTO.setValorTotal(rs.getBigDecimal("valortotal"));
                        movPagamentoDTO.setFormaPagamento(formaPagamentoAdapter.toDto(formaPagamentoDao.findById(rs.getInt("formapagamento"))));
                        movPagamentoDTO.setCredito(rs.getBoolean("creditoCC"));
                        movPagamentoDTOS.add(movPagamentoDTO);
                    }
                } catch (Exception e) {
                    throw new RuntimeException(e);
                } finally {
                    connection.close();
                }
            });

            return movPagamentoDTOS;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    private List<MovPagamentoDTO> ordenaMovPagamento (PaginadorDTO paginadorDTO, List<MovPagamentoDTO> movPagamentoDTOS) {
        if (paginadorDTO != null
                && paginadorDTO.getPage() != null
                && paginadorDTO.getSize() != null) {
            int primeiroPaginacao = (int) (paginadorDTO.getPage() * paginadorDTO.getSize());
            int ultimoRegistro = (int) ((paginadorDTO.getPage() * paginadorDTO.getSize()) + paginadorDTO.getSize());
            if (ultimoRegistro > movPagamentoDTOS.size()) {
                movPagamentoDTOS = movPagamentoDTOS.subList(primeiroPaginacao, movPagamentoDTOS.size());
            } else {
                movPagamentoDTOS = movPagamentoDTOS.subList(primeiroPaginacao, ultimoRegistro);
            }
        }

        if (paginadorDTO != null) {
            paginadorDTO.setQuantidadeTotalElementos((long) movPagamentoDTOS.size());
        }
        return movPagamentoDTOS;
    }

    private String getSqlHistoricoPagamentos(Integer codPessoa, Integer codContrato) {
        return "SELECT  distinct mp.codigo as movpagamento,mp.credito as creditoCC,\n" +
                "                 fp.tipoformapagamento, fp.codigo as formapagamento,\n" +
                "                 mp.recibopagamento, mp.nomepagador, mp.empresa, emp.nome,\n" +
                "                 mp.datalancamento, mp.datapagamento, mp.valor as valor, mp.valortotal as valorTotal,\n" +
                "                 mp.pessoa,\n" +
                "                 CASE\n" +
                "                     WHEN fp.tipoformapagamento not in ('CA','CD') THEN 4\n" +
                "                     WHEN mp.enviadoConciliadora THEN 2\n" +
                "                     WHEN not exists(select codigo from logconciliadora where movpagamento = mp.codigo) THEN 1\n" +
                "                     WHEN exists(select codigo from logconciliadora where sucesso = false and movpagamento = mp.codigo) THEN 3\n" +
                "                     ELSE 0 END as statusConciliadora\n" +
                "FROM movpagamento mp\n" +
                "         INNER JOIN recibopagamento rp on rp.codigo = mp.recibopagamento\n" +
                "         INNER JOIN pagamentomovparcela pmp on rp.codigo = pmp.recibopagamento\n" +
                "         INNER JOIN formapagamento fp ON fp.codigo = mp.formapagamento\n" +
                "         INNER JOIN movparcela mparc ON pmp.movparcela = mparc.codigo AND mparc.pessoa = "+ codPessoa + "\n" +
                "         LEFT JOIN negociacaoeventocontratopagamento necp ON necp.movpagamento = mp.codigo\n" +
                "         INNER JOIN empresa emp ON mp.empresa = emp.codigo\n" +
                "WHERE ( (mp.pessoa = " + codPessoa + " AND mp.recibopagamento is not null)  or (mparc.pessoa = + " + codPessoa + "))\n" +
                "  AND necp.contrato is null\n" +
                "  and mparc.contrato = " + codContrato + " ORDER BY mp.codigo DESC;";
    }

    public List<MovPagamentoDTO> findAllByCodRecibo(Integer codRecibo) throws ServiceException {
        try {
            List<MovPagamento> movPagamentos = movPagamentoDao.findAllByRecibo(codRecibo);
            return movPagamentoAdapter.toDtos(new ArrayList<>(movPagamentos));
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public void save(MovPagamento movPagamento) throws Exception {
        validarDados(movPagamento);
        movPagamentoDao.save(movPagamento);
    }

    /**
     * Operação responsável por validar os dados de um objeto da classe <code>MovPagamentoVO</code>.
     * Todos os tipos de consistência de dados são e devem ser implementadas neste método.
     * São validações típicas: verificação de campos obrigatórios, verificação de valores válidos para os atributos.
     * @exception ServiceException Se uma inconsistência for encontrada aumaticamente é gerada uma exceção descrevendo
     *                               o atributo e o erro ocorrido.
     */
    public void validarDados(MovPagamento movPagamento) throws ServiceException {
        if (movPagamento.getEmpresa() == null || movPagamento.getEmpresa().getCodigo() == 0) {
            throw new ServiceException("Empresa deve ser informada."); // campo novo
        }
        if (movPagamento.getDataPagamento() == null) {
            throw new ServiceException("O campo DATA PAGAMENTO (Movimento do Pagamento) deve ser informado.");
        }
        if (movPagamento.getDataLancamento() == null) {
            throw new ServiceException("O campo DATA LANÇAMENTO (Movimento do Pagamento) deve ser informado.");
        }
        if ((movPagamento.getFormaPagamento() == null)
                || (movPagamento.getFormaPagamento().getCodigo() == 0)) {
            throw new ServiceException("O campo FORMA PAGAMENTO (Movimento do Pagamento) deve ser informado.");
        }
        if ((movPagamento.getResponsavelPagamento() == null)
                || (movPagamento.getResponsavelPagamento().getCodigo() == 0)) {
            throw new ServiceException("O campo RESPONSÁVEL pelo PAGAMENTO (Movimento do Pagamento) deve ser informado.");
        }
        if (movPagamento.getNomePagador().equals("")) {
            throw new ServiceException("O campo NOME DO PAGADOR (Movimento do Pagamento) deve ser informado.");
        }

        if (movPagamento.getFormaPagamento().getTipoFormaPagamento().equals("CA") && movPagamento.getNrParcelaCartaoCredito() == 0) {
            throw new ServiceException("O campo PARCELAS  do CARTÃO CRÉDITO (Movimento do Pagamento) deve ser informado.");

        }
        if (movPagamento.getFormaPagamento().getTipoFormaPagamento().equals("CH")) {
            // TODO (lucasa) Implementar validações de cheques
//            Iterator i = movPagamento.getCheques().iterator();
//            while (i.hasNext()) {
//                Cheque novoCheque = (Cheque) i.next();
//                Cheque.validarDados(novoCheque);
//            }
        }
    }

    public List<LogConciliadoraDTO> obterLogConciliadora(Integer codMovPagamento) throws ServiceException {
        StringBuilder sql = new StringBuilder();
        sql.append("select \n");
        sql.append("* \n");
        sql.append("from logconciliadora \n");
        sql.append("where movpagamento = ").append(codMovPagamento).append(" \n");
        sql.append("order by data desc ");
        List<LogConciliadoraDTO> lista = new ArrayList<>();
        try (SessionImplementor sessionImplementor = movPagamentoDao.createSessionCurrentWork()) {
            sessionImplementor.doWork(con -> {
                try {
                    PreparedStatement pst = con.prepareStatement(sql.toString());
                    ResultSet rs = pst.executeQuery();
                    while (rs.next()) {
                        LogConciliadoraDTO log = new LogConciliadoraDTO();
                        log.setCodigo(rs.getInt("codigo"));
                        log.setData(rs.getTimestamp("data"));
                        log.setSucesso(rs.getBoolean("sucesso"));
                        log.setResultado(rs.getString("resultado"));
                        lista.add(log);
                    }
                } catch (Exception e) {
                    throw new RuntimeException(e);
                } finally {
                    con.close();
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
        return lista;
    }

    @Override
    public MovPagamentoTotaisDTO pagamentosComDataBaseAlterada(FiltroBIControleOperacoesJSON filtroBIControleOperacoesJSON, PaginadorDTO paginadorDTO) throws ServiceException {
        if (filtroBIControleOperacoesJSON.getInicio() == null) {
            throw new ServiceException("filtro-data-inicio-nao-informado", "Deve ser informado o filtro data início para consulta!");
        }

        if (filtroBIControleOperacoesJSON.getFim() == null) {
            throw new ServiceException("filtro-data-fim-nao-informado", "Deve ser informado o filtro data fim para consulta!");
        }

        try {
            return movPagamentoNativeRepository.pagamentosComDataBaseAlterada(
                    filtroBIControleOperacoesJSON, paginadorDTO
            );
        } catch (Exception e) {
            Uteis.logar(e, MovPagamentoServiceImpl.class);
            throw new ServiceException("erro-consulta-pagamentos-com-data-base-alterada", "Erro ao consultar os pagamentos com alteração na data base!");
        }
    }
}
