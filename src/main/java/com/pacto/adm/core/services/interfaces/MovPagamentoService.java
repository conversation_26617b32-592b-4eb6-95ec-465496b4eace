package com.pacto.adm.core.services.interfaces;

import com.pacto.adm.core.dto.LogConciliadoraDTO;
import com.pacto.adm.core.dto.MovPagamentoDTO;
import com.pacto.adm.core.dto.filtros.FiltroBIControleOperacoesJSON;
import com.pacto.adm.core.dto.movpagamento.MovPagamentoTotaisDTO;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.adm.core.entities.MovPagamento;
import com.pacto.config.exceptions.ServiceException;
import org.json.JSONObject;

import java.util.List;

public interface MovPagamentoService {

    List<MovPagamentoDTO> findAllByCodPessoa(Integer codPessoa, JSONObject filtros, PaginadorDTO paginadorDTO) throws ServiceException;

    List<MovPagamentoDTO> findAllByCodContrato(Integer codContrato, PaginadorDTO paginadorDTO) throws ServiceException;

    List<MovPagamentoDTO> findAllByCodRecibo(Integer codRecibo) throws ServiceException;

    void save(MovPagamento movPagamento) throws Exception;

    List<LogConciliadoraDTO> obterLogConciliadora(Integer codMovPagamento) throws ServiceException;

    MovPagamentoTotaisDTO pagamentosComDataBaseAlterada(FiltroBIControleOperacoesJSON filtroBIControleOperacoesJSON, PaginadorDTO paginadorDTO) throws ServiceException;
}
