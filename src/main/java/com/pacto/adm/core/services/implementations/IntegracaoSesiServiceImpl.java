package com.pacto.adm.core.services.implementations;

import com.pacto.adm.core.adapters.IntegracaoSesiAdapter;
import com.pacto.adm.core.dao.interfaces.IntegracaoSesiDao;
import com.pacto.adm.core.dto.IntegracaoSesiDTO;
import com.pacto.adm.core.services.interfaces.DiscoveryService;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.adm.core.dto.filtros.FiltroIntegracaoSesiJSON;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.security.interfaces.RequestService;
import com.pacto.adm.core.services.interfaces.IntegracaoSesiService;
import com.pacto.config.utils.HttpServico;
import com.pacto.config.utils.Uteis;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class IntegracaoSesiServiceImpl implements IntegracaoSesiService {

    @Autowired
    private DiscoveryService discoveryService;
    private final IntegracaoSesiDao integracaoSesiDao;
    private final IntegracaoSesiAdapter integracaoSesiAdapter;
    private final HttpServico httpServico;
    private final RequestService requestService;

    public IntegracaoSesiServiceImpl(IntegracaoSesiDao integracaoSesiDao, IntegracaoSesiAdapter integracaoSesiAdapter, HttpServico httpServico, RequestService requestService) {
        this.integracaoSesiDao = integracaoSesiDao;
        this.integracaoSesiAdapter = integracaoSesiAdapter;
        this.httpServico = httpServico;
        this.requestService = requestService;
    }

    @Override
    public List<IntegracaoSesiDTO> findAll(FiltroIntegracaoSesiJSON filtros, PaginadorDTO paginadorDTO) throws Exception {
        validarFiltros(filtros);
        return integracaoSesiAdapter.toDtos(integracaoSesiDao.findAll(filtros, paginadorDTO));
    }

    private void validarFiltros(FiltroIntegracaoSesiJSON filtros) throws ServiceException {
        if (filtros.getDataInicial() == null) {
            throw new ServiceException("A Data inicial é obrigatória");
        }
        if (filtros.getDataFinal() == null) {
            throw new ServiceException("A Data final é obrigatória");
        }
        if (filtros.getDataInicial().after(filtros.getDataFinal())) {
            throw new ServiceException("A Data inicial não pode ser maior que a data final");
        }

        if (Uteis.diferencaEmDias(filtros.getDataInicial(), filtros.getDataFinal()) > 180) {
            throw new ServiceException("O Intervalo de dias entre a data inicial e final não pode ser maior que 6 meses(180 dias)");
        }
    }

    @Override
    public String reprocessarItem(FiltroIntegracaoSesiJSON filtros) throws ServiceException {
        try {
            JSONObject jsonFiltros = new JSONObject();
            jsonFiltros.put("codigoEntidade", filtros.getCodigo());
            ResponseEntity<String> resposta = httpServico.doJson(discoveryService.getClientDiscovery().getServiceUrls().getIntegracoesMsUrl() + "/integracao-sesi/integracaoReprocessar",
                    jsonFiltros.toString(),
                    HttpMethod.POST,
                    requestService.getUsuarioAtual().getChave());
            JSONObject jsonObject = new JSONObject(resposta.getBody());
            return jsonObject.toString();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

    @Override
    public String consultarItem(FiltroIntegracaoSesiJSON filtros) throws ServiceException {
        try {
            JSONObject jsonFiltros = new JSONObject();
            jsonFiltros.put("codigoEntidade", filtros.getCodigo());
            ResponseEntity<String> resposta = httpServico.doJson(discoveryService.getClientDiscovery().getServiceUrls().getIntegracoesMsUrl() + "/integracao-sesi/integracaoConsultarItem",
                    jsonFiltros.toString(),
                    HttpMethod.POST,
                    requestService.getUsuarioAtual().getChave());
            JSONObject jsonObject = new JSONObject(resposta.getBody());
            return jsonObject.toString();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }


}
