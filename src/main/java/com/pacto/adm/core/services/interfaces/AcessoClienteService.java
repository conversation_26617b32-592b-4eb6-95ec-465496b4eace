package com.pacto.adm.core.services.interfaces;

import com.pacto.adm.core.dto.AcessoClienteDTO;
import com.pacto.adm.core.dto.ClienteInfoAcessosDTO;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;

import java.time.LocalDate;
import java.util.List;

public interface AcessoClienteService {

    List<AcessoClienteDTO> findAllByCodPessoa(Integer codPessoa, PaginadorDTO paginadorDTO) throws ServiceException;

    ClienteInfoAcessosDTO obterInfo(Integer codPessoa) throws ServiceException;

    List<Integer> ultimosMeses(Integer codPessoa, String data) throws ServiceException;

    List<Integer> ultimasSemanas(Integer codPessoa, String data) throws ServiceException;

}
