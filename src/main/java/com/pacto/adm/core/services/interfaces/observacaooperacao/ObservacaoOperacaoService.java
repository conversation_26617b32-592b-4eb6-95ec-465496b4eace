package com.pacto.adm.core.services.interfaces.observacaooperacao;

import com.pacto.adm.core.dto.filtros.FiltroBIControleOperacoesJSON;
import com.pacto.adm.core.dto.observacaooperacao.ObservacaoOperacaoTotaisDTO;
import com.pacto.adm.core.enumerador.TipoObservacaoOperacaoEnum;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;

public interface ObservacaoOperacaoService {
    ObservacaoOperacaoTotaisDTO parcelasCanceladas(FiltroBIControleOperacoesJSON filtroBIControleOperacoesJSON, PaginadorDTO paginadorDTO, boolean buscarComAdministrador) throws ServiceException;
}
