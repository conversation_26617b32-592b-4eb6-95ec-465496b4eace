package com.pacto.adm.core.services.interfaces;
import com.pacto.adm.core.dto.integracoes.ConfiguracaoEmpresaRDStationDTO;
import com.pacto.config.exceptions.ServiceException;


public interface ConfiguracaoEmpresaRDStationService {

    ConfiguracaoEmpresaRDStationDTO findByEmpresaId(Integer codigoEmpresa) throws ServiceException;

    String getUrlApiLeadOldRD(Integer empresaId) throws ServiceException;

    ConfiguracaoEmpresaRDStationDTO salvarConfiguracaoEmpresaRDStation(ConfiguracaoEmpresaRDStationDTO configuracaoEmpresaRDStationDTO) throws ServiceException;

    ConfiguracaoEmpresaRDStationDTO aprovar(ConfiguracaoEmpresaRDStationDTO configuracaoEmpresaRDStationDTO) throws ServiceException;
}
