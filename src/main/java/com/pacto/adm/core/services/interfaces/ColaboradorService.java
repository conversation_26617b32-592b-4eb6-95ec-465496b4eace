package com.pacto.adm.core.services.interfaces;

import com.pacto.adm.core.dto.colaborador.ColaboradorDTO;
import com.pacto.adm.core.dto.filtros.FiltroColaboradorJSON;
import com.pacto.adm.core.entities.Colaborador;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;

import java.util.List;

public interface ColaboradorService {

    List<Colaborador> findAllColaboradoresAtivos() throws ServiceException;

    List<ColaboradorDTO> findAllConsultoresAtivos() throws ServiceException;

    List<ColaboradorDTO> findAllProfessoresAtivos() throws ServiceException;

    ColaboradorDTO findByCodigo(Integer codigo) throws ServiceException;

    List<String> tiposColaborador(Integer codigo, Integer usuario) throws ServiceException;

    List<ColaboradorDTO> findAllAtivosPorTipoVinculo(String tipoVinculo, boolean unpage, FiltroColaboradorJSON filtroColaboradorJSON, PaginadorDTO paginadorDTO) throws ServiceException;
}
