package com.pacto.adm.core.services.implementations;

import com.pacto.adm.core.adapters.ClienteRestricaoAdapter;
import com.pacto.adm.core.dao.interfaces.ClienteDao;
import com.pacto.adm.core.dao.interfaces.ClienteRestricaoDao;
import com.pacto.adm.core.dto.ClienteRestricaoDTO;
import com.pacto.adm.core.dto.base.ClientDiscoveryDataDTO;
import com.pacto.adm.core.dto.base.RedeEmpresaDTO;
import com.pacto.adm.core.dto.filtros.FiltroClienteRestricaoJSON;
import com.pacto.adm.core.entities.Cliente;
import com.pacto.adm.core.entities.ClienteRestricao;
import com.pacto.adm.core.enumerador.clienterestricao.TipoClienteRestricaoEnum;
import com.pacto.adm.core.services.interfaces.ClienteRestricaoService;
import com.pacto.adm.core.services.interfaces.ClienteService;
import com.pacto.adm.core.services.interfaces.DiscoveryService;
import com.pacto.adm.core.services.interfaces.RedeEmpresaService;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.dto.UsuarioSimplesDTO;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.security.interfaces.RequestService;
import com.pacto.config.utils.HttpServico;
import com.pacto.config.utils.JSONMapper;
import com.pacto.config.utils.Uteis;
import com.pacto.config.utils.UteisValidacao;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Service
public class ClienteRestricaoServiceImpl implements ClienteRestricaoService {

    private final ClienteDao clienteDao;
    private final ClienteRestricaoDao clienteRestricaoDao;
    private final ClienteRestricaoAdapter clienteRestricaoAdapter;
    private final RequestService requestService;
    private final RedeEmpresaService redeEmpresaService;
    private final HttpServico httpServico;
    private final DiscoveryService discoveryService;
    private final ClienteService clienteService;

    private final String ENDPOINT_CLIENTE_RESTRICAO = "cliente-restricao";

    @Value("${secret.key.zw.path}")
    private String secretKeyZwPath;
    @Value("${discovery.url}")
    private String urlDiscovery;
    private final String HEADER_TOKEN_ZW = "mD0dL5oG5xI6pR8b";

    public ClienteRestricaoServiceImpl(ClienteDao clienteDao, ClienteRestricaoDao clienteRestricaoDao, ClienteRestricaoAdapter clienteRestricaoAdapter, RequestService requestService, RedeEmpresaService redeEmpresaService, HttpServico httpServico, DiscoveryService discoveryService, ClienteService clienteService) {
        this.clienteDao = clienteDao;
        this.clienteRestricaoDao = clienteRestricaoDao;
        this.clienteRestricaoAdapter = clienteRestricaoAdapter;
        this.requestService = requestService;
        this.redeEmpresaService = redeEmpresaService;
        this.httpServico = httpServico;
        this.discoveryService = discoveryService;
        this.clienteService = clienteService;
    }

    @Override
    public List<ClienteRestricaoDTO> findAll(FiltroClienteRestricaoJSON filtrosJSON, PaginadorDTO paginadorDTO) throws ServiceException {
        return clienteRestricaoAdapter.toDtos(clienteRestricaoDao.findAll(filtrosJSON, paginadorDTO));
    }

    @Override
    public List<ClienteRestricaoDTO> findByCpf(String cpf) throws Exception {
        cpf = Uteis.removerMascara(cpf);
        if (UteisValidacao.emptyString(cpf)) {
            throw new ServiceException("CPF não informado");
        }

        if (naoPossuiRedeEmpresaOuEstaNaChaveFranqueadora()) {
            return clienteRestricaoAdapter.toDtos(clienteRestricaoDao.findByCpf(cpf));
        } else {
            return findByCpfNaFranqueadora(cpf);
        }
    }

    private List<ClienteRestricaoDTO> findByCpfNaFranqueadora(String cpf) throws Exception {
        ResponseEntity<String> response = sendRequestAdmCoreFranqueadora(String.format("%s/%s", ENDPOINT_CLIENTE_RESTRICAO, cpf), HttpMethod.GET, null);
        if (response.getStatusCode().is2xxSuccessful()) {
            return JSONMapper.getList(new JSONObject(response.getBody()).getJSONArray("content"), ClienteRestricaoDTO.class);
        } else {
            throw new ServiceException("Falha ao consultar cliente restricao na chave franqueadora:" + response.getBody());
        }
    }

    @Override
    public List<ClienteRestricaoDTO> findByCodigoMatricula(Integer codigoMatricula) throws Exception {
        if (UteisValidacao.emptyNumber(codigoMatricula)) {
            throw new ServiceException("Código da matrícula não informado");
        }

        Cliente cliente = clienteDao.findByMatricula(codigoMatricula);
        if (cliente == null) {
            throw new ServiceException("Cliente não encontrado");
        }

        if (UteisValidacao.emptyString(cliente.getPessoa().getCpf())) {
            return new ArrayList<>();
        }
        String cpf = Uteis.removerMascara(cliente.getPessoa().getCpf());

        if (naoPossuiRedeEmpresaOuEstaNaChaveFranqueadora()) {
            return clienteRestricaoAdapter.toDtos(clienteRestricaoDao.findByCpf(cpf));
        } else {
            return findByCpfNaFranqueadora(cpf);
        }
    }

    @Override
    public void create(ClienteRestricaoDTO clienteRestricaoDTO) throws Exception {
        validacoes(clienteRestricaoDTO);

        if (naoPossuiRedeEmpresaOuEstaNaChaveFranqueadora()) {
            if (restricaoJaExiste(clienteRestricaoDTO)) {
                return;
            }
            clienteRestricaoDao.save(clienteRestricaoAdapter.toEntity(clienteRestricaoDTO));
        } else {
            criarNaFranqueadora(clienteRestricaoDTO);
        }

    }

    private void criarNaFranqueadora(ClienteRestricaoDTO clienteRestricaoDTO) throws Exception {
        ResponseEntity<String> response = sendRequestAdmCoreFranqueadora(ENDPOINT_CLIENTE_RESTRICAO, HttpMethod.POST, new JSONObject(clienteRestricaoDTO).toString());
        if (!response.getStatusCode().is2xxSuccessful()) {
            throw new ServiceException(response.getBody());
        }
    }

    private boolean restricaoJaExiste(ClienteRestricaoDTO clienteRestricaoDTO) {
        List<ClienteRestricao> restricaoJaExiste = clienteRestricaoDao.findByCpfAndChaveAndCodigoEmpresaAndTipo(clienteRestricaoDTO.getCpf(), clienteRestricaoDTO.getChaveEmpresa(), clienteRestricaoDTO.getCodigoEmpresa(), clienteRestricaoDTO.getTipo());
        return !UteisValidacao.emptyList(restricaoJaExiste);
    }

    @Override
    public void delete(String cpf, String chaveEmpresa, Integer codigoEmpresa, String tipoRestricao) throws Exception {
        if (UteisValidacao.emptyString(cpf)) {
            throw new ServiceException("CPF é obrigatório");
        }
        if (UteisValidacao.emptyString(chaveEmpresa)) {
            throw new ServiceException("Chave da empresa é obrigatória");
        }
        if (UteisValidacao.emptyNumber(codigoEmpresa)) {
            throw new ServiceException("Código da empresa é obrigatório");
        }
        if (UteisValidacao.emptyString(tipoRestricao)) {
            throw new ServiceException("Tipo de Restrição é obrigatório");
        }

        cpf = Uteis.removerMascara(cpf);

        if (naoPossuiRedeEmpresaOuEstaNaChaveFranqueadora()) {
            List<ClienteRestricao> clientesRestricoes = clienteRestricaoDao.findByCpfAndChaveAndCodigoEmpresaAndTipo(cpf, chaveEmpresa, codigoEmpresa, tipoRestricao);
            for (ClienteRestricao clienteRestricao : clientesRestricoes) {
                clienteRestricaoDao.delete(clienteRestricao.getCodigo());
            }
        } else {
            deletarNaFraqueadora(cpf, chaveEmpresa, codigoEmpresa, tipoRestricao);
        }
    }

    public void atualizarRestricoesDeInadimplenciaSync(String cpf) throws Exception {
        String cpfSemMascara = Uteis.removerMascara(cpf);
        if (UteisValidacao.emptyString(cpfSemMascara)) {
            throw new ServiceException("CPF é obrigatório");
        }

        if (naoPossuiRedeEmpresaOuEstaNaChaveFranqueadora()) {
            List<ClienteRestricao> clienteRestricoes = clienteRestricaoDao.findByCpfAndTipo(cpfSemMascara, TipoClienteRestricaoEnum.INADINPLENCIA.getSigla());
            if (UteisValidacao.emptyList(clienteRestricoes)) {
                return;
            }

            for (ClienteRestricao clienteRestricao : clienteRestricoes) {
                try {
                    System.out.printf("Verificando restrição inadimplencia na unidade: %s \n", clienteRestricao.getNomeEmpresa());
                    if (!clienteAindaEstaInadimplente(clienteRestricao)) {
                        System.out.print("\tRestrição removida!\n");
                        clienteRestricaoDao.delete(clienteRestricao.getCodigo());
                    }
                } catch (Exception e) {
                    Uteis.logar(e, ClienteRestricaoServiceImpl.class);
                }
            }
        } else {
            ResponseEntity<String> response = sendRequestAdmCoreFranqueadora(String.format("%s/%s/atualizar", ENDPOINT_CLIENTE_RESTRICAO, cpf), HttpMethod.POST, null);
            if (!response.getStatusCode().is2xxSuccessful()) {
                throw new ServiceException(response.getBody());
            }
        }
    }

    private boolean clienteAindaEstaInadimplente(ClienteRestricao clienteRestricao) throws Exception {
        if (clienteRestricao.getChaveEmpresa().equals(requestService.getUsuarioAtual().getChave())) {
            return clienteDao.clienteEstaInadimplente(clienteRestricao.getCodigoMatricula(), clienteRestricao.getCodigoEmpresa());
        }

        HttpHeaders httpHeaders = getHttpHeaders(clienteRestricao.getChaveEmpresa(), clienteRestricao.getCodigoEmpresa());
        String token = Objects.requireNonNull(httpHeaders.get(HEADER_TOKEN_ZW)).get(0);
        ClientDiscoveryDataDTO clientDiscoveryDataDTO = discoveryService.getClientDiscovery(clienteRestricao.getChaveEmpresa(), token);

        if (clientDiscoveryDataDTO == null || clientDiscoveryDataDTO.getServiceUrls() == null
                || UteisValidacao.emptyString(clientDiscoveryDataDTO.getServiceUrls().getAdmCoreUrl())) {
            throw new Exception("Falha ao tentar verificar cliente inadimplente! Não foi possivel localizar as configurações da chave: " + clienteRestricao.getChaveEmpresa());
        }

        String url = String.format("%s/clientes/inadimplente/%d/%d",
                clientDiscoveryDataDTO.getServiceUrls().getAdmCoreUrl(),
                clienteRestricao.getCodigoMatricula(),
                clienteRestricao.getCodigoEmpresa());

        ResponseEntity<String> response = httpServico.doJson(url, null, HttpMethod.GET, httpHeaders);
        if (response.getStatusCode().is2xxSuccessful()) {
            return new JSONObject(response.getBody()).optBoolean("content");
        } else {
            throw new Exception("Falha ao consultar cliente restricao na chave franqueadora:" + response.getBody());
        }
    }

    private void deletarNaFraqueadora(String cpf, String chaveEmpresa, Integer codigoEmpresa, String tipoRestricao) throws Exception {
        String endPoint = String.format("%s/%s/%s/%d/%s", ENDPOINT_CLIENTE_RESTRICAO, cpf, chaveEmpresa, codigoEmpresa, tipoRestricao);
        ResponseEntity<String> response = sendRequestAdmCoreFranqueadora(endPoint, HttpMethod.DELETE, null);
        if (!response.getStatusCode().is2xxSuccessful()) {
            throw new ServiceException(response.getBody());
        }
    }

    @Override
    public void delete(List<String> cpfs, String chaveEmpresa, Integer codigoEmpresa, String tipoRestricao) throws Exception {
        if (UteisValidacao.emptyString(chaveEmpresa)) {
            throw new ServiceException("Chave da empresa é obrigatória");
        }

        if (UteisValidacao.emptyNumber(codigoEmpresa)) {
            throw new ServiceException("Código da empresa é obrigatório");
        }

        if (UteisValidacao.emptyString(tipoRestricao)) {
            throw new ServiceException("Tipo de Restrição é obrigatório");
        }

        for (String cpf : cpfs) {
            cpf = Uteis.removerMascara(cpf);
            List<ClienteRestricao> clientesRestricoes = clienteRestricaoDao.findByCpfAndChaveAndCodigoEmpresaAndTipo(cpf, chaveEmpresa, codigoEmpresa, tipoRestricao);
            for (ClienteRestricao clienteRestricao : clientesRestricoes) {
                clienteRestricaoDao.delete(clienteRestricao.getCodigo());
            }
        }
    }

    private void validarPossiveisInconsistenciasConfigRedeEmpresa(RedeEmpresaDTO redeEmpresaDTO) throws ServiceException {
        if (UteisValidacao.emptyNumber(redeEmpresaDTO.getCodigoUnidadeFranqueadora())) {
            String msg = String.format("A rede empresa %s não possui código da unidade franqueadora configurada", redeEmpresaDTO.getNome());
            Uteis.logar(msg);
            throw new ServiceException(msg);
        }
        if (UteisValidacao.emptyString(redeEmpresaDTO.getServiceMap().getAdmCoreUrl())) {
            String msg = String.format("A rede empresa %s não possui AdmCoreUrl!", redeEmpresaDTO.getNome());
            Uteis.logar(msg);
            throw new ServiceException(msg);
        }
        RedeEmpresaDTO redeEmpresaFranqueadora = redeEmpresaService.obterRedeEmpresaPorChaveEmpresa(redeEmpresaDTO.getChaveFranqueadora());
        if (redeEmpresaFranqueadora == null || UteisValidacao.emptyNumber(redeEmpresaFranqueadora.getId())) {
            String msg = String.format("A chave franqueadora(%s) configurada na rede empresa %s não faz parte dessa rede", redeEmpresaDTO.getChaveFranqueadora(), redeEmpresaDTO.getNome());
            Uteis.logar(msg);
            throw new ServiceException(msg);
        }
        if (!redeEmpresaFranqueadora.getChaveFranqueadora().equals(redeEmpresaDTO.getChaveFranqueadora())) {
            String msg = String.format("A rede empresa %s está configurada com uma chave franqueadora que está em outra rede empresa %s",
                    redeEmpresaDTO.getNome(), redeEmpresaDTO.getChaveFranqueadora());
            Uteis.logar(msg);
            throw new ServiceException(msg);
        }
    }

    private void validacoes(ClienteRestricaoDTO clienteRestricaoDTO) throws ServiceException {
        if (UteisValidacao.emptyString(clienteRestricaoDTO.getCpf())) {
            throw new ServiceException("O campo CPF é obrigatório");
        }
        clienteRestricaoDTO.setCpf(Uteis.removerMascara(clienteRestricaoDTO.getCpf()));
        if (clienteRestricaoDTO.getCpf().length() != 11) {
            throw new ServiceException("O campo CPF deve conter 11 caracteres");
        }
        if (UteisValidacao.emptyNumber(clienteRestricaoDTO.getCodigoMatricula())) {
            throw new ServiceException("O campo matricula é obrigatório");
        }
        if (UteisValidacao.emptyString(clienteRestricaoDTO.getNome())) {
            throw new ServiceException("O campo Nome é obrigatório");
        }
        if (UteisValidacao.emptyString(clienteRestricaoDTO.getChaveEmpresa())) {
            throw new ServiceException("O campo Chave da empresa de origem é obrigatório");
        }
        if (UteisValidacao.emptyString(clienteRestricaoDTO.getNomeEmpresa())) {
            throw new ServiceException("O campo Nome da empresa de origem é obrigatório");
        }
        if (UteisValidacao.emptyString(clienteRestricaoDTO.getTipo())) {
            throw new ServiceException("O campo Tipo de Restrição é obrigatório");
        }
        TipoClienteRestricaoEnum tipoClienteRestricaoEnum = TipoClienteRestricaoEnum.obterPorSigla(clienteRestricaoDTO.getTipo());
        if (tipoClienteRestricaoEnum == null) {
            throw new ServiceException("Tipo de Restrição inválido");
        }
    }

    protected String getTokenZW(final String key, Integer idEmpresa) {
        UsuarioSimplesDTO usuarioSimplesDTO = new UsuarioSimplesDTO();
        usuarioSimplesDTO.setChave(key);
        if (!UteisValidacao.emptyNumber(idEmpresa)) {
            usuarioSimplesDTO.setIdEmpresa(idEmpresa);
        }
        usuarioSimplesDTO.setAdministrador(true);
        usuarioSimplesDTO.setCodZw(1);
        usuarioSimplesDTO.setUsername("admin");
        String json = new JSONObject(usuarioSimplesDTO).toString();
        return Uteis.encriptar(json, Uteis.readLineByLineJava8(secretKeyZwPath));
    }

    private boolean naoPossuiRedeEmpresaOuEstaNaChaveFranqueadora() throws ServiceException {
        String chave = requestService.getUsuarioAtual().getChave();
        RedeEmpresaDTO redeEmpresaDTO = redeEmpresaService.obterRedeEmpresaPorChaveEmpresa(requestService.getUsuarioAtual().getChave());
        return redeEmpresaDTO == null
                || UteisValidacao.emptyString(redeEmpresaDTO.getChaveFranqueadora())
                || chave.equals(redeEmpresaDTO.getChaveFranqueadora());
    }

    private ResponseEntity<String> sendRequestAdmCoreFranqueadora(String endPoint, HttpMethod httpMethod, String body) throws Exception {
        RedeEmpresaDTO redeEmpresaDTO = redeEmpresaService.obterRedeEmpresaPorChaveEmpresa(requestService.getUsuarioAtual().getChave());
        validarPossiveisInconsistenciasConfigRedeEmpresa(redeEmpresaDTO);

        String urlAdmCoreMsFranqueadora = String.format("%s/%s", redeEmpresaDTO.getServiceMap().getAdmCoreUrl(), endPoint);
        HttpHeaders httpHeaders = getHttpHeaders(redeEmpresaDTO.getChaveFranqueadora(), redeEmpresaDTO.getCodigoUnidadeFranqueadora());

        return httpServico.doJson(urlAdmCoreMsFranqueadora, body, httpMethod, httpHeaders);
    }

    private HttpHeaders getHttpHeaders(String chave, Integer codigoEmpresa) {
        String token = getTokenZW(chave, codigoEmpresa);
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.set(HEADER_TOKEN_ZW, token);
        httpHeaders.set("empresaId", codigoEmpresa.toString());
        httpHeaders.set("Content-Type", "application/json");
        return httpHeaders;
    }
}
