package com.pacto.adm.core.services.implementations;

import com.pacto.adm.core.adapters.AfastamentoContratoDependenteAdapter;
import com.pacto.adm.core.adapters.ContratoAdapter;
import com.pacto.adm.core.adapters.ContratoComAutorizacaoAdapter;
import com.pacto.adm.core.adapters.ContratoComDataBaseAlteradaAdapter;
import com.pacto.adm.core.adapters.ContratoDependenteAdapter;
import com.pacto.adm.core.dao.interfaces.AfastamentoContratoDependenteDao;
import com.pacto.adm.core.dao.interfaces.ContratoDao;
import com.pacto.adm.core.dao.interfaces.ContratoDependenteDao;
import com.pacto.adm.core.dao.interfaces.ContratoOperacaoDao;
import com.pacto.adm.core.dao.interfaces.nativerepositories.contrato.ContratoNativeRepository;
import com.pacto.adm.core.dto.AfastamentoContratoDependenteDTO;
import com.pacto.adm.core.dto.ClienteDTO;
import com.pacto.adm.core.dto.ConfiguracaoSistemaDTO;
import com.pacto.adm.core.dto.ContratoBolsaDTO;
import com.pacto.adm.core.dto.ContratoDTO;
import com.pacto.adm.core.dto.ContratoDependenteDTO;
import com.pacto.adm.core.dto.base.ClientDiscoveryDataDTO;
import com.pacto.adm.core.dto.filtros.FiltroBIControleOperacoesJSON;
import com.pacto.adm.core.dto.filtros.FiltroContratoAutorizacaoJSON;
import com.pacto.adm.core.entities.contrato.AfastamentoContratoDependente;
import com.pacto.adm.core.entities.contrato.ContratoDependente;
import com.pacto.adm.core.enumerador.OrigemSistemaEnum;
import com.pacto.adm.core.services.interfaces.DiscoveryService;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.adm.core.dto.filtros.FiltroContratoJSON;
import com.pacto.adm.core.entities.contrato.Contrato;
import com.pacto.adm.core.entities.contrato.ContratoOperacao;
import com.pacto.adm.core.enumerador.SituacaoClienteEnum;
import com.pacto.adm.core.enumerador.SituacaoDoContratoEnum;
import com.pacto.adm.core.enumerador.TipoOperacaoContratoEnum;
import com.pacto.adm.core.services.interfaces.ClienteService;
import com.pacto.adm.core.services.interfaces.ConfiguracaoSistemaService;
import com.pacto.adm.core.services.interfaces.ContratoOperacaoService;
import com.pacto.adm.core.services.interfaces.ContratoService;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.security.interfaces.RequestService;
import com.pacto.config.utils.Calendario;
import com.pacto.config.utils.HttpServico;
import com.pacto.config.utils.Uteis;
import org.hibernate.engine.spi.SessionImplementor;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class ContratoServiceImpl implements ContratoService {

    @Autowired
    private ContratoDao contratoDao;
    @Autowired
    private ContratoAdapter contratoAdapter;
    @Autowired
    private ContratoOperacaoDao contratoOperacaoDao;
    @Autowired
    private ContratoOperacaoService contratoOperacaoService;
    @Autowired
    private ClienteService clienteService;
    @Autowired
    private ConfiguracaoSistemaService configuracaoSistemaService;
    @Autowired
    private RequestService requestService;
    @Autowired
    private ContratoDependenteAdapter contratoDependenteAdapter;
    @Autowired
    private ContratoDependenteDao contratoDependenteDao;
    @Autowired
    private AfastamentoContratoDependenteAdapter afastamentoContratoDependenteAdapter;
    @Autowired
    private AfastamentoContratoDependenteDao afastamentoContratoDependenteDao;
    @Autowired
    private HttpServico httpServico;
    @Autowired
    private DiscoveryService discoveryService;
    @Autowired
    private ContratoNativeRepository contratoNativeRepository;
    @Autowired
    private ContratoComDataBaseAlteradaAdapter contratoComDataBaseAlteradaAdapter;
    @Autowired
    private ContratoComAutorizacaoAdapter contratoComAutorizacaoAdapter;

    public List<ContratoDTO> findAllByCodPessoa(Integer codPessoa, PaginadorDTO paginadorDTO) throws ServiceException {
        try {
            List<Contrato> contratos = contratoDao.findAllByPessoa(codPessoa, paginadorDTO);
            return contratoAdapter.toDtos(contratos);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public ContratoDTO findById(Integer codContrato) throws ServiceException {
        try {
            // Necessário para atualizar o cache do hibernate, já que há alterações realizadas no zw.
            contratoDao.getCurrentSession().clear();
            ContratoDTO contratoDTO = contratoAdapter.toDto(contratoDao.findById(codContrato));
            if (contratoDTO != null) {
                Double valorPago = consultarValorPagoPorContrato(codContrato);
                String descricaoHorario = consultaDescricaoHorario(codContrato);
                String descricaoCondicaoPagamento = consultaCondicaoPagamento(codContrato);
                contratoDTO.setValorPago(valorPago);
                contratoDTO.setDescricaoHorario(descricaoHorario);
                contratoDTO.setCondicaoDePagamento(descricaoCondicaoPagamento);
            }
            return contratoDTO;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<ContratoDTO> findAllByCodMatricula(Integer codMatricula, FiltroContratoJSON filtroContratoJSON, PaginadorDTO paginadorDTO) throws ServiceException {
        try {
            List<Contrato> contratos = contratoDao.findAllByCodMatricula(codMatricula, filtroContratoJSON, paginadorDTO);
            List<ContratoDTO> dtos = contratoAdapter.toDtos(contratos);

            ClienteDTO cliente = clienteService.findByMatricula(codMatricula);
            verificarSituacaoContrato(cliente, dtos);
            for (ContratoDTO contrato : dtos) {
                if (contrato.getSituacao().equals(SituacaoDoContratoEnum.INATIVO.getCodigo())
                        && contrato.getDataRenovarRealizada() != null) {
                    contrato.setSituacao("RN");
                } else if (contrato.getSituacao().equals(SituacaoDoContratoEnum.INATIVO.getCodigo())) {
                    contrato.setSituacao("IN");
                }
                // apresentar botão renovar contrato apenas para usuário logado
                // na empresa do respectivo contrato
                if (!requestService.getEmpresaId().equals(contrato.getEmpresa().getCodigo())) {
                    contrato.setRenovarContrato(false);
                    contrato.setRematricularContrato(false);
                }
            }
            return dtos;
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new ServiceException(ex);
        }
    }

    private void verificarSituacaoContrato(ClienteDTO cliente, List<ContratoDTO> dtos) throws Exception {
        Integer nrDiasVencido = 0;
        if (cliente.getEmpresa().getCarenciaRenovacao() != null && !cliente.getEmpresa().getCarenciaRenovacao().equals(0)) {
            nrDiasVencido = cliente.getEmpresa().getCarenciaRenovacao();
        } else {
            ConfiguracaoSistemaDTO configuracaoSistemaDTO = configuracaoSistemaService.get();
            nrDiasVencido = configuracaoSistemaDTO.getCarenciaRenovacao();
        }

        if (cliente.getSituacao().equals(SituacaoClienteEnum.ATIVO.getCodigo())) {
            for (ContratoDTO contrato : dtos) {
                Date dataAtual = Calendario.hoje();
                Date dataVigenciaAjustada = Uteis.obterDataFutura2(contrato.getVigenciaAteAjustada(), 1);
                Date dataVigenciaRenovacao = Uteis.obterDataFutura2(contrato.getVigenciaAteAjustada(), (nrDiasVencido + 1));
                if (contrato.getNaoPermitirRenovacaoRematriculaDeContratoAnteriores()) {
                    contrato.setRenovarContrato(false);
                    contrato.setRematricularContrato(false);
                } else if ((contrato.getContratoResponsavelRenovacaoMatricula() != 0) || (contrato.getContratoResponsavelRematriculaMatricula() != 0)) {
                    contrato.setRenovarContrato(false);
                    contrato.setRematricularContrato(false);
                } else if (contratoOperacaoService.existeOperacaoParaEsteContrato(contrato.getCodigo(), TipoOperacaoContratoEnum.CANCELAMENTO.getSigla())) {
                    contrato.setRenovarContrato(false);
                    contrato.setRematricularContrato(true);
                } else if (contratoOperacaoService.validarSeExisteTrancamentoSemRetorno(contrato.getCodigo(), false)) {
                    contrato.setRenovarContrato(false);
                    contrato.setRematricularContrato(false);
                } else if (dataAtual.before(dataVigenciaAjustada)) {
                    contrato.setRenovarContrato(true);
                    contrato.setRematricularContrato(false);
                } else if (dataAtual.before(dataVigenciaRenovacao)) {
                    contrato.setRenovarContrato(true);
                    contrato.setRematricularContrato(false);
                } else if (contrato.getSituacao().equals("IN")) {
                    contrato.setRenovarContrato(false);
                    contrato.setRematricularContrato(true);
                } else {
                    contrato.setRenovarContrato(false);
                    contrato.setRematricularContrato(false);
                }
            }
        } else if (cliente.getSituacao().equals(SituacaoClienteEnum.INATIVO.getCodigo())) {
            for (ContratoDTO contrato : dtos) {
                String situacaoClienteInativo = clienteService.obterSituacaoClienteInativo(contrato, cliente);
                if (contrato.getNaoPermitirRenovacaoRematriculaDeContratoAnteriores()) {
                    contrato.setRenovarContrato(false);
                    contrato.setRematricularContrato(false);
                } else if ((contrato.getContratoResponsavelRenovacaoMatricula() != 0) || (contrato.getContratoResponsavelRematriculaMatricula() != 0)) {
                    contrato.setRenovarContrato(false);
                    contrato.setRematricularContrato(false);
                } else if (contrato.getSituacao().equals("CA")) {
                    contrato.setRenovarContrato(false);
                    contrato.setRematricularContrato(true);
                } else if (situacaoClienteInativo.equals("VE")
                        || (contrato.getSituacao().equals("AT") && Calendario.maiorOuIgual(contrato.getVigenciaAteAjustada(), Calendario.hoje()))) { // contratos rematriculados futuramente
                    contrato.setRenovarContrato(true);
                    contrato.setRematricularContrato(false);
                } else if (situacaoClienteInativo.equals("DE")) {
                    contrato.setRenovarContrato(false);
                    contrato.setRematricularContrato(true);
                } else {
                    contrato.setRenovarContrato(false);
                    contrato.setRematricularContrato(false);
                }
            }
        } else {
            for (ContratoDTO contrato : dtos) {
                if (contrato.getNaoPermitirRenovacaoRematriculaDeContratoAnteriores() || (contrato.getContratoResponsavelRenovacaoMatricula() != 0) || (contrato.getContratoResponsavelRematriculaMatricula() != 0)) {
                    contrato.setRenovarContrato(false);
                    contrato.setRematricularContrato(false);
                } else if (contrato.getSituacao().equals("AT") && Calendario.maiorOuIgual(contrato.getVigenciaAteAjustada(), Calendario.hoje())) {
                    contrato.setRenovarContrato(true);
                    contrato.setRematricularContrato(false);
                }
            }
        }
    }

    @Override
    public Integer consultarQuantidadeContratosAssinados(Integer codMatricula) throws Exception {
        Integer qtdContratosAssinados = contratoDao.consultarQuantidadeContratosAssinados(codMatricula);
        return qtdContratosAssinados;
    }

    @Override
    public void alterarDatasVigenciaContrato(Contrato contrato) throws Exception {
        Contrato contratoAlterar = contratoDao.findById(contrato.getCodigo());
        contratoAlterar.setVigenciaDe(contrato.getVigenciaDe());
        contratoAlterar.setVigenciaAte(contrato.getVigenciaAte());
        contratoAlterar.setVigenciaAteAjustada(contrato.getVigenciaAteAjustada());
        contratoAlterar.setDataPrevistaRenovar(contrato.getDataPrevistaRenovar());
        contratoAlterar.setDataPrevistaRematricula(contrato.getDataPrevistaRematricula());
        contratoDao.update(contratoAlterar);
    }

    @Override
    public void alterarSituacaoContrato(Contrato contrato, Date data) throws Exception {
        try {
            contrato.setDataPrevistaRenovar(data);
            contrato.setDataPrevistaRematricula(data);
            contrato.setVigenciaAteAjustada(data);
            contratoDao.update(contrato);
        } catch (Exception e) {
            throw e;
        }
    }

    @Override
    public Integer obterNrDiasContrato(Contrato contrato) throws Exception {
        long nrDias = 0;
        if (contrato.isVendaCreditoTreino()) { //avalia dias de bônus pois o valor está no crédito, não no dia.
            nrDias = Uteis.nrDiasEntreDatas(contrato.getVigenciaDe(), contrato.getVigenciaAteAjustada()) + 1;
            nrDias -= obterNrDiasOperacoesCarenciaFeriasAtestadoTrancamentoNoContratoParaDias(contrato.clone(), null);
        } else {
            nrDias = calcularDiasContrato(contrato.clone(), null);
        }
        return new Long(nrDias).intValue();
    }

    public long obterNrDiasOperacoesCarenciaFeriasAtestadoTrancamentoNoContratoParaDias(Contrato contrato, Date dataEspecifica) throws Exception {
        long nrDiasOperacoesAux = 0;
        List<ContratoOperacao> listaOperacoesContrato = contratoOperacaoDao.findAllByContrato(contrato.getCodigo());
        Iterator<ContratoOperacao> i = listaOperacoesContrato.iterator();
        while (i.hasNext()) {
            ContratoOperacao contratoOperacao = i.next();
            if ((contratoOperacao.getCarenciaFerias()) || (contratoOperacao.getTrancamento()) || (contratoOperacao.getAtestado()) || contratoOperacao.getAfastamentoColetivo()) {
                // valido se o cliente usou essa operação
                if (dataEspecifica == null || Calendario.maiorOuIgual(dataEspecifica, contratoOperacao.getDataInicioEfetivacaoOperacao())) {
                    // conto os dias total da operacao
                    if (dataEspecifica == null || Calendario.maiorOuIgual(dataEspecifica, contratoOperacao.getDataFimEfetivacaoOperacao())) {
                        nrDiasOperacoesAux += contratoOperacaoService.obterNrDiasContratoOperacao(contratoOperacao);
                    } // senao conto somento os dias que ele usou dessa operação
                    else {
                        Long nrDias = Uteis.nrDiasEntreDatas(contratoOperacao.getDataInicioEfetivacaoOperacao(), dataEspecifica) + 1;
                        nrDiasOperacoesAux += nrDias.intValue();
                    }
                }
            }
        }
        return nrDiasOperacoesAux;
    }

    @Override
    public Integer obterNrDiasUtilizadoAcademiaAteDataEspecifica(Contrato contrato, Date dataEspecifica) throws Exception {
        if (Uteis.getCompareData(dataEspecifica, contrato.getVigenciaDe()) > 0) {
            long nrDias = calcularDiasContrato(contrato, dataEspecifica);
            return new Long(nrDias).intValue();
        }
        return 0;
    }

    @Override
    public Integer obterNrDiasRestantesProFinalDoContrato(Integer nrDiasContrato, Integer nrDiasUtilizadosPeloCliente) {
        return nrDiasContrato - nrDiasUtilizadosPeloCliente;
    }

    @Override
    public ContratoDTO findAllByCodigo(Integer contrato) throws Exception {
        return contratoAdapter.toDto( contratoDao.obterContratoAtual(contrato));
    }

    public long calcularDiasContrato(Contrato contrato, Date dataEspecifica) throws Exception {
        long nrDiasOperacoesBonusAcrescimo = 0;
        long nrDiasOperacoesBonusReducao = 0;
        List<Date> diasAfastamentoColetivoEmBonus = new ArrayList<Date>();
        List<Date> diasUteis = Uteis.getDiasEntreDatas(contrato.getVigenciaDe(), contrato.getVigenciaAte());
        List<Date> diasRetiradosBonusColetivo = new ArrayList<Date>();
        List<ContratoOperacao> listaOperacoesContrato = contratoOperacaoDao.findAllByContrato(contrato.getCodigo());
        Iterator<ContratoOperacao> i = listaOperacoesContrato.iterator();
        while (i.hasNext()) {
            ContratoOperacao obj = i.next();
            if ((obj.getBonusAcrescimo() && !contrato.isVendaCreditoTreino())) {
                nrDiasOperacoesBonusAcrescimo += obj.getNrDiasOperacao();
                continue;
            }
            if (obj.getBonusReducao()) {
                nrDiasOperacoesBonusReducao += obj.getNrDiasOperacao();
                continue;
            }
            List<Date> datasOperacao = Uteis.getDiasEntreDatas(obj.getDataInicioEfetivacaoOperacao(), obj.getDataFimEfetivacaoOperacao());
            if ((obj.getCarenciaFerias()) || (obj.getTrancamento()) || (obj.getAtestado()) || obj.getAfastamentoColetivo()) {
                for (Date dataOperacao : datasOperacao) {
                    if (diasUteis.contains(dataOperacao)) {
                        diasUteis.add(Uteis.somarDias(diasUteis.get(diasUteis.size() - 1), 1));
                        diasUteis.remove(dataOperacao);
                    } else if (obj.getAfastamentoColetivo() && dataEspecifica != null) {
                        diasAfastamentoColetivoEmBonus.add(dataOperacao);  //operacao foi dada dentro de periodo de bonus e precisa ser levada em consideração
                    }
                }
            } else if (obj.getTransfenciaEntrada()) {
                for (int day = 1; day <= obj.getNrDiasOperacao(); day++) {
                    diasUteis.add(Uteis.somarDias(diasUteis.get(diasUteis.size() - 1), 1));
                }
            }
        }
        long diasOperacoesBonus = nrDiasOperacoesBonusAcrescimo - nrDiasOperacoesBonusReducao;
        if (dataEspecifica != null && diasOperacoesBonus > 0) {
            Date dataInicioBonus = Uteis.somarDias(contrato.getVigenciaAteAjustada(), (int) -(diasOperacoesBonus - 1));
            if (Calendario.maiorOuIgual(dataEspecifica, dataInicioBonus)) {
                List<Date> listaDiasOperacao = Uteis.getDiasEntreDatas(dataInicioBonus, dataEspecifica);
                Date dataAdicionar = null;
                for (Date dataOperacao : listaDiasOperacao) {
                    if (!diasAfastamentoColetivoEmBonus.contains(dataOperacao)) {
                        diasUteis.add(Uteis.somarDias(diasUteis.get(diasUteis.size() - 1), 1));
                    } else {
                        if (dataAdicionar == null) {
                            dataAdicionar = dataOperacao;
                        }
                        while (diasAfastamentoColetivoEmBonus.contains(dataAdicionar)) {
                            dataAdicionar = Uteis.somarDias(dataAdicionar, 1);
                        }
                        diasUteis.add(dataAdicionar);
                        dataAdicionar = Uteis.somarDias(dataAdicionar, 1);
                    }
                }
            }
        }
        if (diasOperacoesBonus < 0) {
            for (int j = 0; j < Math.abs(diasOperacoesBonus) && !diasUteis.isEmpty(); j++) {
                diasUteis.remove(diasUteis.size() - 1);
            }
        }

        if (dataEspecifica != null) {
            while (!diasUteis.isEmpty() && Calendario.maior(diasUteis.get(diasUteis.size() - 1), dataEspecifica)) {
                diasUteis.remove(diasUteis.size() - 1);
            }
        }
        return diasUteis.size();
    }

    public String consultaCondicaoPagamento(Integer codigoContrato) throws Exception {
        List<String> descricao = new ArrayList<>();

        try (SessionImplementor sessionImplementor = contratoDao.createSessionCurrentWork()) {
            sessionImplementor.doWork(connection -> {
                String sql = "SELECT descricao FROM contratocondicaopagamento ccp JOIN condicaopagamento cp ON ccp.condicaopagamento = cp.codigo WHERE ccp.contrato = " + codigoContrato;
                try {
                    ResultSet rs = contratoDao.createStatement(connection, sql);
                    if (rs.next()) {
                        descricao.add(rs.getString("descricao"));
                    }
                } catch (Exception e) {
                    throw new RuntimeException(e);
                } finally {
                    connection.close();
                }

            });
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e);
        }
        if (!descricao.isEmpty()) {
            return descricao.get(0);
        }
        return "";
    }

    public Double consultarValorPagoPorContrato(Integer codigoContrato) throws Exception {
        List<Double> valorPago = new ArrayList<>();

        try (SessionImplementor sessionImplementor = contratoDao.createSessionCurrentWork()) {
            sessionImplementor.doWork(connection -> {
                String sql = geraSqlValorPagoContrato(codigoContrato);
                try {
                    ResultSet rs = contratoDao.createStatement(connection, sql);
                    if (rs.next()) {
                        valorPago.add(rs.getDouble("valorTotalPago"));
                    }
                } catch (Exception e) {
                    throw new RuntimeException(e);
                } finally {
                    connection.close();
                }
            });
        }

        if(!valorPago.isEmpty()) {
            return valorPago.get(0);
        }
        return 0.0;
    }

    private String geraSqlValorPagoContrato(Integer codigoContrato) {
        return "SELECT SUM(valorpago) as valorTotalPago\n" +
                "FROM pagamentomovparcela\n" +
                "WHERE codigo IN (SELECT DISTINCT (pmp.codigo)\n" +
                "                 FROM movprodutoparcela mpp\n" +
                "                          INNER JOIN movparcela mpar ON mpar.codigo = mpp.movparcela\n" +
                "                          INNER JOIN movproduto mprd ON mprd.codigo = mpp.movproduto\n" +
                "                          INNER JOIN produto prd ON prd.codigo = mprd.produto\n" +
                "                          INNER JOIN pagamentomovparcela pmp ON pmp.movparcela = mpar.codigo\n" +
                "                 WHERE prd.tipoproduto NOT IN ('TR', 'RT', 'TN', 'RD')\n" +
                "                   AND mprd.contrato = " + codigoContrato + ")";
    }

    private String consultaDescricaoHorario(Integer codContrato) throws ServiceException {
        List<String> descricao = new ArrayList<>();

        try (SessionImplementor sessionImplementor = contratoDao.createSessionCurrentWork()) {
            sessionImplementor.doWork(connection -> {
                String sql = "SELECT descricao FROM contratohorario ch join horario h ON ch.horario = h.codigo WHERE contrato = " + codContrato;
                try {
                    ResultSet rs = contratoDao.createStatement(connection, sql);
                    if (rs.next()) {
                        descricao.add(rs.getString("descricao"));
                    }
                } catch (Exception e) {
                    throw new RuntimeException(e);
                } finally {
                    connection.close();
                }

            });
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e);
        }
        if (!descricao.isEmpty()) {
            return descricao.get(0);
        }
        return "";
    }

    public List<ContratoDependenteDTO> findAllContratoDependente(Integer codMatricula, Integer codContrato, Boolean somenteComCliente, PaginadorDTO paginadorDTO) throws ServiceException {
        try {
            List<ContratoDependente> itens = contratoDependenteDao.findAll(codMatricula, codContrato, somenteComCliente, paginadorDTO);
            return contratoDependenteAdapter.toDtos(itens);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<AfastamentoContratoDependenteDTO> findAllAfastamentoContratoDependenteByContratoDependente(Integer codContratoDependente, PaginadorDTO paginadorDTO) throws ServiceException {
        try {
            List<AfastamentoContratoDependente> itens = afastamentoContratoDependenteDao.findAllByContratoDependente(codContratoDependente, paginadorDTO);
            return afastamentoContratoDependenteAdapter.toDtos(itens);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public List consultarNaoContratosAssinados(Integer codMatricula) throws Exception {
        List<Contrato> contratos =contratoDao.consultarNaoContratosAssinados(codMatricula);
        ClientDiscoveryDataDTO clientDiscoveryDataDTO = discoveryService.getClientDiscovery();
        String chave = requestService.getUsuarioAtual().getChave();
        String zwUrl = clientDiscoveryDataDTO.getServiceUrls().getZwUrl();
        for (Contrato contrato : contratos) {
            atualizarContratoZw(contrato.getPessoa().getCodigo(), contrato.getCodigo(), zwUrl, chave);
        }

        return contratos.stream().map(Contrato::getCodigo).collect(Collectors.toList());
    }

    private void atualizarContratoZw(Integer codigoPessoa,Integer contrato, String zwUrl, String chave) throws Exception {
        StringBuilder urlZw = new StringBuilder(zwUrl);
        urlZw.append("/prest/pacto/atualizar-contrato")
                .append("?chave=").append(chave)
                .append("&contrato=").append(contrato)
                .append("&codigoPessoa=").append(codigoPessoa);

        ResponseEntity<String> responseZw = httpServico.doJson(
                urlZw.toString(),
                null, HttpMethod.POST, "");

        JSONObject jsonObject = new JSONObject(responseZw);
        if (!jsonObject.optString("erro").isEmpty()) {
            String msg = "Falha ao tentar atualizar contrato! " + jsonObject.optString("erro");
            System.out.println(msg);
            throw new ServiceException(msg);
        }
    }

    @Override
    public List<ContratoDTO> contratosComDataBaseAlterada(FiltroBIControleOperacoesJSON filtroBIControleOperacoesJSON, PaginadorDTO paginadorDTO) throws ServiceException {
        if (filtroBIControleOperacoesJSON.getInicio() == null) {
            throw new ServiceException("data-inicio-nao-informada", "Deve ser informado o filtro data início para consulta!");
        }
        if (filtroBIControleOperacoesJSON.getFim() == null) {
            throw new ServiceException("data-fim-nao-informada", "Deve ser informado o filtro data fim para consulta!");
        }

        List<ContratoDTO> lista;

        try {
            List<Contrato> contratos = contratoNativeRepository.contratosComDataBaseAlterada(
                    filtroBIControleOperacoesJSON, paginadorDTO
            );

            lista = contratoComDataBaseAlteradaAdapter.toDtos(contratos);
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException("erro-consultar-contrato-database-alterada", "Ocorreu um erro ao consultar os contratos com a data base alterada.", e);

        }

        return lista;
    }

    @Override
    public List<ContratoBolsaDTO> findContratoBolsa(FiltroBIControleOperacoesJSON filtroBIControleOperacoesJSON, PaginadorDTO paginadorDTO) throws ServiceException {
        if (filtroBIControleOperacoesJSON.getFim() == null) {
            throw new ServiceException("data-fim-nao-informada", "Deve ser informado o filtro data fim para consulta!");
        }
        try {
            return contratoNativeRepository.findContratosBolsa(
                    filtroBIControleOperacoesJSON, paginadorDTO
            );
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException("erro-consultar-contrato-bolsa", "Ocorreu um erro ao consultar os contratos do tipo bolsa.", e);

        }

    }

    @Override
    public List<ContratoDTO> findAllContratosAutorizacao(FiltroContratoAutorizacaoJSON filtroContratoAutorizacaoJSON, PaginadorDTO paginadorDTO) throws ServiceException {
        if (filtroContratoAutorizacaoJSON.getDataBase() == null) {
            throw new ServiceException("data-base-nao-informada", "Deve ser informado o filtro data base para consulta!");
        }
        try {
            List<Contrato> contratos = contratoNativeRepository.findAllContratosAutorizacao(
                    filtroContratoAutorizacaoJSON, paginadorDTO
            );
            return contratoComAutorizacaoAdapter.toDtos(contratos);
        } catch (Exception e) {
            e.printStackTrace();
            StringBuilder message = new StringBuilder("Ocorreu um erro ao consultar os contratos com autorização ");
            if (filtroContratoAutorizacaoJSON.getComAutorizacaoRenovavel()) {
                message.append("renováveis!");
            } else {
                message.append("não renováveis!");
            }
            throw new ServiceException("erro-consultar-contrato-autororizacao", message.toString(), e);

        }

    }

}
