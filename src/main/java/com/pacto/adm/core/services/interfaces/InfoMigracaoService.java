package com.pacto.adm.core.services.interfaces;

import com.pacto.adm.core.dto.InfoMigracaoDTO;
import com.pacto.adm.core.entities.InfoMigracao;
import com.pacto.adm.core.enumerador.TipoInfoMigracaoEnum;
import com.pacto.config.exceptions.ServiceException;

import java.util.List;

public interface InfoMigracaoService {

    InfoMigracaoDTO consultar(TipoInfoMigracaoEnum recurso) throws ServiceException;
    Boolean recursoHabilitado(TipoInfoMigracaoEnum recurso) throws ServiceException;

    List<InfoMigracaoDTO> recursosHabilitados() throws ServiceException;

    String origemRecurso(TipoInfoMigracaoEnum recurso) throws ServiceException;

    void inserir(TipoInfoMigracaoEnum tipo, String info, String origem) throws ServiceException;

    void remover(TipoInfoMigracaoEnum tipo) throws ServiceException;

    Boolean recursoPadraoEmpresa(TipoInfoMigracaoEnum recurso, Integer empresa) throws ServiceException;
}
