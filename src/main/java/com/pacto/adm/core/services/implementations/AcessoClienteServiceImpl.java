package com.pacto.adm.core.services.implementations;

import com.pacto.adm.core.adapters.AcessoClienteAdapter;
import com.pacto.adm.core.dao.interfaces.AcessoClienteDao;
import com.pacto.adm.core.dto.AcessoClienteDTO;
import com.pacto.adm.core.dto.ClienteInfoAcessosDTO;
import com.pacto.adm.core.entities.AcessoCliente;
import com.pacto.adm.core.services.interfaces.AcessoClienteService;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.utils.Uteis;
import org.hibernate.engine.spi.SessionImplementor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Service
public class AcessoClienteServiceImpl implements AcessoClienteService {

    @Autowired
    private AcessoClienteDao acessoClienteDao;
    @Autowired
    private AcessoClienteAdapter acessoClienteAdapter;

    public List<AcessoClienteDTO> findAllByCodPessoa(Integer codPessoa, PaginadorDTO paginadorDTO) throws ServiceException {
        try {
            List<AcessoCliente> acessoClienteList = acessoClienteDao.findAllByPessoa(codPessoa, paginadorDTO);
            return acessoClienteAdapter.toDtos(acessoClienteList);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public ClienteInfoAcessosDTO obterInfo(Integer codPessoa) throws ServiceException {
        ClienteInfoAcessosDTO dto = new ClienteInfoAcessosDTO();
        String sql = "SELECT " +
                "(SELECT max(dthrentrada) FROM acessocliente " +
                "WHERE cliente = sw.codigocliente " +
                "AND (situacao NOT LIKE 'RV_BLOQ%' OR situacao LIKE 'RV_BLOQ%' AND usuario IS NOT NULL)) as dataUltimoAcesso, " +
                "diasAcessoSemana4, diasAcessoSemana3, diasAcessoSemana2, diasAcessoSemanaPassada, " +
                "diasAcessoMes4, diasAcessoMes3, diasAcessoMes2, diasAcessoUltimoMes " +
                "FROM situacaoclientesinteticodw sw where sw.codigopessoa = " + codPessoa;
        try (SessionImplementor sessionImplementor = acessoClienteDao.createSessionCurrentWork()) {
            sessionImplementor.doWork(connection -> {
                try {
                    ResultSet rs = acessoClienteDao.createStatement(connection, sql);
                    if (rs.next()) {
                        dto.setDataUltimoAcesso(rs.getTimestamp("dataUltimoAcesso"));

                        Integer diasAcessoMes4  = rs.getInt("diasAcessoMes4");
                        Integer diasAcessoMes3  = rs.getInt("diasAcessoMes3");
                        Integer diasAcessoMes2  = rs.getInt("diasAcessoMes2");
                        Integer diasAcessoUltimoMes  = rs.getInt("diasAcessoUltimoMes");

                        diasAcessoMes4 = (diasAcessoMes4 < 0 ? 0 : diasAcessoMes4);
                        diasAcessoMes3 = (diasAcessoMes3 < 0 ? 0 : diasAcessoMes3);
                        diasAcessoMes2 = (diasAcessoMes2 < 0 ? 0 : diasAcessoMes2);
                        diasAcessoUltimoMes = (diasAcessoUltimoMes < 0 ? 0 : diasAcessoUltimoMes);

                        Integer diasAcessoSemana4  = rs.getInt("diasAcessoSemana4");
                        Integer diasAcessoSemana3  = rs.getInt("diasAcessoSemana3");
                        Integer diasAcessoSemana2  = rs.getInt("diasAcessoSemana2");
                        Integer diasAcessoSemanaPassada  = rs.getInt("diasAcessoSemanaPassada");

                        diasAcessoSemana4 = (diasAcessoSemana4 < 0 ? 0 : diasAcessoSemana4);
                        diasAcessoSemana3 = (diasAcessoSemana3 < 0 ? 0 : diasAcessoSemana3);
                        diasAcessoSemana2 = (diasAcessoSemana2 < 0 ? 0 : diasAcessoSemana2);
                        diasAcessoSemanaPassada = (diasAcessoSemanaPassada < 0 ? 0 : diasAcessoSemanaPassada);

                        dto.setMediaUltimosMeses(Uteis.arredondarForcando2CasasDecimais(((double) diasAcessoMes4 + diasAcessoMes3 + diasAcessoMes2 + diasAcessoUltimoMes) / 4));
                        dto.setMediaUltimasSemanas(Uteis.arredondarForcando2CasasDecimais(((double) diasAcessoSemana4 + diasAcessoSemana3 + diasAcessoSemana2 + diasAcessoSemanaPassada) / 4));
                    }
                } catch (Exception e) {
                    throw new RuntimeException(e);
                } finally {
                    connection.close();
                }
            });
            return dto;
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e);
        }
    }

    @Override
    public List<Integer> ultimosMeses(Integer codPessoa, String data)  throws ServiceException {
        List<Integer> ret = new ArrayList<>();
        String sql = "SELECT " +
                "diasAcessoMes4, diasAcessoMes3, diasAcessoMes2, diasAcessoUltimoMes " +
                "FROM situacaoclientesinteticodw sw where sw.codigopessoa = " + codPessoa;
        if(Objects.nonNull(data)) {
            sql += " AND dia >= '"+ data + "'";
        }
        try (SessionImplementor sessionImplementor = acessoClienteDao.createSessionCurrentWork()) {
            String finalSql = sql;
            sessionImplementor.doWork(connection -> {
                try {
                    ResultSet rs = acessoClienteDao.createStatement(connection, finalSql);
                    if (rs.next()) {
                        Integer diasAcessoMes4  = rs.getInt("diasAcessoMes4");
                        Integer diasAcessoMes3  = rs.getInt("diasAcessoMes3");
                        Integer diasAcessoMes2  = rs.getInt("diasAcessoMes2");
                        Integer diasAcessoUltimoMes  = rs.getInt("diasAcessoUltimoMes");

                        ret.add(diasAcessoMes4 < 0 ? 0 : diasAcessoMes4);
                        ret.add(diasAcessoMes3 < 0 ? 0 : diasAcessoMes3);
                        ret.add(diasAcessoMes2 < 0 ? 0 : diasAcessoMes2);
                        ret.add(diasAcessoUltimoMes < 0 ? 0 : diasAcessoUltimoMes);
                    }
                } catch (Exception e) {
                    throw new RuntimeException(e);
                } finally {
                    connection.close();
                }
            });
            return ret;
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e);
        }
    }

    @Override
    public List<Integer> ultimasSemanas(Integer codPessoa, String data) throws ServiceException {
        List<Integer> ret = new ArrayList<>();
        String sql = "SELECT " +
                "diasAcessoSemana4, diasAcessoSemana3, diasAcessoSemana2, diasAcessoSemanaPassada " +
                "FROM situacaoclientesinteticodw sw where sw.codigopessoa = " + codPessoa;

        if(Objects.nonNull(data)) {
            sql += " AND dia >= '"+ data + "'";
        }
        try (SessionImplementor sessionImplementor = acessoClienteDao.createSessionCurrentWork()) {
            String finalSql = sql;
            sessionImplementor.doWork(connection -> {
                try {
                    ResultSet rs = acessoClienteDao.createStatement(connection, finalSql);
                    if (rs.next()) {
                        Integer diasAcessoSemana4  = rs.getInt("diasAcessoSemana4");
                        Integer diasAcessoSemana3  = rs.getInt("diasAcessoSemana3");
                        Integer diasAcessoSemana2  = rs.getInt("diasAcessoSemana2");
                        Integer diasAcessoSemanaPassada  = rs.getInt("diasAcessoSemanaPassada");

                        ret.add(diasAcessoSemana4 < 0 ? 0 : diasAcessoSemana4);
                        ret.add(diasAcessoSemana3 < 0 ? 0 : diasAcessoSemana3);
                        ret.add(diasAcessoSemana2 < 0 ? 0 : diasAcessoSemana2);
                        ret.add(diasAcessoSemanaPassada < 0 ? 0 : diasAcessoSemanaPassada);
                    }
                } catch (Exception e) {
                    throw new RuntimeException(e);
                } finally {
                    connection.close();
                }
            });
            return ret;
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e);
        }
    }
}
