package com.pacto.adm.core.services.implementations;

import com.pacto.adm.core.adapters.integracoes.ParceiroFidelidadeAdapter;
import com.pacto.adm.core.dao.interfaces.ParceiroFidelidadeDao;
import com.pacto.adm.core.dto.integracoes.ParceiroFidelidadeDTO;
import com.pacto.adm.core.entities.ParceiroFidelidade;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.adm.core.services.interfaces.ParceiroFidelidadeService;
import com.pacto.config.utils.Uteis;
import org.springframework.stereotype.Service;

import org.hibernate.exception.ConstraintViolationException;


@Service
public class ParceiroFidelidadeServiceImpl implements ParceiroFidelidadeService {

    private static String urlAPI = "http://uat-api.dotzlabs.com"; //URL GET -- HOMOLOGACAO
    private static String urlAPIPost = "https://uat-api.dotzlabs.com"; //URL POST -- HOMOLOGACAO
//    private static String urlAPI = "http://api.dotzlabs.com"; //URL GET -- PRODUÇÃO
//    private static String urlAPIPost = "https://api.dotzlabs.com"; //URL POST -- PRODUÇÃO

    private static final String endpointSaldo = "/pos/v1/members/%s/balance";//consulta saldo
    private static final String endpointPurchase = "/pos/v1/purchases";//envio de compra
    private static final String endpointRedemptions = "/redemptions/v1/redemptions";//envio de resgate
    private static final String endPointToken = "/accounts/v1/connect/token"; //gerar token
    private static final String endProdutos = "/rewards/v1/products"; //gerar token

    private static String urlAPIToken = urlAPIPost + endPointToken;

    private static final String APPLICATION_JSON = "application/json";
    private static final String APPLICATION_FORM = "application/x-www-form-urlencoded";
    private static final String AUTHORIZATION = "Authorization";
    private static final String CONTENT_TYPE = "Content-Type";
    private static final String BASIC = "Basic %s";
    private static final String GRANT_TYPE = "grant_type";
    private static final String CLIENT_CREDENTIALS = "client_credentials";
    private static final String USERNAME = "username";
    private static final String PASSWORD = "password";
    private static final String SCOPE = "scope";
    private static final String POS_API = "pos.api";
    private static final String REWARDS_API = "rewards.api";
    private static final String REDEMPTIONS_API = "redemptions.api";

    private final ParceiroFidelidadeDao parceiroFidelidadeDao;
    private final ParceiroFidelidadeAdapter parceiroFidelidadeAdapter;

    public ParceiroFidelidadeServiceImpl(ParceiroFidelidadeDao parceiroFidelidadeDao, ParceiroFidelidadeAdapter parceiroFidelidadeAdapter) {
        this.parceiroFidelidadeDao = parceiroFidelidadeDao;
        this.parceiroFidelidadeAdapter = parceiroFidelidadeAdapter;
    }

    @Override
    public ParceiroFidelidadeDTO findByEmpresaId(Integer empresaId) throws Exception {
        parceiroFidelidadeDao.getCurrentSession().clear();
        ParceiroFidelidade parceiroFidelidade = parceiroFidelidadeDao.findByEmpresaId(empresaId);
        if (parceiroFidelidade != null) {
            return parceiroFidelidadeAdapter.toDto(parceiroFidelidade);
        } else {
            ParceiroFidelidadeDTO parceiroFidelidadeDTO = new ParceiroFidelidadeDTO();
            parceiroFidelidadeDTO.setEmpresa(empresaId);
            return parceiroFidelidadeDTO;
        }
    }

    public ParceiroFidelidadeDTO saveOrUpdate(ParceiroFidelidadeDTO parceiroFidelidadeDTO) throws ServiceException {
        try {
            parceiroFidelidadeDao.getCurrentSession().clear();
            ParceiroFidelidade parceiroFidelidade;
//            ParceiroFidelidade parceiroFidelidadeAnterior = new ParceiroFidelidade();
            if (Uteis.intNullOrEmpty(parceiroFidelidadeDTO.getCodigo())) {
                parceiroFidelidade = parceiroFidelidadeAdapter.toEntity(parceiroFidelidadeDTO);
                parceiroFidelidade = parceiroFidelidadeDao.save(parceiroFidelidade);
            } else {
                parceiroFidelidade = parceiroFidelidadeDao.findById(parceiroFidelidadeDTO.getCodigo());
//                parceiroFidelidadeAnterior = parceiroFidelidade.clone();
                parceiroFidelidade = parceiroFidelidadeAdapter.toEntity(parceiroFidelidadeDTO);
                parceiroFidelidade = parceiroFidelidadeDao.update(parceiroFidelidade);
            }
            ParceiroFidelidadeDTO dtoRetornar = parceiroFidelidadeAdapter.toDto(parceiroFidelidade);
//            logService.incluirLogInclusaoAlteracao(parceiroFidelidade, parceiroFidelidadeAnterior, "PARCEIROFIDELIDADE", "Parceiro fidelidade");
            return dtoRetornar;
        } catch (Exception e) {
            if (e.getCause() instanceof ConstraintViolationException) {
                org.hibernate.exception.ConstraintViolationException constraintViolationException = ((org.hibernate.exception.ConstraintViolationException) e.getCause());
                throw new ServiceException(constraintViolationException.getSQLException());
            }
            throw new ServiceException(e);
        }
    }
}
