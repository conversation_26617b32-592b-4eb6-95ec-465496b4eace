package com.pacto.adm.core.services.implementations.pendenciacliente;

import com.pacto.adm.core.dao.interfaces.nativerepositories.cliente.ClienteNativeRepository;
import com.pacto.adm.core.dao.interfaces.nativerepositories.colaborador.ColaboradorNativeRepository;
import com.pacto.adm.core.dao.interfaces.nativerepositories.movparcela.MovParcelaNativeRepository;
import com.pacto.adm.core.dto.cliente.ClienteAniversarianteDTO;
import com.pacto.adm.core.dto.colaborador.ColaboradorAniversarianteDTO;
import com.pacto.adm.core.dto.filtros.bi.FiltroBIDTO;
import com.pacto.adm.core.dto.filtros.bi.FiltroClienteAniversarianteJSON;
import com.pacto.adm.core.dto.movparcela.ParcelaEmAbertoColaboradorDTO;
import com.pacto.adm.core.dto.movparcela.ParcelaEmAbertoDTO;
import com.pacto.adm.core.dto.movparcela.ParcelaEmAtrasoDTO;
import com.pacto.adm.core.dto.pendencia.PendenciaDTO;
import com.pacto.adm.core.enumerador.pendencia.IndicadorPendenciaClientesEnum;
import com.pacto.adm.core.services.interfaces.pendenciacliente.PendenciaClientesService;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@Service
public class PendenciaClientesServiceImpl implements PendenciaClientesService {

    private final ClienteNativeRepository clienteNativeRepository;
    private final ColaboradorNativeRepository colaboradorNativeRepository;
    private final MovParcelaNativeRepository movParcelaNativeRepository;

    public PendenciaClientesServiceImpl(ClienteNativeRepository clienteNativeRepository, ColaboradorNativeRepository colaboradorNativeRepository, MovParcelaNativeRepository movParcelaNativeRepository) {
        this.clienteNativeRepository = clienteNativeRepository;
        this.colaboradorNativeRepository = colaboradorNativeRepository;
        this.movParcelaNativeRepository = movParcelaNativeRepository;
    }

    @Override
    public List<?> consultarPorIndicador(String filtros, PaginadorDTO paginadorDTO, String indicador) throws ServiceException {

        IndicadorPendenciaClientesEnum indicadorPendenciaClientesEnum = IndicadorPendenciaClientesEnum.valueOf(indicador);

        switch (indicadorPendenciaClientesEnum) {
            case PARCELAS_EM_ATRASO:
                return parcelasEmAtraso(filtros, paginadorDTO);
            case PARCELAS_EM_ABERTO:
                return parcelasEmAberto(filtros, paginadorDTO);
            case PARCELAS_EM_ABERTO_COLABORADOR:
                return parcelasEmAbertoColaborador(filtros, paginadorDTO);
            case DEBITO_EM_CONTA_CORRENTE:
                return debitoContaCorrente(filtros, paginadorDTO);
            case CREDITO_EM_CONTA_CORRENTE:
                return creditoContaCorrente(filtros, paginadorDTO);
            case PRODUTOS_VENCIDOS:
                return produtosVencidos(filtros, paginadorDTO);
            case CLIENTES_ANIVERSARIANTES:
                return clientesAniversariantes(filtros, paginadorDTO);
            case COLABORADOR_ANIVERSARIANTES:
                return colaboradoresAniversariantes(filtros, paginadorDTO);
            case BV_PENDENTE:
                return bvPendente(filtros, paginadorDTO);
            case CADASTRO_INCOMPLETO_CLIENTE:
                return cadastroIncompleto(filtros, paginadorDTO, false);
            case CADASTRO_INCOMPLETO_VISITANTE:
                return cadastroIncompleto(filtros, paginadorDTO, true);
            case SEM_ASSINATURA_CONTRATO:
                return semAssinaturaContrato(filtros, paginadorDTO);
            case SEM_ASSINATURA_CANCELAMENTO_CONTRATO:
                return semAssinaturaCancelamentoContrato(filtros, paginadorDTO);
            case SEM_RECONHECIMENTO_FACIAL:
                return semReconhecimentoFacial(filtros, paginadorDTO);
            case SEM_FOTO:
                return semFoto(filtros, paginadorDTO);
            case SEM_GEOLOCALIZACAO:
                return semGeolocalizacao(filtros, paginadorDTO);
            case SEM_PRODUTOS:
                return semProdutos(filtros, paginadorDTO);
            case TRANCAMENTO_VENCIDO:
                return trancamentoVencido(filtros, paginadorDTO);
            default:
                throw new ServiceException("indicador-inexistente", "Indicador informado não existe");
        }
    }

    private List<ParcelaEmAtrasoDTO> parcelasEmAtraso(String filtros, PaginadorDTO paginadorDTO) throws ServiceException {
        FiltroBIDTO filtroBIDTO = new FiltroBIDTO(filtros);

        if (filtroBIDTO.getFim() == null) {
            throw new ServiceException("filtro-data-final-nao-informado", "Deve ser informado o filtro da data final!");
        }

        try {
            return movParcelaNativeRepository.consultarPendenciaParcelaEmAbertoAtraso(filtroBIDTO, paginadorDTO);
        } catch (Exception e) {
            throw new ServiceException("erro-consultar-parcelas-em-atraso", "Ocorreu um erro ao consultar as parcelas em atraso: ", e);
        }
    }

    private List<ParcelaEmAbertoDTO> parcelasEmAberto(String filtros, PaginadorDTO paginadorDTO) throws ServiceException {
        FiltroBIDTO filtroBIDTO = new FiltroBIDTO(filtros);

        try {
            return movParcelaNativeRepository.consultarPendenciaParcelaEmAbertoAPagar(filtroBIDTO, paginadorDTO);
        } catch (Exception e) {
            throw new ServiceException("erro-consultar-parcelas-em-aberto", "Ocorreu um erro ao consultar as parcelas em aberto: ", e);
        }
    }

    private List<ParcelaEmAbertoColaboradorDTO> parcelasEmAbertoColaborador(String filtros, PaginadorDTO paginadorDTO) throws ServiceException {
        FiltroBIDTO filtroBIDTO = new FiltroBIDTO(filtros);

        try {
            return movParcelaNativeRepository.consultarPendenciaParcelaEmAbertoAPagarColaborador(filtroBIDTO, paginadorDTO);
        } catch (Exception e) {
            throw new ServiceException("erro-consultar-parcelas-em-aberto", "Ocorreu um erro ao consultar as parcelas em aberto: ", e);
        }
    }

    private List<PendenciaDTO> debitoContaCorrente(String filtros, PaginadorDTO paginadorDTO) throws ServiceException {
        return Collections.emptyList();
    }

    private List<PendenciaDTO> creditoContaCorrente(String filtros, PaginadorDTO paginadorDTO) throws ServiceException {
        return Collections.emptyList();
    }

    private List<PendenciaDTO> produtosVencidos(String filtros, PaginadorDTO paginadorDTO) throws ServiceException {
        return Collections.emptyList();
    }

    private List<ClienteAniversarianteDTO> clientesAniversariantes(String filtros, PaginadorDTO paginadorDTO) throws ServiceException {
        FiltroClienteAniversarianteJSON filtroClienteAniversarianteJSON = new FiltroClienteAniversarianteJSON(filtros);
        if (filtroClienteAniversarianteJSON.getInicio() == null) {
            throw new ServiceException("filtro-data-inicial-nao-informado", "Deve ser informado o filtro da data inicial!");
        }

        if (filtroClienteAniversarianteJSON.getFim() == null) {
            throw new ServiceException("filtro-data-final-nao-informado", "Deve ser informado o filtro da data final!");
        }

        try {
            return clienteNativeRepository.consultaAniversariantes(filtroClienteAniversarianteJSON, paginadorDTO);
        } catch (Exception e) {
            throw new ServiceException("erro-consultar-clientes-aniversariantes", "Ocorreu um erro ao consultar os clientes aniversariantes: ", e);
        }
    }

    private List<ColaboradorAniversarianteDTO> colaboradoresAniversariantes(String filtros, PaginadorDTO paginadorDTO) throws ServiceException {
        FiltroBIDTO filtroBIDTO = new FiltroBIDTO(filtros);

        if (filtroBIDTO.getFim() == null) {
            throw new ServiceException("filtro-data-final-nao-informado", "Deve ser informado o filtro da data final!");
        }

        try {
            return colaboradorNativeRepository.colaboradorAniversariante(filtroBIDTO, paginadorDTO);
        } catch (Exception e) {
            throw new ServiceException("erro-consultar-colaborador-aniversariantes", "Ocorreu um erro ao consultar os colaboradores aniversariantes: ", e);
        }
    }

    private List<PendenciaDTO> bvPendente(String filtros, PaginadorDTO paginadorDTO) throws ServiceException {
        return Collections.emptyList();
    }

    private List<PendenciaDTO> cadastroIncompleto(String filtros, PaginadorDTO paginadorDTO, Boolean visitante) throws ServiceException {
        return Collections.emptyList();
    }

    private List<PendenciaDTO> semAssinaturaContrato(String filtros, PaginadorDTO paginadorDTO) throws ServiceException {
        return Collections.emptyList();
    }

    private List<PendenciaDTO> semAssinaturaCancelamentoContrato(String filtros, PaginadorDTO paginadorDTO) throws ServiceException {
        return Collections.emptyList();
    }

    private List<PendenciaDTO> semReconhecimentoFacial(String filtros, PaginadorDTO paginadorDTO) throws ServiceException {
        return Collections.emptyList();
    }

    private List<PendenciaDTO> semFoto(String filtros, PaginadorDTO paginadorDTO) throws ServiceException {
        return Collections.emptyList();
    }

    private List<PendenciaDTO> semGeolocalizacao(String filtros, PaginadorDTO paginadorDTO) throws ServiceException {
        return Collections.emptyList();
    }

    private List<PendenciaDTO> semProdutos(String filtros, PaginadorDTO paginadorDTO) throws ServiceException {
        return Collections.emptyList();
    }

    private List<PendenciaDTO> trancamentoVencido(String filtros, PaginadorDTO paginadorDTO) throws ServiceException {
        return Collections.emptyList();
    }
}
