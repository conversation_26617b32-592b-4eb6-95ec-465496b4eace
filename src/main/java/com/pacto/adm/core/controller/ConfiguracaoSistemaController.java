package com.pacto.adm.core.controller;

import com.pacto.adm.core.dto.enveloperesposta.configuracao.EnvelopeRespostaConfiguracaoSistemaDTO;
import com.pacto.adm.core.services.interfaces.ConfiguracaoSistemaService;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/configuracao-sistema")
@Tag(name = "Configurações SESI")
public class ConfiguracaoSistemaController {

    private final ConfiguracaoSistemaService configuracaoSistemaService;

    public ConfiguracaoSistemaController(ConfiguracaoSistemaService configuracaoSistemaService) {
        this.configuracaoSistemaService = configuracaoSistemaService;
    }

    @Operation(
            summary = "Consultar configurações do sistema relacionadas ao SESI",
            description = "Consulta as configurações do sistema relacionadas ao SESI",
            responses = {
                    @ApiResponse(
                            responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json", schema = @Schema(implementation = EnvelopeRespostaConfiguracaoSistemaDTO.class),
                                    examples = @ExampleObject(name = "Status 200", summary = "Exemplo de resposta com o status 200",
                                            value = EnvelopeRespostaConfiguracaoSistemaDTO.resposta
                                    )))}
    )
    @ResponseBody
    @GetMapping()
    public ResponseEntity<EnvelopeRespostaDTO> get() {
        try {
            return ResponseEntityFactory.ok(configuracaoSistemaService.get());
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }
}


