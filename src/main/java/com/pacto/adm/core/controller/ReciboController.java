package com.pacto.adm.core.controller;

import com.pacto.adm.core.dto.enveloperesposta.contrato.EnvelopeRespostaListMovPagamentoDTO;
import com.pacto.adm.core.dto.enveloperesposta.contrato.EnvelopeRespostaListMovParcelaDTO;
import com.pacto.adm.core.dto.enveloperesposta.pagamento.recibo.EnvelopeRespostaListReciboPagamentoDTO;
import com.pacto.adm.core.dto.enveloperesposta.produto.EnvelopeRespostaListMovProdutoDTO;
import com.pacto.adm.core.services.interfaces.MovPagamentoService;
import com.pacto.adm.core.services.interfaces.MovParcelaService;
import com.pacto.adm.core.services.interfaces.MovProdutoService;
import com.pacto.adm.core.services.interfaces.ReciboPagamentoService;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/recibos")
public class ReciboController {

    @Autowired
    private ReciboPagamentoService reciboPagamentoService;
    @Autowired
    private MovParcelaService movParcelaService;
    @Autowired
    private MovProdutoService movProdutoService;
    @Autowired
    private MovPagamentoService movPagamentoService;

    @Operation(
            summary = "Consultar todos os recibos de uma pessoa",
            description = "Consulta todos os recibos de uma pessoa.",
            tags = {"Consulta de Recibos"},
            parameters = {
                    @Parameter(name = "codPessoa", description = "Código da pessoa que se deseja consultar os recibos", example = "3", required = true),
                    @Parameter(name = "page", description = "Número da página", example = "0", schema = @Schema(implementation = Integer.class)),
                    @Parameter(name = "size", description = "Quantidade máxima de elementos por página", example = "10", schema = @Schema(implementation = Integer.class)),
            },
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json", schema = @Schema(implementation = EnvelopeRespostaListReciboPagamentoDTO.class),
                                    examples = {
                                            @ExampleObject(
                                                    summary = "Resposta Status 200",
                                                    description = "Exemplo de resposta com o status 200.",
                                                    value = EnvelopeRespostaListReciboPagamentoDTO.resposta),
                                    }
                            )
                    )
            }
    )
    @GetMapping("/{codPessoa}")
    public ResponseEntity<EnvelopeRespostaDTO> findByPessoa(@PathVariable Integer codPessoa,
                                                            @Parameter(hidden = true) @RequestParam(value = "filters", required = false) JSONObject filtros,
                                                            @Parameter(hidden = true) PaginadorDTO paginadorDTO) {
        try {
            return ResponseEntityFactory.ok(reciboPagamentoService.findAllByCodPessoa(codPessoa, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Consultar parcelas pagas através de um recibo",
            description = "Consulta as parcelas pagas através do código de um recibo.",
            tags = {"Consulta de Recibos"},
            parameters = {
                    @Parameter(name = "codRecibo", description = "Código do recibo que será consultado as parcelas", example = "45", required = true),
            },
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json", schema = @Schema(implementation = EnvelopeRespostaListMovParcelaDTO.class),
                                    examples = {
                                            @ExampleObject(
                                                    summary = "Resposta Status 200",
                                                    description = "Exemplo de resposta com o status 200.",
                                                    value = EnvelopeRespostaListMovParcelaDTO.resposta),
                                    }
                            )
                    )
            }
    )
    @GetMapping("/{codRecibo}/parcelas")
    public ResponseEntity<EnvelopeRespostaDTO> findParcelasByRecibo(@PathVariable Integer codRecibo) {
        try {
            return ResponseEntityFactory.ok(movParcelaService.findAllByCodRecibo(codRecibo));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Consultar produtos através do código de um recibo",
            description = "Consulta os produtos que foram vendidos através do código de um recibo.",
            tags = {"Consulta de Recibos"},
            parameters = {
                    @Parameter(name = "codRecibo", description = "Código do recibo que será consultado os produtos", example = "45", required = true),
            },
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaListMovProdutoDTO.class),
                                    examples = {
                                            @ExampleObject(
                                                    summary = "Resposta Status 200",
                                                    description = "Exemplo de resposta com o status 200.",
                                                    value = EnvelopeRespostaListMovProdutoDTO.resposta),
                                    }
                            )
                    )
            }
    )
    @GetMapping("/{codRecibo}/produtos")
    public ResponseEntity<EnvelopeRespostaDTO> findProdutosByRecibo(@PathVariable Integer codRecibo) {
        try {
            return ResponseEntityFactory.ok(movProdutoService.findAllByCodRecibo(codRecibo));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }


    @Operation(
            summary = "Consultar todos os pagamentos através do código de um recibo",
            description = "Consulta todos os pagamentos realizados através do código de um recibo.",
            tags = {"Consulta de Recibos"},
            parameters = {
                    @Parameter(name = "codRecibo", description = "Código do recibo que será consultado os pagamentos", example = "45", required = true),
            },
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaListMovPagamentoDTO.class),
                                    examples = {
                                            @ExampleObject(
                                                    summary = "Resposta Status 200",
                                                    description = "Exemplo de resposta com o status 200.",
                                                    value = EnvelopeRespostaListMovPagamentoDTO.resposta),
                                    }
                            )
                    )
            }
    )
    @GetMapping("/{codRecibo}/pagamentos")
    public ResponseEntity<EnvelopeRespostaDTO> findPagamentosByRecibo(@PathVariable Integer codRecibo) {
        try {
            return ResponseEntityFactory.ok(movPagamentoService.findAllByCodRecibo(codRecibo));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }
}
