package com.pacto.adm.core.controller;

import com.pacto.adm.core.dto.enveloperesposta.grupo.EnvelopeRespostaGrupo;
import com.pacto.adm.core.dto.enveloperesposta.grupo.EnvelopeRespostaListGrupo;
import com.pacto.adm.core.dto.enveloperesposta.log.EnvelopeRespostaListLogDTO;
import com.pacto.adm.core.services.interfaces.GrupoService;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/grupo")
@Tag(name = "Grupo com Desconto")
public class GrupoController {

    @Autowired
    private GrupoService grupoService;
    @Operation(
            summary = "Consultar todos os grupos",
            description = "Consulta todos os grupos.",
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(mediaType = "application/json", schema = @Schema(implementation = EnvelopeRespostaListGrupo.class),
                                    examples = @ExampleObject(name = "Status 200", summary = "Exemplo de resposta com o status 200", value = EnvelopeRespostaListGrupo.resposta)
                            )
                    )}
    )
    @GetMapping("")
    public ResponseEntity<EnvelopeRespostaDTO> findAll() {
        try {
            return ResponseEntityFactory.ok(grupoService.findAll());
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}
