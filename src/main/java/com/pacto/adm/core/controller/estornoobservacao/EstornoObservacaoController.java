package com.pacto.adm.core.controller.estornoobservacao;

import com.pacto.adm.core.dto.enveloperesposta.estornoobservacao.EnvelopeRespostaListEstornoObservacaoDTO;
import com.pacto.adm.core.dto.filtros.FiltroBIControleOperacoesJSON;
import com.pacto.adm.core.services.interfaces.estornoobservacao.EstornoObservacaoService;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.json.JSONObject;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/estorno-observacao")
public class EstornoObservacaoController {

    private final EstornoObservacaoService estornoObservacaoService;

    public EstornoObservacaoController(EstornoObservacaoService estornoObservacaoService) {
        this.estornoObservacaoService = estornoObservacaoService;
    }

    @GetMapping("/usuario-comum")
    public ResponseEntity<EnvelopeRespostaDTO> usuarioComum(
            @RequestParam(value = "filters", required = false) String filtros,
            PaginadorDTO paginadorDTO
    ) {
        try {
            FiltroBIControleOperacoesJSON filtroBIControleOperacoesJSON = new FiltroBIControleOperacoesJSON();
            if (filtros != null) {
                filtroBIControleOperacoesJSON = new FiltroBIControleOperacoesJSON(new JSONObject(filtros));
            }
            return ResponseEntityFactory.ok(estornoObservacaoService.consultarPorNomeEntidadePorDataAlteracaoPorOperacao(
                    filtroBIControleOperacoesJSON, paginadorDTO, false, false
            ), paginadorDTO);
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @GetMapping("/admin")
    public ResponseEntity<EnvelopeRespostaDTO> admin(
            @RequestParam(value = "filters", required = false) String filtros,
            PaginadorDTO paginadorDTO
    ) {
        try {
            FiltroBIControleOperacoesJSON filtroBIControleOperacoesJSON = new FiltroBIControleOperacoesJSON();
            if (filtros != null) {
                filtroBIControleOperacoesJSON = new FiltroBIControleOperacoesJSON(new JSONObject(filtros));
            }
            return ResponseEntityFactory.ok(estornoObservacaoService.consultarPorNomeEntidadePorDataAlteracaoPorOperacao(
                    filtroBIControleOperacoesJSON, paginadorDTO, true, false
            ), paginadorDTO);
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @GetMapping("/recorrencia")
    public ResponseEntity<EnvelopeRespostaDTO> recorrencia(
            @RequestParam(value = "filters", required = false) String filtros,
            PaginadorDTO paginadorDTO
    ) {
        try {
            FiltroBIControleOperacoesJSON filtroBIControleOperacoesJSON = new FiltroBIControleOperacoesJSON();
            if (filtros != null) {
                filtroBIControleOperacoesJSON = new FiltroBIControleOperacoesJSON(new JSONObject(filtros));
            }
            return ResponseEntityFactory.ok(estornoObservacaoService.consultarPorNomeEntidadePorDataAlteracaoPorOperacao(
                    filtroBIControleOperacoesJSON, paginadorDTO, false, true
            ), paginadorDTO);
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }
}
