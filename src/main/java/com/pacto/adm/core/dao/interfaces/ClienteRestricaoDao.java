package com.pacto.adm.core.dao.interfaces;


import com.pacto.adm.core.dto.filtros.FiltroClienteRestricaoJSON;
import com.pacto.adm.core.entities.ClienteRestricao;
import com.pacto.config.dto.PaginadorDTO;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ClienteRestricaoDao extends DaoGenerico<ClienteRestricao, Integer> {

    List<ClienteRestricao> findAll(FiltroClienteRestricaoJSON filtros, PaginadorDTO paginadorDTO);

    List<ClienteRestricao> findByCpf(String cpf);

    List<ClienteRestricao> findByCpfAndTipo(String cpf, String tipo);

    List<ClienteRestricao> findByCpfAndChaveAndCodigoEmpresaAndTipo(String cpf, String chaveEmpresa, Integer codigoEmpresa, String tipo);

}
