package com.pacto.adm.core.dao.implementations;

import com.pacto.adm.core.dao.interfaces.ParceiroFidelidadeDao;
import com.pacto.adm.core.entities.ParceiroFidelidade;
import org.springframework.stereotype.Repository;

import javax.persistence.Query;
import java.util.HashMap;
import java.util.Map;

@Repository
public class ParceiroFidelidadeDaoImpl extends  DaoGenericoImpl<ParceiroFidelidade, Integer> implements ParceiroFidelidadeDao {

    @Override
    public ParceiroFidelidade findByEmpresaId(Integer empresaId) {
        getCurrentSession().clear();

        Map<String, Object> params = new HashMap<>();
        StringBuilder s = new StringBuilder("SELECT obj FROM ParceiroFidelidade obj ");
        s.append(" WHERE obj.empresa.codigo = :empresa ");
        params.put("empresa", empresaId);
        Query query = getCurrentSession().createQuery(s.toString());
        if (params != null) {
            for (String p : params.keySet()) {
                query.setParameter(p, params.get(p));
            }
        }
        ParceiroFidelidade parceiroFidelidade = null;
        try {
            parceiroFidelidade = (ParceiroFidelidade) query.getSingleResult();
        } catch (Exception e) {
        }
        return parceiroFidelidade;
    }
}
