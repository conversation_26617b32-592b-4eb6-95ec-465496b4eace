package com.pacto.adm.core.dao.interfaces;

import com.pacto.adm.core.dto.negociacao.CheckNegociacaoDTO;
import com.pacto.adm.core.dto.negociacao.NegociacaoDTO;
import com.pacto.adm.core.entities.contrato.SituacaoContratoEnum;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.adm.core.dto.filtros.FiltroContratoJSON;
import com.pacto.adm.core.entities.contrato.Contrato;
import com.pacto.config.exceptions.ServiceException;

import java.util.List;

public interface ContratoDao extends DaoGenerico<Contrato, Integer> {

    List<Contrato> findAllByPessoa(Integer codPessoa, PaginadorDTO paginadorDTO) throws Exception;

    List<Contrato> findAllByCodMatricula(Integer codMatricula, FiltroContratoJSON filtroContratoJSON, PaginadorDTO paginadorDTO) throws Exception;

    Integer consultarQuantidadeContratosAssinados(Integer codMatricula) throws Exception;

    Contrato obterRenovacaoContrato(Integer contratoAtual) throws Exception;

    Contrato obterContratoAtual(Integer contratoAtual) throws Exception;

    void montarSugestoesPlanoNegociacao(Integer usuario, NegociacaoDTO config, SituacaoContratoEnum situacaoContrato) throws Exception;

    CheckNegociacaoDTO checkInicialNegociacaoAluno(Integer usuario, Integer codigoCliente, Integer empresa, Integer contrato, Boolean novaLinha) throws ServiceException;

    CheckNegociacaoDTO checkInicialNegociacaoAluno(Integer usuario, Integer codigoCliente, Integer empresa, Integer contrato, Boolean novaLinha, Boolean verificarEmpresaEContratoResponsavelRematricula) throws ServiceException;

    List consultarNaoContratosAssinados(Integer codMatricula) throws Exception;
}
