package com.pacto.adm.core.dao.implementations;

import com.pacto.adm.core.dao.interfaces.ConfiguracaoIntegracaoFogueteDao;
import com.pacto.adm.core.entities.ConfiguracaoIntegracaoFoguete;
import org.springframework.stereotype.Repository;

import javax.persistence.Query;
import java.util.HashMap;
import java.util.Map;

@Repository
public class ConfiguracaoIntegracaoFogueteDaoImpl extends DaoGenericoImpl<ConfiguracaoIntegracaoFoguete, Integer> implements ConfiguracaoIntegracaoFogueteDao {
    @Override
    public ConfiguracaoIntegracaoFoguete findByEmpresaId(Integer empresaId) {
        getCurrentSession().clear();

        Map<String, Object> params = new HashMap<>();
        StringBuilder s = new StringBuilder("SELECT obj FROM ConfiguracaoIntegracaoFoguete obj ");
        s.append(" WHERE obj.empresa.codigo = :empresa ");
        params.put("empresa", empresaId);
        Query query = getCurrentSession().createQuery(s.toString());
        if (params != null) {
            for (String p : params.keySet()) {
                query.setParameter(p, params.get(p));
            }
        }
        ConfiguracaoIntegracaoFoguete config = new ConfiguracaoIntegracaoFoguete();
        try {
            config = (ConfiguracaoIntegracaoFoguete) query.getResultList().get(0);
        } catch (Exception e) {}

        return config;
    }
}
