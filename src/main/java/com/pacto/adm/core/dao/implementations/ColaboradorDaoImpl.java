package com.pacto.adm.core.dao.implementations;

import com.pacto.adm.core.dao.interfaces.ColaboradorDao;
import com.pacto.adm.core.entities.Colaborador;
import com.pacto.adm.core.entities.Pessoa;
import com.pacto.adm.core.entities.empresa.Empresa;
import com.pacto.adm.core.enumerador.SituacaoColaboradorEnum;
import com.pacto.adm.core.enumerador.TipoColaboradorEnum;
import com.pacto.config.utils.UteisValidacao;
import org.hibernate.engine.spi.SessionImplementor;
import org.springframework.stereotype.Repository;

import javax.persistence.Query;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public class ColaboradorDaoImpl extends DaoGenericoImpl<Colaborador, Integer> implements ColaboradorDao {

    @Override
    public Colaborador findByPessoa(Integer codigoPessoa, Integer empresa) throws Exception{
        StringBuilder where = new StringBuilder();
        Map<String, Object> params = new HashMap<>();
        where.append(" SELECT col FROM Colaborador col WHERE col.pessoa.codigo = :codigoPessoa ");
        if (empresa != null  ){
            where.append(" and col.empresa.codigo = :empresa ");
            params.put("empresa", empresa);
        }
        params.put("codigoPessoa", codigoPessoa);
        Query query = getCurrentSession().createQuery(where.toString());
        if (params != null) {
            for (String p : params.keySet()) {
                query.setParameter(p, params.get(p));
            }
        }
        if (query.getResultList().size() > 0) {
            return (Colaborador) query.getResultList().get(0);
        } else {
            return null;
        }
    }

    @Override
    public List<Colaborador> findColaboradorByTipoColaboradorAndSituacao(TipoColaboradorEnum[] arrayTipoColaborador,
                                                                         SituacaoColaboradorEnum situacaoColaboradorEnum,
                                                                         Integer codigoEmpresa) {
        List<Colaborador> colaboradores = new ArrayList<>();
        try (SessionImplementor sessionImplementor = createSessionCurrentWork()) {
            sessionImplementor.doWork(connection -> {
                StringBuilder sql = new StringBuilder("SELECT distinct \n" +
                        "c.*, p.nome as colaboradorNome, p.codigo as pessoaCodigo \n" +
                        "FROM colaborador c INNER JOIN tipocolaborador t on t.colaborador = c.codigo \n" +
                        "INNER JOIN pessoa p on p.codigo = c.pessoa \n");

                if (arrayTipoColaborador != null &&
                        arrayTipoColaborador.length > 0) {
                    String tipos = "";
                    for (TipoColaboradorEnum obj : arrayTipoColaborador) {
                        tipos += (",'" + obj.getSigla() + "'");
                    }
                    sql.append("AND upper(t.descricao) in (").append(tipos.replaceFirst(",","")).append(") \n");
                }

                if (situacaoColaboradorEnum != null) {
                    sql.append("AND c.situacao = '").append(situacaoColaboradorEnum.getSigla().toUpperCase()).append("' \n");
                }

                if (codigoEmpresa != null && codigoEmpresa != 0) {
                    sql.append("AND c.empresa = ").append(codigoEmpresa).append(" \n");
                }

                sql.append("ORDER BY p.nome ");
                try {
                    ResultSet rs = createStatement(connection, sql.toString());
                    while (rs.next()) {
                        Colaborador colaborador = new Colaborador();
                        colaborador.setCodigo(rs.getInt("codigo"));
                        colaborador.setSituacao(rs.getString("situacao"));
                        Empresa empresa = new Empresa();
                        empresa.setCodigo(rs.getInt("empresa"));
                        colaborador.setEmpresa(empresa);
                        Pessoa pessoa = new Pessoa();
                        pessoa.setCodigo(rs.getInt("pessoaCodigo"));
                        pessoa.setNome(rs.getString("colaboradorNome"));
                        colaborador.setPessoa(pessoa);
                        colaboradores.add(colaborador);
                    }
                } catch (Exception e) {
                    throw new RuntimeException(e);
                } finally {
                    connection.close();
                }
            });
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        return colaboradores;
    }

    @Override
    public List<String> tiposDoColaborador(Integer codigoColaborador, Integer usuario) throws Exception {
        List<String> tipos = new ArrayList<>();
        try (SessionImplementor sessionImplementor = createSessionCurrentWork()) {
            sessionImplementor.doWork(connection -> {
                try {
                    String sql = "select descricao from tipocolaborador t ";
                    if (!UteisValidacao.emptyNumber(usuario)) {
                        sql += "where colaborador in (select colaborador from usuario where codigo = " + usuario + ") ";
                    } else {
                        sql += "where colaborador = " + codigoColaborador;
                    }
                    ResultSet rs = createStatement(connection, sql);
                    while (rs.next()) {
                        tipos.add(rs.getString("descricao"));
                    }
                } catch (Exception e) {
                    throw new RuntimeException(e);
                } finally {
                    connection.close();
                }
            });
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        return tipos;
    }
}
