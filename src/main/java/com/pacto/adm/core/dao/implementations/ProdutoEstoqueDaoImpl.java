package com.pacto.adm.core.dao.implementations;

import com.pacto.adm.core.dao.interfaces.ProdutoEstoqueDao;
import com.pacto.adm.core.entities.financeiro.ProdutoEstoque;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.utils.Calendario;
import org.hibernate.engine.spi.SessionImplementor;
import org.springframework.stereotype.Repository;

import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Repository
public class ProdutoEstoqueDaoImpl extends DaoGenericoImpl<ProdutoEstoque, Integer> implements ProdutoEstoqueDao {

    public Date pesquisarBalancoComDataMaior(Date dataComparar, Integer codigoProduto, Integer codigoEmpresa)throws Exception{
        try {
            Map<String, Date> mapaBalanco = new HashMap<>();
            mapaBalanco.put("dataBalanco", null);
            try (SessionImplementor sessionImplementor = createSessionCurrentWork()) {
                sessionImplementor.doWork(connection -> {
                    try {
                        StringBuilder sql = new StringBuilder();

                        sql.append("select max(b.dataCadastro) dataBalanco \n");
                        sql.append("from balanco b \n");
                        sql.append("inner join balancoItens bi on bi.balanco = b.codigo \n");
                        sql.append("where cancelado = false and bi.produto = ").append(codigoProduto).append(" and b.empresa = ").append(codigoEmpresa).append(" \n");
                        sql.append("and b.dataCadastro > '").append(Calendario.getData(dataComparar, "yyyy-MM-dd HH:mm:ss")).append("'");
                        PreparedStatement pst = connection.prepareStatement(sql.toString());
                        ResultSet rs = pst.executeQuery();
                        if (rs.next()){
                            mapaBalanco.put("dataBalanco", rs.getTimestamp("dataBalanco"));
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                });

            }
            return mapaBalanco.get("dataBalanco");
        }catch (Exception e){
            throw new ServiceException(e);
        }
    }

    public Date pesquisarProdutoEstoqueComDataMaior(Date dataComparar, Integer codigoProduto, Integer codigoEmpresa)throws Exception{
        try {
            Map<String, Date> mapaInicioEstoque = new HashMap<>();
            mapaInicioEstoque.put("dataInicioEstoque", null);
            try (SessionImplementor sessionImplementor = createSessionCurrentWork()) {
                sessionImplementor.doWork(connection -> {
                    try {
                        StringBuilder sql = new StringBuilder();
                        sql.append("select min(pes.dataCadastro) as dataCadastro \n");
                        sql.append("from produtoEstoque_alteracaosit pes \n");
                        sql.append("inner join produtoEstoque pe on pe.codigo = pes.produtoEstoque \n");
                        sql.append("where produto = ").append(codigoProduto).append(" and empresa = ").append(codigoEmpresa);
                        //verificar se a datacomparar está com hora vazia, se sim, comparar apenas o dia
                        //para que a consulta permita lançar vendas com data retroativa
                        if(isHoraZero(dataComparar)){
                            sql.append("and pes.dataCadastro::date > '").append(Calendario.getData(dataComparar, "yyyy-MM-dd")).append("'");
                        } else {
                            sql.append("and pes.dataCadastro > '").append(Calendario.getData(dataComparar, "yyyy-MM-dd HH:mm:ss")).append("'");
                        }
                        PreparedStatement pst = connection.prepareStatement(sql.toString());
                        ResultSet rs = pst.executeQuery();
                        if (rs.next()){
                            mapaInicioEstoque.put("dataInicioEstoque", rs.getTimestamp("dataCadastro"));
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                });

            }
            return mapaInicioEstoque.get("dataInicioEstoque");
        }catch (Exception e){
            throw new ServiceException(e);
        }
    }

    public boolean isHoraZero(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        int hour = calendar.get(Calendar.HOUR_OF_DAY);
        int minute = calendar.get(Calendar.MINUTE);
        int second = calendar.get(Calendar.SECOND);
        int millisecond = calendar.get(Calendar.MILLISECOND);
        return hour == 0 && minute == 0 && second == 0 && millisecond == 0;
    }
}
