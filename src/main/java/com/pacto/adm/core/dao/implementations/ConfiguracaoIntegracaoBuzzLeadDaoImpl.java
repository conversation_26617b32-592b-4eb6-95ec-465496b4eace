package com.pacto.adm.core.dao.implementations;

import com.pacto.adm.core.dao.interfaces.ConfiguracaoIntegracaoBuzzLeadDao;
import com.pacto.adm.core.entities.ConfiguracaoIntegracaoBuzzLead;
import org.springframework.stereotype.Repository;

import javax.persistence.Query;
import java.util.HashMap;
import java.util.Map;

@Repository
public class ConfiguracaoIntegracaoBuzzLeadDaoImpl extends DaoGenericoImpl<ConfiguracaoIntegracaoBuzzLead, Integer> implements ConfiguracaoIntegracaoBuzzLeadDao {
    @Override
    public ConfiguracaoIntegracaoBuzzLead findByEmpresaId(Integer empresaId) {
        getCurrentSession().clear();

        Map<String, Object> params = new HashMap<>();
        StringBuilder s = new StringBuilder("SELECT obj FROM ConfiguracaoIntegracaoBuzzLead obj ");
        s.append(" WHERE obj.empresa.codigo = :empresa ");
        params.put("empresa", empresaId);
        Query query = getCurrentSession().createQuery(s.toString());
        if (params != null) {
            for (String p : params.keySet()) {
                query.setParameter(p, params.get(p));
            }
        }
        ConfiguracaoIntegracaoBuzzLead config = new ConfiguracaoIntegracaoBuzzLead();
        try {
            config = (ConfiguracaoIntegracaoBuzzLead) query.getResultList().get(0);
        } catch (Exception e) {
        }
        return config;
    }
}
