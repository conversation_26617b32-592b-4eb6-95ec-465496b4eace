package com.pacto.adm.core.dao.implementations.nativerepositories.cliente;

import com.pacto.adm.core.dao.interfaces.nativerepositories.cliente.ClienteParaVerificarNativeRepository;
import com.pacto.adm.core.dto.PessoaDTO;
import com.pacto.adm.core.dto.cliente.ClienteParaVerificarDTO;
import com.pacto.adm.core.dto.filtros.bi.FiltroBIDTO;
import com.pacto.adm.core.util.QueryUtils;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import javax.persistence.Tuple;
import java.math.BigInteger;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.stream.Collectors;

@Repository
public class ClienteParaVerificarNativeRepositoryImpl implements ClienteParaVerificarNativeRepository {

    @PersistenceContext(unitName = "zwClientPU")
    private EntityManager entityManager;

    @Override
    public List<ClienteParaVerificarDTO> consultarClientesParaVerificar(FiltroBIDTO filtroBIDTO, PaginadorDTO paginadorDTO, Boolean verificado) throws Exception {

        if (filtroBIDTO == null) {
            throw new Exception("Necessário informar algum filtro!");
        }

        if (filtroBIDTO.getEmpresa() == null) {
            throw new Exception("Deve ser informado a empresa!");
        }

        String sql = sqlConsultaClientesParaVerificar(filtroBIDTO, paginadorDTO, verificado);

        Query query = entityManager.createNativeQuery(sql, Tuple.class);

        setParametersConsultaClientesParaVerificar(query, filtroBIDTO);
        QueryUtils.setQueryPagination(query, paginadorDTO);

        List<Tuple> resultado = query.getResultList();

        List<ClienteParaVerificarDTO> lista = resultado.stream().map(tuple -> new ClienteParaVerificarDTO(
                tuple.get("codigoCliente", Integer.class),
                tuple.get("matricula", String.class),
                tuple.get("verificadoem", Date.class),
                tuple.get("contratorenovado", Boolean.class),
                tuple.get("usuarioVerificacao", String.class),
                new PessoaDTO(
                        tuple.get("nome", String.class)
                )
        )).collect(Collectors.toList());

        if (paginadorDTO != null) {
            Query countQuery = entityManager.createNativeQuery("select count(sqlCount.*) from (" + sql + ") as sqlCount");
            setParametersConsultaClientesParaVerificar(countQuery, filtroBIDTO);
            paginadorDTO.setQuantidadeTotalElementos(((BigInteger) countQuery.getSingleResult()).longValue());
        }

        return lista;
    }

    private String sqlConsultaClientesParaVerificar(FiltroBIDTO filtroBIDTO, PaginadorDTO paginadorDTO, Boolean verificado) {
        StringBuilder sql = new StringBuilder();

        sql.append("SELECT cli.codigo as codigocliente,\n");
        sql.append("    cli.matricula,\n");
        sql.append("    pes.nome,\n");
        sql.append("    cli.verificadoem,\n");
        sql.append("    (EXISTS (SELECT codigo FROM contrato WHERE pessoa = cli.pessoa AND situacaocontrato = 'RN')) as contratorenovado,\n");
        sql.append("    usu.nome as usuarioVerificacao\n");
        sql.append("FROM cliente cli\n");
        sql.append("INNER JOIN pessoa pes ON cli.pessoa = pes.codigo\n");
        sql.append("LEFT JOIN usuario usu ON cli.usuarioVerificacao = usu.codigo\n");
        sql.append("WHERE verificarCliente\n");
        sql.append("AND cli.empresa = :codigoEmpresa\n");

        if (verificado != null) {
            if (verificado) {
                sql.append("AND verificadoEm IS NOT NULL\n");
            } else {
                sql.append("AND verificadoEm IS NULL\n");
            }
        }

        if (StringUtils.hasText(filtroBIDTO.getQuickSearchValue())) {
            sql.append("AND ");
            LinkedHashMap<String, Class<?>> columnTypes = new LinkedHashMap<>();
            columnTypes.put("pes.nome", null);
            columnTypes.put("cli.matricula", null);
            columnTypes.put("cli.verificadoem", Date.class);
            columnTypes.put("usu.nome", null);
            QueryUtils.buildSqlQuickSearchByType(sql, columnTypes);
        }

        String sortField = "pes.nome";
        String sortOrder = "ASC";

        if (paginadorDTO != null) {
            if (paginadorDTO.getPage() == null) {
                paginadorDTO.setPage(0L);
            }
            if (paginadorDTO.getSize() == null) {
                paginadorDTO.setSize(3L);
            }

            if (paginadorDTO.getSort() != null) {
                sortField = paginadorDTO.getSort().split(",")[0];
                sortOrder = paginadorDTO.getSort().split(",")[1];

                if (sortField.equals("pessoa")) {
                    sortField = "pes.nome";
                }
                if (sortField.equals("matricula")) {
                    sortField = "cli.matricula ";
                }
                if (sortField.equals("contratoRenovado")) {
                    sortField = "contratoRenovado";
                }
                if (sortField.equals("verificadoEm")) {
                    sortField = "cli.verificadoem";
                }
                if (sortField.equals("usuarioVerificacao")) {
                    sortField = "usu.nome";
                }
            }

        }

        sql.append("ORDER BY ").append(sortField).append(" ").append(sortOrder).append("\n");

        return sql.toString();
    }

    private void setParametersConsultaClientesParaVerificar(Query query, FiltroBIDTO filtroBIDTO) {
        query.setParameter("codigoEmpresa", filtroBIDTO.getEmpresa());

        if (StringUtils.hasText(filtroBIDTO.getQuickSearchValue())) {
            QueryUtils.addQuickSearchParams(filtroBIDTO.getQuickSearchValue(), query);
        }

    }

}
