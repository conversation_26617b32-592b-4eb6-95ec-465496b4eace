package com.pacto.adm.core.dao.implementations;

import com.pacto.adm.core.dao.interfaces.ColetorDao;
import com.pacto.adm.core.entities.Coletor;
import com.pacto.adm.core.entities.LocalAcesso;
import org.springframework.stereotype.Repository;

import javax.persistence.Query;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public class ColetorDaoImpl extends DaoGenericoImpl<Coletor, Integer> implements ColetorDao {

    @Override
    public List<Coletor> findByLocaAcesso(Integer codigoLocalAcesso, boolean desativado) throws Exception {

        StringBuilder sql = new StringBuilder();

        sql.append("SELECT coletor FROM Coletor coletor WHERE coletor.codigo = "+codigoLocalAcesso +"and desativado = "+desativado);

        Query query = getCurrentSession().createQuery(sql.toString());

        if (query.getResultList().size() > 0) {
            return (List<Coletor>) query.getResultList();
        } else {
            return null;
        }
    }
}
