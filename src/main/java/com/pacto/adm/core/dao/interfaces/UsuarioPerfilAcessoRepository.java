package com.pacto.adm.core.dao.interfaces;

import com.pacto.adm.core.entities.UsuarioPerfilAcesso;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface UsuarioPerfilAcessoRepository extends JpaRepository<UsuarioPerfilAcesso, Integer> {

    @Query("SELECT case when COUNT(DISTINCT p.nomeEntidade) > 0 THEN true ELSE false END" +
            " FROM UsuarioPerfilAcesso upa\n" +
            "INNER JOIN upa.perfilAcesso pa\n" +
            "INNER JOIN Permissao p ON p.codPerfilAcesso = pa.codigo\n" +
            "WHERE p.nomeEntidade = :nomeEntidade AND upa.usuario.codigo = :usuario")
    boolean usuarioPossuiPermissaoByNomeEntidade(@Param("nomeEntidade") String nomeEntidade, @Param("usuario") Integer usuario);

}
