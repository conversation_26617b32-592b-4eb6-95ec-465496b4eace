package com.pacto.adm.core.dao.implementations;

import com.pacto.adm.core.dao.interfaces.QuestionarioClienteDao;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.adm.core.entities.QuestionarioCliente;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public class QuestionarioClienteDaoImpl extends DaoGenericoImpl<QuestionarioCliente, Integer> implements QuestionarioClienteDao {

    private static final int MAXIMO_RESULTADOS = 10;

    @Override
    public List<QuestionarioCliente> findByCliente(Integer codCliente, PaginadorDTO paginadorDTO) throws Exception {

        getCurrentSession().clear();

        int maxResults = MAXIMO_RESULTADOS;
        int indiceInicial = 0;

        if (paginadorDTO != null) {
            maxResults = paginadorDTO.getSize() == null ? MAXIMO_RESULTADOS : paginadorDTO.getSize().intValue();
            indiceInicial = paginadorDTO.getPage() == null ? indiceInicial : paginadorDTO.getPage().intValue() * maxResults;
        }

        StringBuilder where = new StringBuilder();
        Map<String, Object> params = new HashMap<>();
        where.append(" WHERE obj.cliente = :codCliente");

        params.put("codCliente", codCliente);

        if (paginadorDTO != null) {
            paginadorDTO.setQuantidadeTotalElementos(countWithParam("obj.codigo", where, params).longValue());
            paginadorDTO.setSize((long) maxResults);
            paginadorDTO.setPage((long) indiceInicial);
            if (paginadorDTO.getSort() != null && !paginadorDTO.getSort().isEmpty()) {
                String sortField = paginadorDTO.getSort().split(",")[0];
                String sortOrder = paginadorDTO.getSort().split(",")[1];
                if (sortField.equalsIgnoreCase("codigo")) {
                    where.append(" order by obj.questionario.nomeInterno " + sortOrder);
                } else if (sortField.equalsIgnoreCase("data")) {
                    where.append(" order by obj.data " + sortOrder);
                }
            } else {
                where.append(" order by obj.data desc ");
            }
        } else {
            where.append(" order by obj.data desc ");
        }
        return findByParam(where, params);
    }
}
