package com.pacto.adm.core.dao.implementations.nativerepositories.produto;

import com.pacto.adm.core.dao.interfaces.nativerepositories.produto.ProdutoNativeRepository;
import com.pacto.adm.core.dto.filtros.FiltroPlanoContaProdutoJSON;
import com.pacto.adm.core.entities.Produto;
import com.pacto.config.dto.PaginadorDTO;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import java.util.List;

@Repository
public class ProdutoNativeRepositoryImpl implements ProdutoNativeRepository {
    @PersistenceContext(unitName = "zwClientPU")
    private EntityManager entityManager;

    @Override
    public List<Produto> findAllProdutos(FiltroPlanoContaProdutoJSON filtroPlanoContaProdutoJSON, PaginadorDTO paginadorDTO) throws Exception {

        StringBuilder sql = new StringBuilder("SELECT p.codigo, p.descricao\n")
                .append("FROM produto p\n")
                .append("WHERE p.desativado = FALSE\n");

        String defaultTipoProdutoCondition = "AND p.tipoproduto NOT IN ('SE', 'PE','CD','DE','DV','RT','FR','DR','DC','AT','RD','CH','DS','HM')\n";

        if (filtroPlanoContaProdutoJSON == null) {
            sql.append(defaultTipoProdutoCondition);
        } else {
            if (filtroPlanoContaProdutoJSON.getSomenteServicos()) {
                sql.append("AND p.tipoproduto = 'SE'\n");
            } else {
                sql.append(defaultTipoProdutoCondition);
            }
            if (StringUtils.hasText(filtroPlanoContaProdutoJSON.getParametro())) {
                sql.append(" AND p.descricao LIKE :quickSearchValue\n");
            }
        }

        sql.append("ORDER BY p.descricao");

        Query query = entityManager.createNativeQuery(sql.toString(), "ProdutoCodigoDescricao");
        if (filtroPlanoContaProdutoJSON != null) {
            if (StringUtils.hasText(filtroPlanoContaProdutoJSON.getParametro())) {
                query.setParameter("quickSearchValue", filtroPlanoContaProdutoJSON);
            }
        }

        int page = 1;
        int size = 10;

        if (paginadorDTO != null) {
            if (paginadorDTO.getPage() == null) {
                paginadorDTO.setPage((long) page);
            }
            if (paginadorDTO.getSize() == null) {
                paginadorDTO.setSize((long) size);
            }
            page = paginadorDTO.getPage().intValue();
            if (page == 0) {
                page = 1;
            }
            size = paginadorDTO.getSize().intValue();
        }

        query.setMaxResults(size);
        query.setFirstResult((page - 1) * size);

        return query.getResultList();
    }
}
