package com.pacto.adm.core.dao.implementations;

import com.pacto.adm.core.dao.interfaces.ContratoModalidadeTurmaDao;
import com.pacto.adm.core.entities.contrato.ContratoModalidadeTurma;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public class ContratoModalidadeTurmaDaoImpl extends DaoGenericoImpl<ContratoModalidadeTurma, Integer> implements ContratoModalidadeTurmaDao {

    @Override
    public List<ContratoModalidadeTurma> findByCodigoContrato(Integer codContrato) throws Exception {
        StringBuilder where = new StringBuilder();
        Map<String, Object> params = new HashMap<>();

        where.append("JOIN ContratoModalidade cm on cm.codigo = obj.contratoModalidade.codigo ");
        where.append("WHERE cm.contrato.codigo = :codContrato");
        params.put("codContrato", codContrato);

        return findByParam(where, params);
    }
}
