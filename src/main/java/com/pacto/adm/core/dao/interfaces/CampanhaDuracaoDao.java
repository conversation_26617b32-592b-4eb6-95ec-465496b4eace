package com.pacto.adm.core.dao.interfaces;

import com.pacto.adm.core.entities.clubeDeVantagens.CampanhaDuracao;
import com.pacto.adm.core.enumerador.TipoItemCampanhaEnum;
import com.pacto.config.exceptions.ServiceException;

import java.util.Date;

public interface CampanhaDuracaoDao extends DaoGenerico<CampanhaDuracao, Integer> {
    CampanhaDuracao campanhaVigenteMultiplicador(Date hoje, TipoItemCampanhaEnum produto, Integer codigo) throws ServiceException;
}
