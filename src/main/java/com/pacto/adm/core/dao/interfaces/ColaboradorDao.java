package com.pacto.adm.core.dao.interfaces;

import com.pacto.adm.core.entities.Colaborador;
import com.pacto.adm.core.enumerador.SituacaoColaboradorEnum;
import com.pacto.adm.core.enumerador.TipoColaboradorEnum;

import java.util.List;


public interface ColaboradorDao extends DaoGenerico<Colaborador, Integer> {

    Colaborador findByPessoa(Integer codigoPessoa, Integer empresa) throws Exception;

    List<String> tiposDoColaborador(Integer codigoColaborador, Integer usuario) throws Exception;

    List<Colaborador> findColaboradorByTipoColaboradorAndSituacao(TipoColaboradorEnum[] arrayTipoColaborador,
                                                                  SituacaoColaboradorEnum situacaoColaboradorEnum,
                                                                  Integer codigoEmpresa) throws Exception;

}
