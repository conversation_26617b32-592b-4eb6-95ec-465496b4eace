package com.pacto.adm.core.dao.implementations;

import com.pacto.adm.core.dao.interfaces.AvisoInternoDao;
import com.pacto.adm.core.entities.AvisoInterno;
import com.pacto.adm.core.entities.Coletor;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import javax.persistence.Query;
import javax.persistence.TypedQuery;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public class AvisoInternoDaoImpl extends DaoGenericoImpl<AvisoInterno, Integer> implements AvisoInternoDao {

    @Override
    public List<AvisoInterno> findAllByVisivelParaTodos(boolean ativo, boolean visivelTodos) throws Exception {
        StringBuilder sql = new StringBuilder();

        sql.append("SELECT avisointerno FROM AvisoInterno avisointerno WHERE avisointerno.ativo = " + ativo + " and " + "avisointerno.visivelParaTodos = " + visivelTodos + " AND dataExpiracao >= CURRENT_DATE");

        Query query = getCurrentSession().createQuery(sql.toString());

        if (query.getResultList().size() > 0) {
            return (List<AvisoInterno>) query.getResultList();
        } else {
            return null;
        }
    }

    @Override
    public List<AvisoInterno> findAvisosByUsuario(Integer empresa, Integer codUsuario) throws Exception {
        StringBuilder hql = new StringBuilder();

        hql.append("SELECT ai FROM AvisoInterno ai ");
        hql.append("LEFT JOIN ai.destinatarios u ON u.codigo = :codUsuario ");
        hql.append("LEFT JOIN ai.perfis p ");
        hql.append("LEFT JOIN UsuarioPerfilAcesso upa ON upa.perfilAcesso = p.codigo ");
        hql.append("WHERE ai.ativo = true and ai.empresa = :empresa ");
        hql.append("AND ai.dataExpiracao >= CURRENT_DATE ");
        hql.append("AND (ai.visivelParaTodos = true ");
        hql.append("OR u.codigo IS NOT NULL ");
        hql.append("OR upa.usuario.codigo = :codUsuario) ");
        hql.append("ORDER BY ai.dataPublicacao DESC");

        Query query = getCurrentSession().createQuery(hql.toString());
        query.setParameter("codUsuario", codUsuario);
        query.setParameter("empresa", empresa);

        List<AvisoInterno> results = query.getResultList();

        return results.isEmpty() ? null : results;
    }

}
