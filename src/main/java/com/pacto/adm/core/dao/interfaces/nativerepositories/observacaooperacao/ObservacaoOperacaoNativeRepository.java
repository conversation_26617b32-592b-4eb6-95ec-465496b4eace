package com.pacto.adm.core.dao.interfaces.nativerepositories.observacaooperacao;

import com.pacto.adm.core.dto.filtros.FiltroBIControleOperacoesJSON;
import com.pacto.adm.core.dto.observacaooperacao.ObservacaoOperacaoTotaisDTO;
import com.pacto.adm.core.enumerador.TipoObservacaoOperacaoEnum;
import com.pacto.config.dto.PaginadorDTO;

public interface ObservacaoOperacaoNativeRepository {
    ObservacaoOperacaoTotaisDTO consultarPorNomeEntidadePorDataAlteracaoPorOperacao(FiltroBIControleOperacoesJSON filtros, PaginadorDTO paginadorDTO, boolean buscarComAdministrador, TipoObservacaoOperacaoEnum tipoObservacaoOperacaoEnum) throws Exception;
}
