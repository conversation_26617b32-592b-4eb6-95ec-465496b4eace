package com.pacto.adm.core.dao.implementations;

import com.pacto.adm.core.dao.interfaces.MovProdutoDao;
import com.pacto.adm.core.dao.interfaces.ReciboDevolucaoDao;
import com.pacto.adm.core.dto.filtros.FiltroMovProdutoJSON;
import com.pacto.adm.core.entities.MovProduto;
import com.pacto.adm.core.entities.Pessoa;
import com.pacto.adm.core.entities.Produto;
import com.pacto.adm.core.entities.contrato.Contrato;
import com.pacto.adm.core.entities.empresa.Empresa;
import com.pacto.adm.core.entities.recibodevolucao.ReciboDevolucao;
import com.pacto.adm.core.enumerador.TipoProdutoEnum;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.utils.Calendario;
import com.pacto.config.utils.UteisValidacao;
import org.hibernate.engine.spi.SessionImplementor;
import org.springframework.stereotype.Repository;

import javax.persistence.Query;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicLong;
import java.util.logging.Level;
import java.util.logging.Logger;

@Repository
public class MovProdutoDaoImpl extends DaoGenericoImpl<MovProduto, Integer> implements MovProdutoDao {

    private final ReciboDevolucaoDao reciboDevolucaoDao;
    private static final int MAXIMO_RESULTADOS = 10;

    public MovProdutoDaoImpl(ReciboDevolucaoDao reciboDevolucaoDao) {
        this.reciboDevolucaoDao = reciboDevolucaoDao;
    }

    @Override
    public List<MovProduto> consultarProdutoComValidadePorCodigoPessoa(Integer codPessoa, PaginadorDTO paginadorDTO) throws Exception {
        StringBuilder sql = new StringBuilder("SELECT mp.*, emp.nome as nomeEmpresa, emp.codigo as codigoEmpresa  \n"
                + "FROM MovProduto mp \n"
                + "INNER JOIN empresa emp on emp.codigo = mp.empresa\n"
                + "INNER JOIN produto p on p.codigo = mp.produto\n"
                + "WHERE mp.pessoa = " + codPessoa + "\n"
                + "AND ((p.tipoVigencia = 'ID' OR p.tipoVigencia = 'VV') and (p.tipoproduto in ('SE','AT'))\n" +
                "       OR (p.tipoproduto IN ('" + TipoProdutoEnum.DESAFIO.getCodigo() + "')))\n");
        List<MovProduto> lista = new ArrayList<>();
        try (SessionImplementor sessionImplementor = createSessionCurrentWork()) {
            sessionImplementor.doWork(con -> {
                if (paginadorDTO != null) {
                    try {
                        paginadorDTO.setQuantidadeTotalElementos(countResults(sql.toString()));
                    } catch (ServiceException e) {
                        throw new RuntimeException("Não consegui realizar o count!", e);
                    }
                    if (paginadorDTO.getSort() != null && !paginadorDTO.getSort().isEmpty()) {
                        String sort = paginadorDTO.getSort().split(",")[0].trim();
                        String orderDirection = paginadorDTO.getSort().split(",")[1];

                        if (sort.equalsIgnoreCase("codigo")) {
                            sql.append(" order by mp.codigo " + orderDirection);
                        } else if (sort.equalsIgnoreCase("contrato")) {
                            sql.append(" order by mp.contrato " + orderDirection);
                        } else if (sort.equalsIgnoreCase("empresa")) {
                            sql.append(" order by emp.nome " + orderDirection);
                        } else if (sort.equalsIgnoreCase("descricao")) {
                            sql.append(" order by mp.descricao " + orderDirection);
                        } else if (sort.equalsIgnoreCase("datalancamento")) {
                            sql.append(" order by mp.datalancamento " + orderDirection);
                        } else if (sort.equalsIgnoreCase("dataFinalVigencia")) {
                            sql.append(" order by mp.dataFinalVigencia " + orderDirection);
                        } else if (sort.equalsIgnoreCase("valordesconto")) {
                            sql.append(" order by mp.valordesconto " + orderDirection);
                        } else if (sort.equalsIgnoreCase("totalfinal")) {
                            sql.append(" order by mp.totalfinal " + orderDirection);
                        } else if (sort.equalsIgnoreCase("situacao")) {
                            sql.append(" order by mp.situacao " + orderDirection);
                        } else {
                            sql.append(" order by mp.datafinalvigencia desc ");
                        }
                    } else {
                        sql.append(" order by mp.datafinalvigencia desc ");
                    }
                    if (paginadorDTO.getPage() != null) {
                        sql.append(" offset " + paginadorDTO.getPage());
                    }
                    if (paginadorDTO.getSize() != null) {
                        sql.append(" limit " + paginadorDTO.getSize());
                    }
                }
                try (PreparedStatement sqlConsulta = con.prepareStatement(sql.toString())) {
                    ResultSet rs = sqlConsulta.executeQuery();
                    while (rs.next()) {
                        MovProduto movProduto = new MovProduto();
                        movProduto.setCodigo(rs.getInt("codigo"));
                        movProduto.setEmpresa(new Empresa());
                        movProduto.getEmpresa().setCodigo(rs.getInt("codigoEmpresa"));
                        movProduto.getEmpresa().setNome(rs.getString("nomeEmpresa"));
                        movProduto.setDataFinalVigencia(rs.getDate("dataFinalVigencia"));
                        movProduto.setContrato(new Contrato());
                        movProduto.getContrato().setCodigo(rs.getInt("contrato"));
                        movProduto.setDescricao(rs.getString("descricao"));
                        movProduto.setDataLancamento(rs.getTimestamp("datalancamento"));
                        movProduto.setPessoa(new Pessoa());
                        movProduto.getPessoa().setCodigo(rs.getInt("pessoa"));
                        movProduto.setQuantidade(rs.getInt("quantidade"));
                        movProduto.setPrecoUnitario(rs.getBigDecimal("precounitario"));
                        movProduto.setValorDesconto(rs.getBigDecimal("valordesconto"));
                        movProduto.setTotalFinal(rs.getBigDecimal("totalfinal"));
                        lista.add(movProduto);
                    }
                } catch (SQLException e) {
                    e.printStackTrace();
                    throw new RuntimeException("Erro ao executar o SQL", e);
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new RuntimeException("Erro ao montas o objeto com os dados da consulta", e);
                } finally {
                    con.close();
                }
            });
        }

        return lista;
    }

    @Override
    public List<MovProduto> findAllByPessoa(Integer codPessoa, FiltroMovProdutoJSON filtroMovProdutoJSON, PaginadorDTO paginadorDTO) throws Exception {
        StringBuilder sql = montarSqlProdutoCliente(codPessoa, null, filtroMovProdutoJSON);
        List<MovProduto> lista = new ArrayList<>();
        try (SessionImplementor sessionImplementor = createSessionCurrentWork()) {
            sessionImplementor.doWork(con -> {
                if (paginadorDTO != null) {
                    try {
                        paginadorDTO.setQuantidadeTotalElementos(countResults(sql.toString()));
                    } catch (ServiceException e) {
                        throw new RuntimeException("Não consegui realizar o count!", e);
                    }
                    if (paginadorDTO.getSort() != null && !paginadorDTO.getSort().isEmpty()) {
                        String sort = paginadorDTO.getSort().split(",")[0].trim();
                        String orderDirection = paginadorDTO.getSort().split(",")[1];

                        sql.append("order by ");
                        if (sort.equalsIgnoreCase("codigo")) {
                            sql.append("sql.codigo " + orderDirection);
                        } else if (sort.equalsIgnoreCase("contrato")) {
                            sql.append("sql.contrato " + orderDirection);
                        } else if (sort.equalsIgnoreCase("empresa")) {
                            sql.append("sql.empresa_nome " + orderDirection);
                        } else if (sort.equalsIgnoreCase("descricao")) {
                            sql.append("sql.descricao " + orderDirection);
                        } else if (sort.equalsIgnoreCase("datalancamento")) {
                            sql.append("sql.datalancamento " + orderDirection);
                        } else if (sort.equalsIgnoreCase("precounitario")) {
                            sql.append("sql.precounitario " + orderDirection);
                        } else if (sort.equalsIgnoreCase("valordesconto")) {
                            sql.append("sql.valordesconto " + orderDirection);
                        } else if (sort.equalsIgnoreCase("totalfinal")) {
                            sql.append("sql.totalfinal " + orderDirection);
                        } else if (sort.equalsIgnoreCase("situacao")) {
                            sql.append("sql.situacao " + orderDirection);
                        } else {
                            sql.append("sql.codigo " + orderDirection);
                        }
                    } else {
                        sql.append(" order by sql.codigo desc ");
                    }
                    if (paginadorDTO.getPage() != null) {
                        sql.append(" offset " + (paginadorDTO.getPage() * paginadorDTO.getSize()));
                    }
                    if (paginadorDTO.getSize() != null) {
                        sql.append(" limit " + paginadorDTO.getSize());
                    }
                } else {
                    sql.append(" order by sql.codigo desc ");
                }
                try (PreparedStatement sqlConsulta = con.prepareStatement(sql.toString())) {
                    ResultSet rs = sqlConsulta.executeQuery();
                    while (rs.next()) {
                        MovProduto movProduto = new MovProduto();
                        movProduto.setCodigo(rs.getInt("codigo"));
                        movProduto.setEmpresa(new Empresa());
                        movProduto.getEmpresa().setCodigo(rs.getInt("empresa"));
                        movProduto.getEmpresa().setNome(rs.getString("empresa_nome"));
                        movProduto.setContrato(new Contrato());
                        movProduto.getContrato().setCodigo(rs.getInt("contrato"));
                        movProduto.setDescricao(rs.getString("descricao"));
                        movProduto.setDataLancamento(rs.getTimestamp("datalancamento"));
                        movProduto.setPessoa(new Pessoa());
                        movProduto.getPessoa().setCodigo(rs.getInt("pessoa"));
                        movProduto.setQuantidade(rs.getInt("quantidade"));
                        movProduto.setPrecoUnitario(rs.getBigDecimal("precounitario"));
                        movProduto.setValorDesconto(rs.getBigDecimal("valordesconto"));
                        movProduto.setTotalFinal(rs.getBigDecimal("totalfinal"));
                        movProduto.setSituacao(rs.getString("situacao"));
                        movProduto.setProduto(new Produto());
                        movProduto.getProduto().setCodigo(rs.getInt("codigoproduto"));
                        movProduto.getProduto().setTipoProduto(rs.getString("tipoproduto"));
                        movProduto.setNumeroCupomDesconto(rs.getString("numeroCupomDesconto"));
                        movProduto.setStatusProtheus(rs.getString("statusprotheus"));
                        movProduto.setLinkNota(rs.getString("linknota"));
                        movProduto.setDataCancelamento(rs.getDate("datacancelamento"));
                        movProduto.setDataPagamento(rs.getDate("datapagamento"));
                        if (movProduto.getSituacao().equals("EA")) {
                            obterValorParcialmentPago(movProduto);
                        }
                        if (movProduto.getProduto().getTipoProduto().equals("SS") && movProduto.getSituacao().equals("CA")) {
                            try {
                                Optional<ReciboDevolucao> reciboDevolucao = reciboDevolucaoDao.findByParam(new String[]{"movProduto.codigo"}, new Object[]{movProduto.getCodigo()});
                                if (reciboDevolucao.isPresent()) {
                                    movProduto.setReciboDevolucao(reciboDevolucao.get());
                                } else {
                                    Logger.getLogger(DaoGenericoImpl.class.getName()).log(Level.SEVERE, "Nenhum recibo de devolução encontrato para a compra de código " + movProduto.getCodigo() + "!");
                                }
                            } catch (Exception ex) {
                                Logger.getLogger(DaoGenericoImpl.class.getName()).log(Level.SEVERE, "Nenhum recibo de devolução encontrato para a compra de código " + movProduto.getCodigo() + "!", ex);
                            }
                        }
                        lista.add(movProduto);
                    }
                } catch (SQLException e) {
                    e.printStackTrace();
                    throw new RuntimeException("Erro ao executar o SQL", e);
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new RuntimeException("Erro ao montas o objeto com os dados da consulta", e);
                } finally {
                    con.close();
                }
            });
        }

        return lista;
    }

    public void obterValorParcialmentPago(MovProduto produto) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT SUM(valorpago) AS valor \n");
        sql.append("FROM   movprodutoparcela mpp \n");
        sql.append("       INNER JOIN movparcela mp \n");
        sql.append("       ON     mp.codigo      = mpp.movparcela \n");
        sql.append("       AND    mpp.movproduto = ").append(produto.getCodigo()).append("\n");
        sql.append("       AND    mp.situacao LIKE 'PG'");
        try (SessionImplementor sessionImplementor = createSessionCurrentWork()) {
            sessionImplementor.doWork(con -> {

                try (PreparedStatement sqlConsulta = con.prepareStatement(sql.toString())) {
                    ResultSet rs = sqlConsulta.executeQuery();
                    if (rs.next()) {
                        produto.setValorParcialmentePago(rs.getBigDecimal("valor"));
                    }
                } catch (SQLException e) {
                    throw new RuntimeException("Erro ao executar o SQL", e);
                } finally {
                    con.close();
                }
            });
        }
    }

    private StringBuilder montarSqlProdutoCliente(Integer pessoa, Integer contrato, FiltroMovProdutoJSON filtroJSON) {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT  * FROM ( \n");
        sql.append(" SELECT \n");
        sql.append(" distinct on (movproduto.codigo) movproduto.codigo, movproduto.empresa, emp.nome as empresa_nome, movproduto.contrato, movproduto.descricao, datalancamento, pessoa, quantidade, precounitario, \n");
        sql.append(" valordesconto, totalfinal, situacao, p.tipoproduto, p.codigo as codigoproduto , numeroCupomDesconto, statusprotheus, linknota, oo.dataoperacao as datacancelamento, rp.data as dataPagamento, mparc.aulaavulsadiaria \n");
        sql.append(" FROM movproduto \n");
        sql.append(" INNER JOIN produto p ON p.codigo = movproduto.produto \n");
        sql.append(" INNER JOIN empresa emp ON movproduto.empresa = emp.codigo \n");
        sql.append(" LEFT JOIN movprodutoparcela mpp ON mpp.movproduto =  movproduto.codigo \n");
        sql.append(" LEFT JOIN movparcela mparc ON mparc.codigo = mpp.movparcela and mpp.movproduto = movproduto.codigo \n");
        sql.append(" LEFT JOIN recibopagamento rp on rp.codigo = mpp.recibopagamento\n");
        sql.append(" LEFT JOIN observacaooperacao oo ON oo.movparcela = mpp.movparcela \n");
        sql.append("                                AND oo.tipooperacao = 'PC' \n");
        sql.append(" WHERE pessoa = ").append(pessoa).append(" \n");
        sql.append("  AND p.codigo NOT IN (SELECT produto FROM perfileventoproduto) \n");
        if (!UteisValidacao.emptyNumber(contrato)) {
            sql.append(" AND movproduto.contrato = ").append(contrato);
        }
        if (filtroJSON != null &&
                filtroJSON.getIgnorarPlano() != null &&
                filtroJSON.getIgnorarPlano()) {
            sql.append(" AND p.tipoproduto not in ('").append(TipoProdutoEnum.MES_REFERENCIA_PLANO.getCodigo()).append("') \n");
        }
        sql.append(" ) as sql \n");
        return sql;
    }

    private Long countResults(String sql) throws ServiceException {
        try {
            AtomicLong totalResults = new AtomicLong();
            try (SessionImplementor sessionImplementor = createSessionCurrentWork()) {
                sessionImplementor.doWork(connection -> {
                    try {
                        StringBuilder sqlCount = new StringBuilder();
                        sqlCount.append(" SELECT count(sqlCount.*) as count FROM (");
                        sqlCount.append(sql);
                        sqlCount.append(") as sqlCount");
                        ResultSet resultSet = createStatement(connection, sqlCount.toString());
                        if (resultSet.next()) {
                            totalResults.set(resultSet.getLong("count"));
                        }
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    } finally {
                        connection.close();
                    }
                });
            }
            return totalResults.get();
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e);
        }
    }

    @Override
    public List<MovProduto> consultarPorCodigoContrato(Integer codigoContrato) throws Exception {
        getCurrentSession().clear();

        StringBuilder where = new StringBuilder();
        Map<String, Object> params = new HashMap<>();
        where.append("WHERE obj.contrato.codigo = :codigoContrato ");
        params.put("codigoContrato", codigoContrato);

        return findByParam(where, params);
    }

    @Override
    public MovProduto consultarPorCodigoVendaAvulsaRetornaCodigo(Integer codigoVendaAvulsa) throws Exception {
        getCurrentSession().clear();

        StringBuilder hql = new StringBuilder();
        Map<String, Object> params = new HashMap<>();
        hql.append(" SELECT obj FROM MovProduto obj");
        hql.append(" WHERE obj.vendaAvulsa.codigo = :codigoVendaAvulsa ");
        params.put("codigoVendaAvulsa", codigoVendaAvulsa);

        Query query = getCurrentSession().createQuery(hql.toString());
        if (params != null) {
            for (String p : params.keySet()) {
                query.setParameter(p, params.get(p));
            }
        }
        if (query.getResultList().size() > 0) {
            return (MovProduto) query.getResultList().get(0);
        } else {
            return null;
        }
    }

    @Override
    public List<MovProduto> consultarMovProdutosPorCodigoPessoa(Integer codPessoa, Integer codContrato,
                                                                FiltroMovProdutoJSON filtroMovProdutoJSON,
                                                                PaginadorDTO paginadorDTO) throws Exception {
        getCurrentSession().clear();

        int maxResults = MAXIMO_RESULTADOS;
        int indiceInicial = 0;

        if (paginadorDTO != null) {
            maxResults = paginadorDTO.getSize() == null ? MAXIMO_RESULTADOS : paginadorDTO.getSize().intValue();
            indiceInicial = paginadorDTO.getPage() == null ? indiceInicial : paginadorDTO.getPage().intValue() * maxResults;
        }

        StringBuilder where = new StringBuilder();
        Map<String, Object> params = new HashMap<>();

        where.append("WHERE obj.pessoa.codigo = :codigoPessoa \n");
        params.put("codigoPessoa", codPessoa);

        if (!UteisValidacao.emptyNumber(codContrato)) {
            where.append("AND obj.contrato.codigo = :codigoContrato \n");
            params.put("codigoContrato", codContrato);
        }

        if (filtroMovProdutoJSON != null) {
            if (!UteisValidacao.emptyString(filtroMovProdutoJSON.getSituacao())) {
                where.append("AND obj.situacao = :situacao \n");
                params.put("situacao", filtroMovProdutoJSON.getSituacao());
            }

            if (filtroMovProdutoJSON.getProdutosEstoque() != null &&
                    filtroMovProdutoJSON.getProdutosEstoque()) {
                where.append("AND obj.produto.tipoProduto = :tipoProduto \n");
                params.put("tipoProduto", TipoProdutoEnum.PRODUTO_ESTOQUE.getCodigo());
            }
            if (filtroMovProdutoJSON.getProdutoServico() != null &&
                    filtroMovProdutoJSON.getProdutoServico()) {
                where.append("AND obj.produto.tipoProduto = :tipoProduto \n");
                params.put("tipoProduto", TipoProdutoEnum.SERVICO.getCodigo());
            }
            if (filtroMovProdutoJSON.getProdutosVencidos() != null &&
                    filtroMovProdutoJSON.getProdutosVencidos()) {
                where.append("AND obj.dataInicioVigencia is not null \n");
                where.append("AND obj.dataFinalVigencia is not null \n");
                where.append("AND obj.dataFinalVigencia < '").append(Calendario.getDataAplicandoFormatacao(Calendario.hoje(), "yyyy-MM-dd")).append("' \n");
                where.append("AND ((obj.produto.tipoVigencia = 'ID' OR obj.produto.tipoVigencia = 'VV') and (obj.produto.tipoProduto in ('SE','AT')) " +
                        "OR (obj.produto.tipoProduto IN ('").append(TipoProdutoEnum.DESAFIO.getCodigo()).append("'))) \n");
            }
            if (filtroMovProdutoJSON.getProdutosComVencimento() != null &&
                    filtroMovProdutoJSON.getProdutosComVencimento()) {
                where.append("AND ((obj.produto.tipoVigencia = 'ID' OR obj.produto.tipoVigencia = 'VV') and (obj.produto.tipoProduto in ('SE','AT')) " +
                        "OR (obj.produto.tipoProduto IN ('").append(TipoProdutoEnum.DESAFIO.getCodigo()).append("'))) \n");
            }

            if (!UteisValidacao.emptyString(filtroMovProdutoJSON.getParametro())) {
                where.append("AND ( \n");
                where.append("upper(obj.descricao) like '%").append(filtroMovProdutoJSON.getParametro().toUpperCase()).append("%' \n");
                where.append("or \n");
                where.append("upper(obj.produto.descricao) like '%").append(filtroMovProdutoJSON.getParametro().toUpperCase()).append("%' \n");
                where.append("or \n");
                where.append("upper(obj.empresa.nome) like '%").append(filtroMovProdutoJSON.getParametro().toUpperCase()).append("%' \n");

                boolean somenteNumeros = UteisValidacao.somenteNumeros(filtroMovProdutoJSON.getParametro());
                if (somenteNumeros) {
                    where.append("or \n");
                    where.append("obj.codigo = ").append(filtroMovProdutoJSON.getParametro()).append(" \n");
                    where.append("or \n");
                    where.append("obj.contrato.codigo = ").append(filtroMovProdutoJSON.getParametro()).append(" \n");
                }
                where.append(") \n");
            }
        }

        if (paginadorDTO != null) {
            paginadorDTO.setQuantidadeTotalElementos(countWithParam("obj.codigo", where, params).longValue());
            paginadorDTO.setSize((long) maxResults);
            paginadorDTO.setPage((long) indiceInicial);
            if (paginadorDTO.getSort() != null && !paginadorDTO.getSort().isEmpty()) {
                String sortField = paginadorDTO.getSort().split(",")[0];
                String sortOrder = paginadorDTO.getSort().split(",")[1];

                if (sortField.equalsIgnoreCase("codigo")) {
                    where.append(" order by obj.codigo " + sortOrder);
                } else if (sortField.equalsIgnoreCase("contrato")) {
                    where.append(" order by obj.contrato.codigo " + sortOrder);
                } else if (sortField.equalsIgnoreCase("descricao")) {
                    where.append(" order by obj.descricao " + sortOrder);
                } else if (sortField.equalsIgnoreCase("produto")) {
                    where.append(" order by obj.produto.descricao " + sortOrder);
                } else if (sortField.equalsIgnoreCase("empresa")) {
                    where.append(" order by obj.empresa.nome " + sortOrder);
                } else if (sortField.equalsIgnoreCase("dataLancamento")) {
                    where.append(" order by obj.dataLancamento " + sortOrder);
                } else if (sortField.equalsIgnoreCase("precoUnitario")) {
                    where.append(" order by obj.precoUnitario " + sortOrder);
                } else if (sortField.equalsIgnoreCase("valorDesconto")) {
                    where.append(" order by obj.valorDesconto " + sortOrder);
                } else if (sortField.equalsIgnoreCase("totalFinal")) {
                    where.append(" order by obj.totalFinal " + sortOrder);
                } else if (sortField.equalsIgnoreCase("situacao")) {
                    where.append(" order by obj.situacao " + sortOrder);
                } else if (sortField.equalsIgnoreCase("renovavelAutomaticamente")) {
                    where.append(" order by obj.renovavelAutomaticamente " + sortOrder);
                } else if (sortField.equalsIgnoreCase("dataInicioVigencia")) {
                    where.append(" order by obj.dataInicioVigencia " + sortOrder);
                } else if (sortField.equalsIgnoreCase("dataFinalVigencia")) {
                    where.append(" order by obj.dataFinalVigencia " + sortOrder);
                }
            } else {
                where.append(" order by obj.codigo desc");
            }
        } else {
            where.append(" order by obj.codigo desc");
        }

        return findByParam(where, params, maxResults, indiceInicial);
    }

    @Override
    public Integer consultarPorCodigoMovProdutoRetornaAulaAvulsaDiaria(Integer codigoMovProduto) throws Exception {
        getCurrentSession().clear();

        StringBuilder hql = new StringBuilder();
        Map<String, Object> params = new HashMap<>();
        hql.append(" SELECT mp.aulaAvulsaDiaria.codigo FROM MovProduto obj");
        hql.append(" INNER JOIN MovProdutoParcela mpp ON mpp.movProduto.codigo = obj.codigo ");
        hql.append(" INNER JOIN MovParcela mp ON mp.codigo = mpp.movParcela.codigo and mpp.movProduto.codigo = obj.codigo ");
        hql.append(" WHERE obj.codigo = :codigoMovProduto ");
        hql.append(" AND mp.aulaAvulsaDiaria IS NOT NULL ");
        params.put("codigoMovProduto", codigoMovProduto);

        Query query = getCurrentSession().createQuery(hql.toString());
        if (params != null) {
            for (String p : params.keySet()) {
                query.setParameter(p, params.get(p));
            }
        }
        if (query.getResultList().size() > 0) {
            return (Integer) query.getResultList().get(0);
        } else {
            return null;
        }
    }
}
