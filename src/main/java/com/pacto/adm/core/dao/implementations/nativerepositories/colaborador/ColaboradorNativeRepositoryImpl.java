package com.pacto.adm.core.dao.implementations.nativerepositories.colaborador;

import com.pacto.adm.core.dao.interfaces.nativerepositories.colaborador.ColaboradorNativeRepository;
import com.pacto.adm.core.dto.PessoaDTO;
import com.pacto.adm.core.dto.colaborador.ColaboradorAniversarianteDTO;
import com.pacto.adm.core.dto.filtros.bi.FiltroBIDTO;
import com.pacto.adm.core.util.QueryUtils;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.utils.Uteis;
import com.pacto.config.utils.UteisValidacao;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import javax.persistence.Tuple;
import java.math.BigInteger;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.stream.Collectors;

@Repository
public class ColaboradorNativeRepositoryImpl implements ColaboradorNativeRepository {

    @PersistenceContext(unitName = "zwClientPU")
    private EntityManager entityManager;

    @Override
    public List<ColaboradorAniversarianteDTO> colaboradorAniversariante(FiltroBIDTO filtroBIDTO, PaginadorDTO paginadorDTO) throws Exception {

        if (filtroBIDTO == null) {
            throw new Exception("Não foi informado nenhum filtro!");
        }

        if (filtroBIDTO.getFim() == null) {
            throw new Exception("Não foi informado o filtro de data!");
        }

        String sql = sqlColaboradorAniversariante(filtroBIDTO, paginadorDTO);

        Query query = entityManager.createNativeQuery(sql, Tuple.class);
        setParametersColaboradorAniversariante(query, filtroBIDTO);
        QueryUtils.setQueryPagination(query, paginadorDTO);

        List<Tuple> resultado = query.getResultList();

        List<ColaboradorAniversarianteDTO> aniversariantes = resultado.stream().map(
                tuple -> new ColaboradorAniversarianteDTO(
                        tuple.get("codigoColaborador", Integer.class),
                        new PessoaDTO(
                                tuple.get("pessoa", Integer.class),
                                tuple.get("nome", String.class)
                        ),
                        tuple.get("situacao", String.class),
                        tuple.get("cpf", String.class)
                )
        ).collect(Collectors.toList());

        if (paginadorDTO != null) {
            Query countQuery = entityManager.createNativeQuery("select count(sqlCount.*) from (" + sql + ") as sqlCount");
            setParametersColaboradorAniversariante(countQuery, filtroBIDTO);
            paginadorDTO.setQuantidadeTotalElementos(((BigInteger) countQuery.getSingleResult()).longValue());
        }

        return aniversariantes;
    }

    private String sqlColaboradorAniversariante(FiltroBIDTO filtroBIDTO, PaginadorDTO paginadorDTO) throws Exception {
        StringBuilder sql = new StringBuilder();

        sql.append("SELECT col.codigo AS codigoColaborador, col.pessoa, p.nome as nome, col.situacao, p.cfp AS cpf\n");
        sql.append("FROM Colaborador col\n");
        sql.append("LEFT JOIN Pessoa p ON col.pessoa = p.codigo\n");
        sql.append("WHERE DATE_PART('MONTH',p.datanasc) = :mes\n");
        sql.append("AND DATE_PART('DAY',p.datanasc) = :dia\n");

        if (!UteisValidacao.emptyNumber(filtroBIDTO.getEmpresa())) {
            sql.append("AND col.empresa = :codigoEmpresa\n");
        }

        if (StringUtils.hasText(filtroBIDTO.getQuickSearchValue())) {
            sql.append("AND ");
            QueryUtils.buildSqlQuickSearchByType(sql, new LinkedHashMap<String, Class<?>>() {{
                put("p.nome", null);
                put("col.situacao", null);
                put("p.cfp", null);
            }});
        }

        String sortField = "p.nome";
        String sortOrder = "ASC";

        if (paginadorDTO != null) {
            if (paginadorDTO.getPage() == null) {
                paginadorDTO.setPage(0L);
            }
            if (paginadorDTO.getSize() == null) {
                paginadorDTO.setSize(3L);
            }

            if (paginadorDTO.getSort() != null) {
                sortField = paginadorDTO.getSort().split(",")[0];
                sortOrder = paginadorDTO.getSort().split(",")[1];

                if (sortField.equals("pessoa")) {
                    sortField = "p.nome";
                }
                if (sortField.equals("situacao")) {
                    sortField = "col.situacao";
                }
                if (sortField.equals("cpf")) {
                    sortField = "p.cfp";
                }
            }
        }

        sql.append("ORDER BY ").append(sortField).append(" ").append(sortOrder).append("\n");

        return sql.toString();
    }

    private void setParametersColaboradorAniversariante(Query query, FiltroBIDTO filtroBIDTO) {
        int dia = Uteis.getDiaMesData(filtroBIDTO.getFim());
        int mes = Uteis.getMesData(filtroBIDTO.getFim());
        if (!UteisValidacao.emptyNumber(filtroBIDTO.getEmpresa())) {
            query.setParameter("codigoEmpresa", filtroBIDTO.getEmpresa());
        }

        query.setParameter("dia", dia);
        query.setParameter("mes", mes);

        if (StringUtils.hasText(filtroBIDTO.getQuickSearchValue())) {
            QueryUtils.addQuickSearchParams(filtroBIDTO.getQuickSearchValue(), query);
        }
    }
}
