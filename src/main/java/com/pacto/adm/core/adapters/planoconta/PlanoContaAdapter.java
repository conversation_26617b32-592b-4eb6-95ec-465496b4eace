package com.pacto.adm.core.adapters.planoconta;

import com.pacto.adm.core.adapters.AdapterInterface;
import com.pacto.adm.core.dto.planoconta.PlanoContaDTO;
import com.pacto.adm.core.entities.PlanoConta;
import org.springframework.stereotype.Component;

@Component
public class PlanoContaAdapter implements AdapterInterface<PlanoConta, PlanoContaDTO> {

    @Override
    public PlanoContaDTO toDto(PlanoConta planoConta) {
        if (planoConta == null) {
            return null;
        }
        return new PlanoContaDTO(
                planoConta.getCodigo(),
                planoConta.getNome(),
                planoConta.getInsideltv()
        );
    }

    @Override
    public PlanoConta toEntity(PlanoContaDTO planoContaDTO) {
        if (planoContaDTO == null) {
            return null;
        }
        return new PlanoConta(
                planoContaDTO.getCodigo(),
                planoContaDTO.getNome(),
                planoContaDTO.getInsideltv());
    }
}
