package com.pacto.adm.core.adapters.classificacao;

import com.pacto.adm.core.adapters.AdapterInterface;
import com.pacto.adm.core.dto.classificacao.ClassificacaoDTO;
import com.pacto.adm.core.entities.Classificacao;
import org.springframework.stereotype.Component;

@Component
public class ClassificacaoAdapter implements AdapterInterface<Classificacao, ClassificacaoDTO> {

    @Override
    public ClassificacaoDTO toDto(Classificacao classificacao) {
        return new ClassificacaoDTO(
                classificacao.getCodigo(),
                classificacao.getNome(),
                classificacao.getEnviarsmsautomatico()
        );
    }

    @Override
    public Classificacao toEntity(ClassificacaoDTO classificacaoDTO) {
        return new Classificacao(
                classificacaoDTO.getCodigo(),
                classificacaoDTO.getNome(),
                classificacaoDTO.getEnviarsmsautomatico()
        );
    }
}
