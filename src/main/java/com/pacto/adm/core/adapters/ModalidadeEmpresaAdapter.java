package com.pacto.adm.core.adapters;

import com.pacto.adm.core.dao.interfaces.EmpresaDao;
import com.pacto.adm.core.dao.interfaces.ModalidadeDao;
import com.pacto.adm.core.dto.modalidade.EmpresaResumidaDTO;
import com.pacto.adm.core.dto.modalidade.ModalidadeEmpresaDTO;
import com.pacto.adm.core.entities.contrato.ModalidadeEmpresa;
import org.springframework.stereotype.Component;

@Component
public class ModalidadeEmpresaAdapter implements AdapterInterface<ModalidadeEmpresa, ModalidadeEmpresaDTO>{

    private final EmpresaDao empresaDao;
    private final ModalidadeDao modalidadeDao;

    public ModalidadeEmpresaAdapter(EmpresaDao empresaDao, ModalidadeDao modalidadeDao) {
        this.empresaDao = empresaDao;
        this.modalidadeDao = modalidadeDao;
    }

    @Override
    public ModalidadeEmpresa toEntity(ModalidadeEmpresaDTO modalidadeEmpresaDTO) {
        ModalidadeEmpresa modalidadeEmpresa = new ModalidadeEmpresa();
        modalidadeEmpresa = toEntity(modalidadeEmpresaDTO, modalidadeEmpresa);
        return modalidadeEmpresa;
    }

    @Override
    public ModalidadeEmpresa toEntity(ModalidadeEmpresaDTO modalidadeEmpresaDTO, ModalidadeEmpresa modalidadeEmpresa) {
        try {
            modalidadeEmpresa.setCodigo(modalidadeEmpresaDTO.getCodigo());
            modalidadeEmpresa.setEmpresa(empresaDao.findById(modalidadeEmpresaDTO.getEmpresa()));
            if (modalidadeEmpresaDTO.getModalidade() != null) {
                modalidadeEmpresa.setModalidade(modalidadeDao.findById(modalidadeEmpresaDTO.getModalidade()));
            }
            return modalidadeEmpresa;
        } catch (Exception e) {
            modalidadeEmpresa.setEmpresa(null);
            modalidadeEmpresa.setModalidade(null);
            return modalidadeEmpresa;
        }
    }

    @Override
    public ModalidadeEmpresaDTO toDto(ModalidadeEmpresa modalidadeEmpresa) {

        ModalidadeEmpresaDTO modalidadeEmpresaDTO = new ModalidadeEmpresaDTO();
        modalidadeEmpresaDTO.setCodigo(modalidadeEmpresa.getCodigo());
        modalidadeEmpresaDTO.setEmpresa(modalidadeEmpresa.getEmpresa().getCodigo());
        modalidadeEmpresaDTO.setNomeEmpresa(modalidadeEmpresa.getEmpresa().getNome());
        modalidadeEmpresaDTO.setModalidade(modalidadeEmpresa.getModalidade().getCodigo());

        return modalidadeEmpresaDTO;
    }
}
