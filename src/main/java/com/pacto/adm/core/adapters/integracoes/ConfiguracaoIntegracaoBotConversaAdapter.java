package com.pacto.adm.core.adapters.integracoes;

import com.pacto.adm.core.adapters.AdapterInterface;
import com.pacto.adm.core.dto.empresa.EmpresaDTO;
import com.pacto.adm.core.dto.integracoes.ConfiguracaoIntegracaoBotConversaDTO;
import com.pacto.adm.core.entities.ConfiguracaoIntegracaoBotConversa;
import org.springframework.stereotype.Component;

@Component
public class ConfiguracaoIntegracaoBotConversaAdapter implements AdapterInterface<ConfiguracaoIntegracaoBotConversa, ConfiguracaoIntegracaoBotConversaDTO> {

    @Override
    public ConfiguracaoIntegracaoBotConversaDTO toDto(ConfiguracaoIntegracaoBotConversa configEntity) {
            ConfiguracaoIntegracaoBotConversaDTO configuracaoIntegracaoBotConversaDTO = new ConfiguracaoIntegracaoBotConversaDTO();
            configuracaoIntegracaoBotConversaDTO.setCodigo(configEntity.getCodigo());
            configuracaoIntegracaoBotConversaDTO.setAtivo(configEntity.getAtivo());
            configuracaoIntegracaoBotConversaDTO.setTipoFluxo(configEntity.getTipoFluxo());
            configuracaoIntegracaoBotConversaDTO.setDescricao(configEntity.getDescricao());
            configuracaoIntegracaoBotConversaDTO.setUrlWebHooBotConversa(configEntity.getUrlWebHooBotConversa());
            configuracaoIntegracaoBotConversaDTO.setEmpresa(configEntity.getEmpresa());
            configuracaoIntegracaoBotConversaDTO.setFase(configEntity.getFase());
            return configuracaoIntegracaoBotConversaDTO;
    }

    public ConfiguracaoIntegracaoBotConversa toEntity(ConfiguracaoIntegracaoBotConversaDTO configDto) {
        if (configDto != null ) {
            try {
                ConfiguracaoIntegracaoBotConversa  configEntity = new ConfiguracaoIntegracaoBotConversa();
                configEntity.setCodigo(configDto.getCodigo());
                configEntity.setAtivo(configDto.getAtivo());
                configEntity.setDescricao(configDto.getDescricao());
                configEntity.setUrlWebHooBotConversa(configDto.getUrlWebHooBotConversa());
                configEntity.setEmpresa(configDto.getEmpresa());
                configEntity.setFase(configDto.getFase());
                configEntity.setTipoFluxo(configDto.getTipoFluxo());
                return configEntity;
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return null;
    }
}
