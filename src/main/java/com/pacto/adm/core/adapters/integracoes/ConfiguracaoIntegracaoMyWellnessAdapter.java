package com.pacto.adm.core.adapters.integracoes;

import com.pacto.adm.core.adapters.AdapterInterface;
import com.pacto.adm.core.dto.integracoes.ConfiguracaoIntegracaoMyWellnessDTO;
import com.pacto.adm.core.dto.empresa.EmpresaDTO;
import com.pacto.adm.core.entities.empresa.Empresa;
import org.springframework.stereotype.Component;

@Component
public class ConfiguracaoIntegracaoMyWellnessAdapter implements AdapterInterface<Empresa, ConfiguracaoIntegracaoMyWellnessDTO> {

    @Override
    public ConfiguracaoIntegracaoMyWellnessDTO toDto(Empresa empresa) {
        if (empresa != null) {
            ConfiguracaoIntegracaoMyWellnessDTO configuracaoIntegracaoMyWellnessDTO = new ConfiguracaoIntegracaoMyWellnessDTO();
            configuracaoIntegracaoMyWellnessDTO.setHabilitada(empresa.isIntegracaoMyWellneHabilitada());
            configuracaoIntegracaoMyWellnessDTO.setEnviarVinculos(empresa.isIntegracaoMyWellnessEnviarVinculos());
            configuracaoIntegracaoMyWellnessDTO.setEnviarGrupos(empresa.isIntegracaoMyWellnessEnviarGrupos());
            configuracaoIntegracaoMyWellnessDTO.setFacilityUrl(empresa.getIntegracaoMyWellnessFacilityUrl());
            configuracaoIntegracaoMyWellnessDTO.setApiKey(empresa.getIntegracaMyWellneApiKey());
            configuracaoIntegracaoMyWellnessDTO.setUser(empresa.getIntegracaoMyWellnessUser());
            configuracaoIntegracaoMyWellnessDTO.setPassword(empresa.getIntegracaoMyWellnessPassword());
            configuracaoIntegracaoMyWellnessDTO.setNrDiasVigenciaMyWellnessGymPass(empresa.getNrDiasVigenciaMyWellnessGymPass());
            configuracaoIntegracaoMyWellnessDTO.setTipoVigenciaMyWellnessGympass(empresa.getTipoVigenciaMyWellnessGympass());
            EmpresaDTO empresaDTO = new EmpresaDTO();
            empresaDTO.setCodigo(empresa.getCodigo());
            empresaDTO.setNome(empresa.getNome());
            configuracaoIntegracaoMyWellnessDTO.setEmpresa(empresaDTO);
            return configuracaoIntegracaoMyWellnessDTO;
        }
        return null;
    }

    public Empresa toEntity(ConfiguracaoIntegracaoMyWellnessDTO configMyWellnessDTO, Empresa empresa) {
        if (configMyWellnessDTO != null && configMyWellnessDTO.getEmpresa() != null) {
            try {
                empresa.setIntegracaoMyWellneHabilitada(configMyWellnessDTO.isHabilitada());
                empresa.setIntegracaoMyWellnessFacilityUrl(configMyWellnessDTO.getFacilityUrl());
                empresa.setIntegracaoMyWellnessEnviarVinculos(configMyWellnessDTO.isEnviarVinculos());
                empresa.setIntegracaoMyWellnessEnviarGrupos(configMyWellnessDTO.isEnviarGrupos());
                empresa.setIntegracaMyWellneApiKey(configMyWellnessDTO.getApiKey());
                empresa.setIntegracaoMyWellnessUser(configMyWellnessDTO.getUser());
                empresa.setIntegracaoMyWellnessPassword(configMyWellnessDTO.getPassword());
                empresa.setNrDiasVigenciaMyWellnessGymPass(configMyWellnessDTO.getNrDiasVigenciaMyWellnessGymPass());
                empresa.setTipoVigenciaMyWellnessGympass(configMyWellnessDTO.getTipoVigenciaMyWellnessGympass());
                return empresa;
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return null;
    }
}
