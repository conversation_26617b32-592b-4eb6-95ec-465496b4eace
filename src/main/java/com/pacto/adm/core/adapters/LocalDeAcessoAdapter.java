package com.pacto.adm.core.adapters;

import com.pacto.adm.core.adapters.empresa.EmpresaAdapter;
import com.pacto.adm.core.adapters.empresa.EmpresaLocalDeAcessoAdapter;
import com.pacto.adm.core.dto.LocalDeAcessoDTO;
import com.pacto.adm.core.entities.LocalAcesso;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class LocalDeAcessoAdapter implements AdapterInterface<LocalAcesso, LocalDeAcessoDTO> {
    @Autowired
    EmpresaLocalDeAcessoAdapter empresaLocalDeAcessoAdapter;

    @Override
    public LocalAcesso toEntity(LocalDeAcessoDTO localAcessoDTO) {
        if (localAcessoDTO != null) {
            LocalAcesso localAcesso = new LocalAcesso();
            localAcesso.setCodigo(localAcessoDTO.getCodigo());
            localAcesso.setDescricao(localAcessoDTO.getDescricao());
//            localAcesso.setEmpresa(empresaLocalDeAcessoAdapter.toEntity(localAcessoDTO.getEmpresa()));
            return localAcesso;
        }
        return null;
    }

    @Override
    public LocalDeAcessoDTO toDto(LocalAcesso localAcesso) {
        if (localAcesso != null) {
            LocalDeAcessoDTO localAcessoDTO = new LocalDeAcessoDTO();
            localAcessoDTO.setCodigo(localAcesso.getCodigo());
            localAcessoDTO.setDescricao(localAcesso.getDescricao());
//            localAcessoDTO.setEmpresa(empresaLocalDeAcessoAdapter.toDto(localAcesso.getEmpresa()));

            return localAcessoDTO;
        }
        return null;
    }
}
