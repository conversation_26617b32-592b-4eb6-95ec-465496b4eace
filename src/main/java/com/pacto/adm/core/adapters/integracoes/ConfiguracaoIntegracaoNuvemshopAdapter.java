package com.pacto.adm.core.adapters.integracoes;

import com.pacto.adm.core.adapters.AdapterInterface;
import com.pacto.adm.core.dto.integracoes.ConfiguracaoIntegracaoNuvemshopDTO;
import com.pacto.adm.core.entities.empresa.Empresa;
import org.springframework.stereotype.Component;

@Component
public class ConfiguracaoIntegracaoNuvemshopAdapter implements AdapterInterface<Empresa, ConfiguracaoIntegracaoNuvemshopDTO> {
    @Override
    public ConfiguracaoIntegracaoNuvemshopDTO toDto(Empresa empresa) {
        if (empresa != null) {
            ConfiguracaoIntegracaoNuvemshopDTO cfg= new ConfiguracaoIntegracaoNuvemshopDTO();
            cfg.setEmpresa(empresa.getCodigo());
            cfg.setHabilitada(empresa.isIntegracaoNuvemshopHabilitada());
            cfg.setAppName(empresa.getIntegracaoNuvemshopNomeApp());
            cfg.setEmail(empresa.getIntegracaoNuvemshopEmail());
            cfg.setAccessToken(empresa.getIntegracaoNuvemshopTokenAcesso());
            cfg.setStoreId(empresa.getIntegracaoNuvemshopStoreId());

            return cfg;
        }
        return null;
    }
}
