package com.pacto.adm.core.adapters;

import com.pacto.adm.core.dto.ArquivoDTO;
import com.pacto.adm.core.entities.Arquivo;
import com.pacto.adm.core.entities.Pessoa;
import com.pacto.adm.core.entities.solicitacaocompra.SolicitacaoCompra;
import com.pacto.config.utils.UteisValidacao;
import org.springframework.stereotype.Component;

@Component
public class ArquivoAdapter implements AdapterInterface<Arquivo, ArquivoDTO> {

    @Override
    public Arquivo toEntity(ArquivoDTO arquivoDTO) {
        if (arquivoDTO != null) {
            Arquivo arquivo = new Arquivo();
            arquivo.setCodigo(arquivoDTO.getCodigo());
            arquivo.setTipo(arquivoDTO.getTipo());
            arquivo.setNome(arquivoDTO.getNome());
            arquivo.setFotokey(arquivoDTO.getFotoKey());
            arquivo.setObservacao(arquivoDTO.getObservacao());
            if (!UteisValidacao.emptyNumber(arquivoDTO.getSolicitacaoCompra())) {
                arquivo.setSolicitacaoCompra(new SolicitacaoCompra());
                arquivo.getSolicitacaoCompra().setCodigo(arquivoDTO.getSolicitacaoCompra());
            }
            arquivo.setExtensao(arquivoDTO.getExtensao());
            if (arquivoDTO.getPessoa() != null) {
                Pessoa pessoa = new Pessoa();
                pessoa.setCodigo(arquivoDTO.getPessoa());
                arquivo.setPessoa(pessoa);
            } else {
                arquivo.setPessoa(null);
            }

            return arquivo;
        }
        return null;
    }

    @Override
    public ArquivoDTO toDto(Arquivo arquivo) {
        if (arquivo != null) {
            ArquivoDTO arquivoDTO = new ArquivoDTO();
            arquivoDTO.setCodigo(arquivo.getCodigo());
            arquivoDTO.setTipo(arquivo.getTipo());
            arquivoDTO.setNome(arquivo.getNome());
            arquivoDTO.setFotoKey(arquivo.getFotokey());
            arquivoDTO.setObservacao(arquivo.getObservacao());
            arquivoDTO.setExtensao(arquivo.getExtensao());
            arquivoDTO.setDataRegistro(arquivo.getDataRegistro() != null ? arquivo.getDataRegistro().getTime() : null);
            if(arquivo.getPessoa() != null){
                arquivoDTO.setPessoa(arquivo.getPessoa().getCodigo());
            } else {
                arquivoDTO.setPessoa(null);
            }
            return arquivoDTO;
        }
        return null;
    }
}
