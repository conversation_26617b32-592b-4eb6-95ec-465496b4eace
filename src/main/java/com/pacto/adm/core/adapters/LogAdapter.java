package com.pacto.adm.core.adapters;

import com.pacto.adm.core.dto.LogAlteracoesDTO;
import com.pacto.adm.core.dto.LogDTO;
import com.pacto.adm.core.entities.Log;
import com.pacto.config.utils.Calendario;
import com.pacto.config.utils.Uteis;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

@Component
public class LogAdapter implements AdapterInterface<Log, LogDTO> {

    private final ClienteAdapter clienteAdapter;

    public LogAdapter(ClienteAdapter clienteAdapter) {
        this.clienteAdapter = clienteAdapter;
    }

    @Override
    public LogDTO toDto(Log log) {
        LogDTO logDTO = new LogDTO();
        logDTO.setCodigo(log.getCodigo());
        logDTO.setNomeEntidade(log.getNomeEntidade());
        logDTO.setNomeEntidadeDescricao(log.getNomeEntidadeDescricao());
        logDTO.setChavePrimaria(log.getChavePrimaria());
        logDTO.setChavePrimariaEntidadeSubordinada(log.getChavePrimariaEntidadeSubordinada());
        logDTO.setNomeCampo(log.getNomeCampo());
        logDTO.setValorCampoAnterior(log.getValorCampoAnterior());
        logDTO.setValorCampoAlterado(log.getValorCampoAlterado());
        logDTO.setDataAlteracao(log.getDataAlteracao());
        logDTO.setResponsavelAlteracao(log.getResponsavelAlteracao());
        logDTO.setOperacao(log.getOperacao());
        logDTO.setPessoa(log.getPessoa());
        logDTO.setCliente(log.getCliente());
        logDTO.setOrigem(log.getOrigem());
        setCamposNovoLog(logDTO);
        if (log.getClienteEntity() != null) {
            logDTO.setClienteDTO(clienteAdapter.toDto(log.getClienteEntity()));
        }
        return logDTO;
    }

    public void setCamposNovoLog(LogDTO logDTO) {
        logDTO.setChave(logDTO.getChavePrimaria());
        logDTO.setUsuario(logDTO.getResponsavelAlteracao());
        logDTO.setDia(Uteis.getDataComHHMM(logDTO.getDataAlteracao()));
        logDTO.setHora(Calendario.getDataAplicandoFormatacao(logDTO.getDataAlteracao(), "HH:mm:ss"));
        logDTO.setIdentificador(StringUtils.isEmpty(logDTO.getNomeEntidadeDescricao()) ? logDTO.getNomeEntidade() : logDTO.getNomeEntidadeDescricao());
        if (!StringUtils.isEmpty(logDTO.getDescricao())) {
            logDTO.setDescricao(logDTO.getDescricao().replaceAll("\n", "<br />"));
        }
        List<LogAlteracoesDTO> alteracoes = new ArrayList<>();
        LogAlteracoesDTO alteracao = new LogAlteracoesDTO();
        alteracao.setCampo(logDTO.getNomeCampo());
        if (!StringUtils.isEmpty(logDTO.getValorCampoAlterado())) {
            alteracao.setValorAlterado(logDTO.getValorCampoAlterado().replaceAll("\n", "<br />"));
        }
        if (!StringUtils.isEmpty(logDTO.getValorCampoAnterior())) {
            alteracao.setValorAnterior(logDTO.getValorCampoAnterior().replaceAll("\n", "<br />"));
        }
        alteracoes.add(alteracao);
        logDTO.setAlteracoes(new ArrayList<>(alteracoes));
    }

    @Override
    public Log toEntity(LogDTO logDTO) {
        Log log = new Log();
        log.setCodigo(logDTO.getCodigo());
        log.setNomeEntidade(logDTO.getNomeEntidade());
        log.setNomeEntidadeDescricao(logDTO.getNomeEntidadeDescricao());
        log.setChavePrimaria(logDTO.getChavePrimaria());
        log.setChavePrimariaEntidadeSubordinada(logDTO.getChavePrimariaEntidadeSubordinada());
        log.setNomeCampo(logDTO.getNomeCampo());
        log.setValorCampoAnterior(logDTO.getValorCampoAnterior());
        log.setValorCampoAlterado(logDTO.getValorCampoAlterado());
        log.setDataAlteracao(logDTO.getDataAlteracao());
        log.setResponsavelAlteracao(logDTO.getResponsavelAlteracao());
        log.setOperacao(logDTO.getOperacao());
        log.setPessoa(logDTO.getPessoa());
        log.setCliente(logDTO.getCliente());
        return log;
    }
}
