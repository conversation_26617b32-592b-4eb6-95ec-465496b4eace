package com.pacto.adm.core.adapters.integracoes;

import com.pacto.adm.core.adapters.AdapterInterface;
import com.pacto.adm.core.dto.UsuarioDTO;
import com.pacto.adm.core.dto.empresa.EmpresaDTO;
import com.pacto.adm.core.dto.integracoes.ConfiguracaoIntegracaoGenericaLeadsDTO;
import com.pacto.adm.core.entities.ConfiguracaoIntegracaoGenericaLeads;
import com.pacto.adm.core.entities.Usuario;
import com.pacto.adm.core.entities.empresa.Empresa;
import com.pacto.config.utils.UteisValidacao;
import org.springframework.stereotype.Component;

@Component
public class ConfiguracaoIntegracaoGenericaLeadsAdapter implements AdapterInterface<ConfiguracaoIntegracaoGenericaLeads, ConfiguracaoIntegracaoGenericaLeadsDTO> {

    @Override
    public ConfiguracaoIntegracaoGenericaLeads toEntity(ConfiguracaoIntegracaoGenericaLeadsDTO configDTO) {
        if (configDTO != null) {
            ConfiguracaoIntegracaoGenericaLeads configEntity = new ConfiguracaoIntegracaoGenericaLeads();
            configEntity.setCodigo(configDTO.getCodigo());
            configEntity.setHabilitada(configDTO.getHabilitada());
            if (configDTO.getResponsavelPadrao() != null) {
                configEntity.setResponsavelPadrao(new Usuario());
                configEntity.getResponsavelPadrao().setCodigo(configDTO.getResponsavelPadrao().getCodigo());
            }
            configEntity.setHoraLimite(configDTO.getHoraLimite());
            configEntity.setAcaoObjecao(configDTO.getAcaoObjecao());
            configEntity.setEmpresa(new Empresa());
            configEntity.getEmpresa().setCodigo(configDTO.getEmpresa().getCodigo());
            return configEntity;
        }
        return null;
    }

    @Override
    public ConfiguracaoIntegracaoGenericaLeadsDTO toDto(ConfiguracaoIntegracaoGenericaLeads configEntity) {
        if (configEntity != null && !UteisValidacao.emptyNumber(configEntity.getCodigo())) {
            ConfiguracaoIntegracaoGenericaLeadsDTO configDto = new ConfiguracaoIntegracaoGenericaLeadsDTO();
            configDto.setCodigo(configEntity.getCodigo());
            configDto.setHabilitada(configEntity.getHabilitada());
            if (configEntity.getResponsavelPadrao() != null) {
                configDto.setResponsavelPadrao(new UsuarioDTO());
                configDto.getResponsavelPadrao().setCodigo(configEntity.getResponsavelPadrao().getCodigo());
                configDto.getResponsavelPadrao().setNome(configEntity.getResponsavelPadrao().getNome());
            }
            configDto.setHoraLimite(configEntity.getHoraLimite());
            configDto.setAcaoObjecao(configEntity.getAcaoObjecao());
            configDto.setEmpresa(new EmpresaDTO());
            configDto.getEmpresa().setCodigo(configEntity.getEmpresa().getCodigo());
            configDto.getEmpresa().setNome(configEntity.getEmpresa().getNome());
            return configDto;
        }
        return null;
    }
}
