package com.pacto.adm.core.adapters.tipoconviteaulaexperimental;

import com.pacto.adm.core.adapters.AdapterInterface;
import com.pacto.adm.core.adapters.empresa.EmpresaAdapter;
import com.pacto.adm.core.dto.tipoconviteaulaexperimental.TipoConviteAulaExperimentalDTO;
import com.pacto.adm.core.entities.tipoconviteaulaexperimental.TipoConviteAulaExperimental;
import org.springframework.stereotype.Component;

@Component
public class TipoConviteAulaExperimentalAdapter implements AdapterInterface<TipoConviteAulaExperimental, TipoConviteAulaExperimentalDTO> {

    private final EmpresaAdapter empresaAdapter;

    public TipoConviteAulaExperimentalAdapter(EmpresaAdapter empresaAdapter) {
        this.empresaAdapter = empresaAdapter;
    }

    @Override
    public TipoConviteAulaExperimentalDTO toDto(TipoConviteAulaExperimental tipoConviteAulaExperimental) {
        TipoConviteAulaExperimentalDTO tipoConviteAulaExperimentalDTO = new TipoConviteAulaExperimentalDTO();
        tipoConviteAulaExperimentalDTO.setCodigo(tipoConviteAulaExperimental.getCodigo());
        tipoConviteAulaExperimentalDTO.setAlunoPodeEnviarConvite(tipoConviteAulaExperimental.getAlunoPodeEnviarConvite());
        tipoConviteAulaExperimentalDTO.setAulasAgendadasSemDiasSeguido(tipoConviteAulaExperimental.getAulasAgendadasSemDiasSeguido());
        tipoConviteAulaExperimentalDTO.setColaboradorPodeEnviarConvite(tipoConviteAulaExperimental.getColaboradorPodeEnviarConvite());
        tipoConviteAulaExperimentalDTO.setDataLancamento(tipoConviteAulaExperimental.getDataLancamento());
        tipoConviteAulaExperimentalDTO.setDescricao(tipoConviteAulaExperimental.getDescricao());
        tipoConviteAulaExperimentalDTO.setQuantidadeAulaExperimental(tipoConviteAulaExperimental.getQuantidadeAulaExperimental());
        tipoConviteAulaExperimentalDTO.setQuantidadeConviteAlunoPodeEnviar(tipoConviteAulaExperimental.getQuantidadeConviteAlunoPodeEnviar());
        tipoConviteAulaExperimentalDTO.setVigenciaInicial(tipoConviteAulaExperimental.getVigenciaInicial());
        tipoConviteAulaExperimentalDTO.setVigenciaFinal(tipoConviteAulaExperimental.getVigenciaFinal());
        if (tipoConviteAulaExperimental.getEmpresa() != null) {
            tipoConviteAulaExperimentalDTO.setEmpresa(empresaAdapter.toDto(tipoConviteAulaExperimental.getEmpresa()));
        }
        return tipoConviteAulaExperimentalDTO;
    }

    @Override
    public TipoConviteAulaExperimental toEntity(TipoConviteAulaExperimentalDTO tipoConviteAulaExperimentalDTO) {
        TipoConviteAulaExperimental tipoConviteAulaExperimental = new TipoConviteAulaExperimental();
        tipoConviteAulaExperimental.setCodigo(tipoConviteAulaExperimentalDTO.getCodigo());
        tipoConviteAulaExperimental.setAlunoPodeEnviarConvite(tipoConviteAulaExperimentalDTO.getAlunoPodeEnviarConvite());
        tipoConviteAulaExperimental.setAulasAgendadasSemDiasSeguido(tipoConviteAulaExperimentalDTO.getAulasAgendadasSemDiasSeguido());
        tipoConviteAulaExperimental.setColaboradorPodeEnviarConvite(tipoConviteAulaExperimentalDTO.getColaboradorPodeEnviarConvite());
        tipoConviteAulaExperimental.setDataLancamento(tipoConviteAulaExperimentalDTO.getDataLancamento());
        tipoConviteAulaExperimental.setDescricao(tipoConviteAulaExperimentalDTO.getDescricao());
        tipoConviteAulaExperimental.setQuantidadeAulaExperimental(tipoConviteAulaExperimentalDTO.getQuantidadeAulaExperimental());
        tipoConviteAulaExperimental.setQuantidadeConviteAlunoPodeEnviar(tipoConviteAulaExperimentalDTO.getQuantidadeConviteAlunoPodeEnviar());
        tipoConviteAulaExperimental.setVigenciaInicial(tipoConviteAulaExperimentalDTO.getVigenciaInicial());
        tipoConviteAulaExperimental.setVigenciaFinal(tipoConviteAulaExperimentalDTO.getVigenciaFinal());
        if (tipoConviteAulaExperimentalDTO.getEmpresa() != null) {
            tipoConviteAulaExperimental.setEmpresa(empresaAdapter.toEntity(tipoConviteAulaExperimentalDTO.getEmpresa()));
        }
        return tipoConviteAulaExperimental;
    }
}
