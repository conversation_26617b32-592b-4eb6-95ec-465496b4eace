package com.pacto.adm.core.adapters.conviteaulaexperimental;

import com.pacto.adm.core.adapters.AdapterInterface;
import com.pacto.adm.core.adapters.ClienteAdapter;
import com.pacto.adm.core.adapters.UsuarioAdapter;
import com.pacto.adm.core.adapters.colaborador.ColaboradorAdapter;
import com.pacto.adm.core.adapters.indicado.IndicadoAdapter;
import com.pacto.adm.core.adapters.passivo.PassivoAdapter;
import com.pacto.adm.core.adapters.tipoconviteaulaexperimental.TipoConviteAulaExperimentalAdapter;
import com.pacto.adm.core.dto.conviteaulaexperimental.ConviteAulaExperimentalDTO;
import com.pacto.adm.core.entities.conviteaulaexperimental.ConviteAulaExperimental;
import org.springframework.stereotype.Component;

@Component
public class ConviteAulaExperimentalAdapter implements AdapterInterface<ConviteAulaExperimental, ConviteAulaExperimentalDTO> {

    private final UsuarioAdapter usuarioAdapter;
    private final TipoConviteAulaExperimentalAdapter tipoConviteAulaExperimentalAdapter;
    private final PassivoAdapter passivoAdapter;
    private final IndicadoAdapter indicadoAdapter;
    private final ClienteAdapter clienteAdapter;
    private final ColaboradorAdapter colaboradorAdapter;

    public ConviteAulaExperimentalAdapter(
            UsuarioAdapter usuarioAdapter, TipoConviteAulaExperimentalAdapter tipoConviteAulaExperimentalAdapter,
            PassivoAdapter passivoAdapter, IndicadoAdapter indicadoAdapter,
            ClienteAdapter clienteAdapter, ColaboradorAdapter colaboradorAdapter
    ) {
        this.usuarioAdapter = usuarioAdapter;
        this.tipoConviteAulaExperimentalAdapter = tipoConviteAulaExperimentalAdapter;
        this.passivoAdapter = passivoAdapter;
        this.indicadoAdapter = indicadoAdapter;
        this.clienteAdapter = clienteAdapter;
        this.colaboradorAdapter = colaboradorAdapter;
    }

    @Override
    public ConviteAulaExperimentalDTO toDto(ConviteAulaExperimental conviteAulaExperimental) {
        ConviteAulaExperimentalDTO conviteAulaExperimentalDTO = new ConviteAulaExperimentalDTO();
        conviteAulaExperimentalDTO.setCodigo(conviteAulaExperimental.getCodigo());
        conviteAulaExperimentalDTO.setDataLancamento(conviteAulaExperimental.getDataLancamento());
        conviteAulaExperimentalDTO.setDataValidacaoConvidado(conviteAulaExperimental.getDataValidacaoConvidado());
        if (conviteAulaExperimental.getClienteConvidado() != null) {
            conviteAulaExperimentalDTO.setClienteConvidado(clienteAdapter.toDto(
                    conviteAulaExperimental.getClienteConvidado())
            );
        }
        if (conviteAulaExperimental.getClienteConvidado() != null) {
            conviteAulaExperimentalDTO.setClienteConvidou(clienteAdapter.toDto(
                    conviteAulaExperimental.getClienteConvidou())
            );
        }
        if (conviteAulaExperimental.getClienteConvidado() != null) {
            conviteAulaExperimentalDTO.setClienteIndicadoOuPassivo(clienteAdapter.toDto(
                    conviteAulaExperimental.getClienteIndicadoOuPassivo())
            );
        }
        if (conviteAulaExperimental.getClienteConvidado() != null) {
            conviteAulaExperimentalDTO.setColaboradorResponsavelConvite(colaboradorAdapter.toDto(
                    conviteAulaExperimental.getColaboradorResponsavelConvite())
            );
        }
        if (conviteAulaExperimental.getClienteConvidado() != null) {
            conviteAulaExperimentalDTO.setIndicadoConvidado(indicadoAdapter.toDto(conviteAulaExperimental.getIndicadoConvidado()));
        }
        if (conviteAulaExperimental.getClienteConvidado() != null) {
            conviteAulaExperimentalDTO.setPassivoConvidado(passivoAdapter.toDto(conviteAulaExperimental.getPassivoConvidado()));
        }
        if (conviteAulaExperimental.getClienteConvidado() != null) {
            conviteAulaExperimentalDTO.setTipoConviteAulaExperimental(tipoConviteAulaExperimentalAdapter.toDto(
                    conviteAulaExperimental.getTipoConviteAulaExperimental()
            ));
        }
        if (conviteAulaExperimental.getClienteConvidado() != null) {
            conviteAulaExperimentalDTO.setUsuarioConvidou(usuarioAdapter.toDto(conviteAulaExperimental.getUsuarioConvidou()));
        }
        return conviteAulaExperimentalDTO;
    }

    @Override
    public ConviteAulaExperimental toEntity(ConviteAulaExperimentalDTO conviteAulaExperimentalDTO) {
        ConviteAulaExperimental conviteAulaExperimental = new ConviteAulaExperimental();
        conviteAulaExperimental.setCodigo(conviteAulaExperimentalDTO.getCodigo());
        conviteAulaExperimental.setDataLancamento(conviteAulaExperimentalDTO.getDataLancamento());
        conviteAulaExperimental.setDataValidacaoConvidado(conviteAulaExperimentalDTO.getDataValidacaoConvidado());
        if (conviteAulaExperimentalDTO.getClienteConvidado() != null) {
            conviteAulaExperimental.setClienteConvidado(clienteAdapter.toEntity(
                    conviteAulaExperimentalDTO.getClienteConvidado())
            );
        }
        if (conviteAulaExperimentalDTO.getClienteConvidado() != null) {
            conviteAulaExperimental.setClienteConvidou(clienteAdapter.toEntity(
                    conviteAulaExperimentalDTO.getClienteConvidou())
            );
        }
        if (conviteAulaExperimentalDTO.getClienteConvidado() != null) {
            conviteAulaExperimental.setClienteIndicadoOuPassivo(clienteAdapter.toEntity(
                    conviteAulaExperimentalDTO.getClienteIndicadoOuPassivo())
            );
        }
        if (conviteAulaExperimentalDTO.getClienteConvidado() != null) {
            conviteAulaExperimental.setColaboradorResponsavelConvite(colaboradorAdapter.toEntity(
                    conviteAulaExperimentalDTO.getColaboradorResponsavelConvite())
            );
        }
        if (conviteAulaExperimentalDTO.getClienteConvidado() != null) {
            conviteAulaExperimental.setIndicadoConvidado(indicadoAdapter.toEntity(conviteAulaExperimentalDTO.getIndicadoConvidado()));
        }
        if (conviteAulaExperimentalDTO.getClienteConvidado() != null) {
            conviteAulaExperimental.setPassivoConvidado(passivoAdapter.toEntity(conviteAulaExperimentalDTO.getPassivoConvidado()));
        }
        if (conviteAulaExperimentalDTO.getClienteConvidado() != null) {
            conviteAulaExperimental.setTipoConviteAulaExperimental(tipoConviteAulaExperimentalAdapter.toEntity(
                    conviteAulaExperimentalDTO.getTipoConviteAulaExperimental()
            ));
        }
        if (conviteAulaExperimentalDTO.getClienteConvidado() != null) {
            conviteAulaExperimental.setUsuarioConvidou(usuarioAdapter.toEntity(conviteAulaExperimentalDTO.getUsuarioConvidou()));
        }
        return conviteAulaExperimental;
    }
}
