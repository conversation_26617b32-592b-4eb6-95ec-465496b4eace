package com.pacto.adm.core.adapters.empresa;

import com.pacto.adm.core.adapters.AdapterInterface;
import com.pacto.adm.core.adapters.UsuarioAdapter;
import com.pacto.adm.core.dto.integracoes.ConfiguracaoEmpresaRDStationDTO;
import com.pacto.adm.core.dto.empresa.EmpresaDTO;
import com.pacto.adm.core.entities.ConfiguracaoEmpresaRDStation;
import com.pacto.adm.core.entities.empresa.Empresa;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class ConfiguracaoEmpresaRDStationAdapter implements AdapterInterface<ConfiguracaoEmpresaRDStation, ConfiguracaoEmpresaRDStationDTO> {

    @Value("${integracao.rdstation.clientIdOauth}")
    private String clientIdOauth;
    @Value("${integracao.rdstation.clientSecretOauth}")
    private String clientSecretOauth;
    private final UsuarioAdapter usuarioAdapter;

    public ConfiguracaoEmpresaRDStationAdapter(UsuarioAdapter usuarioAdapter) {
        this.usuarioAdapter = usuarioAdapter;
    }

    public ConfiguracaoEmpresaRDStationDTO toDto(ConfiguracaoEmpresaRDStation configuracaoEmpresaRDStation) {
        ConfiguracaoEmpresaRDStationDTO configuracaoEmpresaRDStationDTO = new ConfiguracaoEmpresaRDStationDTO();
        configuracaoEmpresaRDStationDTO.setCodigo(configuracaoEmpresaRDStation.getCodigo());
        configuracaoEmpresaRDStationDTO.setEmpresaUsaRd(configuracaoEmpresaRDStation.getEmpresaUsaRd());
        configuracaoEmpresaRDStationDTO.setAcaoObjecao(configuracaoEmpresaRDStation.getAcaoObjecao());
        configuracaoEmpresaRDStationDTO.setHoraLimite(configuracaoEmpresaRDStation.getHoraLimite());
        configuracaoEmpresaRDStationDTO.setEventWeebHook(configuracaoEmpresaRDStation.getEventWeebHook());
        if (configuracaoEmpresaRDStation.getResponsavelPadrao() != null) {
            configuracaoEmpresaRDStationDTO.setResponsavelPadrao(usuarioAdapter.toDto(configuracaoEmpresaRDStation.getResponsavelPadrao()));
        }
        configuracaoEmpresaRDStationDTO.setEmpresa(new EmpresaDTO());
        configuracaoEmpresaRDStationDTO.getEmpresa().setCodigo(configuracaoEmpresaRDStation.getEmpresa().getCodigo());
        configuracaoEmpresaRDStationDTO.setConfigAtualizarAlunoRdStationMarketing(configuracaoEmpresaRDStation.isConfigAtualizarAlunoRdStationMarketing());
        configuracaoEmpresaRDStationDTO.setAccessTokenRdStationMarketing(configuracaoEmpresaRDStation.getAccessTokenRdStationMarketing());
        configuracaoEmpresaRDStationDTO.setRefreshTokenRdStationMarketing(configuracaoEmpresaRDStation.getRefreshTokenRdStationMarketing());
        return configuracaoEmpresaRDStationDTO;
    }

    @Override
    public ConfiguracaoEmpresaRDStation toEntity(ConfiguracaoEmpresaRDStationDTO configuracaoEmpresaRDStationDTO) {
        if (configuracaoEmpresaRDStationDTO != null) {
            ConfiguracaoEmpresaRDStation configuracaoEmpresaRDStation = new ConfiguracaoEmpresaRDStation();
            configuracaoEmpresaRDStation.setCodigo(configuracaoEmpresaRDStationDTO.getCodigo());
            configuracaoEmpresaRDStation.setAcaoObjecao(configuracaoEmpresaRDStationDTO.getAcaoObjecao());
            configuracaoEmpresaRDStation.setEmpresaUsaRd(configuracaoEmpresaRDStationDTO.getEmpresaUsaRd());
            configuracaoEmpresaRDStation.setHoraLimite(configuracaoEmpresaRDStationDTO.getHoraLimite());
            configuracaoEmpresaRDStation.setEventWeebHook(configuracaoEmpresaRDStationDTO.getEventWeebHook());
            if (configuracaoEmpresaRDStationDTO.getResponsavelPadrao() != null) {
                configuracaoEmpresaRDStation.setResponsavelPadrao(usuarioAdapter.toEntity(configuracaoEmpresaRDStationDTO.getResponsavelPadrao()));
            }
            configuracaoEmpresaRDStation.setChavePublica(configuracaoEmpresaRDStation.getClientIdoAuthRds());
            configuracaoEmpresaRDStation.setChavePrivada(configuracaoEmpresaRDStation.getClientSecretoAuthRds());
            configuracaoEmpresaRDStation.setEmpresa(new Empresa());
            configuracaoEmpresaRDStation.getEmpresa().setCodigo(configuracaoEmpresaRDStationDTO.getEmpresa().getCodigo());
            configuracaoEmpresaRDStation.setChavePublica(clientIdOauth);
            configuracaoEmpresaRDStation.setChavePrivada(clientSecretOauth);
            configuracaoEmpresaRDStation.setClientIdoAuthRds(clientIdOauth);
            configuracaoEmpresaRDStation.setClientSecretoAuthRds(clientSecretOauth);
            configuracaoEmpresaRDStation.setConfigAtualizarAlunoRdStationMarketing(configuracaoEmpresaRDStationDTO.getConfigAtualizarAlunoRdStationMarketing());
            configuracaoEmpresaRDStation.setAccessTokenRdStationMarketing(configuracaoEmpresaRDStationDTO.getAccessTokenRdStationMarketing());
            configuracaoEmpresaRDStation.setRefreshTokenRdStationMarketing(configuracaoEmpresaRDStationDTO.getRefreshTokenRdStationMarketing());
            return configuracaoEmpresaRDStation;
        }
        return null;
    }
}
