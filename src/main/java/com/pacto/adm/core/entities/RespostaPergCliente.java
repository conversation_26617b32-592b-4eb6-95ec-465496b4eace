package com.pacto.adm.core.entities;

import com.pacto.config.annotations.RelationalField;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;

import javax.persistence.Entity;
import javax.persistence.ForeignKey;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;

@Entity
public class RespostaPergCliente {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private String respostaTextual;
    private Boolean respostaOpcao;
    private String descricaoRespota;

    @RelationalField
    @ManyToOne
    @JoinColumn(name = "perguntacliente", foreignKey = @ForeignKey(name = "fk_respostapergcliente_perguntacliente"))
    @NotFound(action = NotFoundAction.IGNORE)
    private PerguntaCliente perguntaCliente;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getRespostaTextual() {
        return respostaTextual;
    }

    public void setRespostaTextual(String respostaTextual) {
        this.respostaTextual = respostaTextual;
    }

    public Boolean getRespostaOpcao() {
        return respostaOpcao;
    }

    public void setRespostaOpcao(Boolean respostaOpcao) {
        this.respostaOpcao = respostaOpcao;
    }

    public String getDescricaoRespota() {
        return descricaoRespota;
    }

    public void setDescricaoRespota(String descricaoRespota) {
        this.descricaoRespota = descricaoRespota;
    }

    public PerguntaCliente getPerguntaCliente() {
        return perguntaCliente;
    }

    public void setPerguntaCliente(PerguntaCliente perguntaCliente) {
        this.perguntaCliente = perguntaCliente;
    }
}
