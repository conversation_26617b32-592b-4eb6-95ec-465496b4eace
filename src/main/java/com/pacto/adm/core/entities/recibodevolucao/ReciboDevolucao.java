package com.pacto.adm.core.entities.recibodevolucao;

import com.pacto.adm.core.entities.MovProduto;
import com.pacto.adm.core.entities.Pessoa;
import com.pacto.adm.core.entities.ReciboPagamento;
import com.pacto.adm.core.entities.Usuario;
import com.pacto.adm.core.entities.contrato.Contrato;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.ForeignKey;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.math.BigDecimal;
import java.util.Date;

@Entity
@Table(name = "recibodevolucao")
public class ReciboDevolucao {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "datadevolucao", nullable = false)
    private Date dataDevolucao;
    private Boolean devolucaoManual;
    private Boolean liberacao;
    private Boolean liberacaoDevolucao;
    private Boolean quitacao;
    private Boolean quitacaoManual;
    private BigDecimal valorBaseContrato;
    private BigDecimal valorContaCorrente;
    private BigDecimal valorContrato;
    @Column(name = "valordevolucao", nullable = false)
    private BigDecimal valorDevolucao;
    private BigDecimal valorDevolvidoEmDinheiro;
    @Column(name = "valormultacancelamento", nullable = false)
    private BigDecimal valorMultaCancelamento;
    private BigDecimal valorOriginal;
    private BigDecimal valorRealDevolucao;
    @Column(name = "valortaxacancelamento", nullable = false)
    private BigDecimal valorTaxaCancelamento;
    @Column(name = "valortotalpagopelocliente", nullable = false)
    private BigDecimal valorTotalPagoPeloCliente;
    @Column(name = "valortotalsomaprodutocontratos", nullable = false)
    private BigDecimal valorTotalSomaProdutoContratos;
    @Column(name = "valorutilizadopelocliente", nullable = false)
    private BigDecimal valorUtilizadoPeloCliente;

    @ManyToOne
    @JoinColumn(name = "contrato", foreignKey = @ForeignKey(name = "recibodevolucao_contrato_fkey"))
    private Contrato contrato;

    @ManyToOne
    @JoinColumn(name = "movproduto", foreignKey = @ForeignKey(name = "fk_recibodevolucao_produto"))
    private MovProduto movProduto;

    @ManyToOne
    @JoinColumn(name = "pessoa", foreignKey = @ForeignKey(name = "recibodevolucao_pessoa_fkey"))
    private Pessoa pessoa;

    @ManyToOne
    @JoinColumn(name = "proddevolucao", foreignKey = @ForeignKey(name = "fk_contratomodalidade_produtodevolucao"))
    private MovProduto prodDevolucao;

    @ManyToOne
    @JoinColumn(name = "prodrecebiveis", foreignKey = @ForeignKey(name = "fk_contratomodalidade_produtorecebiveis"))
    private MovProduto prodRecebiveis;

    @ManyToOne
    @JoinColumn(name = "reciboeditado", foreignKey = @ForeignKey(name = "recibodevolucao_recibopagamento_fkey"))
    private ReciboPagamento reciboEditado;

    @ManyToOne
    @JoinColumn(name = "usuario", foreignKey = @ForeignKey(name = "fk_recibodevolucao_produto"), nullable = false)
    private Usuario usuario;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Date getDataDevolucao() {
        return dataDevolucao;
    }

    public void setDataDevolucao(Date dataDevolucao) {
        this.dataDevolucao = dataDevolucao;
    }

    public Boolean getDevolucaoManual() {
        return devolucaoManual;
    }

    public void setDevolucaoManual(Boolean devolucaoManual) {
        this.devolucaoManual = devolucaoManual;
    }

    public Boolean getLiberacao() {
        return liberacao;
    }

    public void setLiberacao(Boolean liberacao) {
        this.liberacao = liberacao;
    }

    public Boolean getLiberacaoDevolucao() {
        return liberacaoDevolucao;
    }

    public void setLiberacaoDevolucao(Boolean lieracaoDevolucao) {
        this.liberacaoDevolucao = lieracaoDevolucao;
    }

    public Boolean getQuitacao() {
        return quitacao;
    }

    public void setQuitacao(Boolean quitacao) {
        this.quitacao = quitacao;
    }

    public Boolean getQuitacaoManual() {
        return quitacaoManual;
    }

    public void setQuitacaoManual(Boolean quitacaoManual) {
        this.quitacaoManual = quitacaoManual;
    }

    public BigDecimal getValorBaseContrato() {
        return valorBaseContrato;
    }

    public void setValorBaseContrato(BigDecimal valorBaseContrato) {
        this.valorBaseContrato = valorBaseContrato;
    }

    public BigDecimal getValorContaCorrente() {
        return valorContaCorrente;
    }

    public void setValorContaCorrente(BigDecimal valorContaCorrente) {
        this.valorContaCorrente = valorContaCorrente;
    }

    public BigDecimal getValorContrato() {
        return valorContrato;
    }

    public void setValorContrato(BigDecimal valorContrato) {
        this.valorContrato = valorContrato;
    }

    public BigDecimal getValorDevolucao() {
        return valorDevolucao;
    }

    public void setValorDevolucao(BigDecimal valorDevolucao) {
        this.valorDevolucao = valorDevolucao;
    }

    public BigDecimal getValorDevolvidoEmDinheiro() {
        return valorDevolvidoEmDinheiro;
    }

    public void setValorDevolvidoEmDinheiro(BigDecimal valorDevolvidoEmDinheiro) {
        this.valorDevolvidoEmDinheiro = valorDevolvidoEmDinheiro;
    }

    public BigDecimal getValorMultaCancelamento() {
        return valorMultaCancelamento;
    }

    public void setValorMultaCancelamento(BigDecimal valorMultaCancelamento) {
        this.valorMultaCancelamento = valorMultaCancelamento;
    }

    public BigDecimal getValorOriginal() {
        return valorOriginal;
    }

    public void setValorOriginal(BigDecimal valorOriginal) {
        this.valorOriginal = valorOriginal;
    }

    public BigDecimal getValorRealDevolucao() {
        return valorRealDevolucao;
    }

    public void setValorRealDevolucao(BigDecimal valorRealDevolucao) {
        this.valorRealDevolucao = valorRealDevolucao;
    }

    public BigDecimal getValorTaxaCancelamento() {
        return valorTaxaCancelamento;
    }

    public void setValorTaxaCancelamento(BigDecimal valorTaxaCancelamento) {
        this.valorTaxaCancelamento = valorTaxaCancelamento;
    }

    public BigDecimal getValorTotalPagoPeloCliente() {
        return valorTotalPagoPeloCliente;
    }

    public void setValorTotalPagoPeloCliente(BigDecimal valorTotalPagoPeloCliente) {
        this.valorTotalPagoPeloCliente = valorTotalPagoPeloCliente;
    }

    public BigDecimal getValorTotalSomaProdutoContratos() {
        return valorTotalSomaProdutoContratos;
    }

    public void setValorTotalSomaProdutoContratos(BigDecimal valorTotalSomaProdutoContratos) {
        this.valorTotalSomaProdutoContratos = valorTotalSomaProdutoContratos;
    }

    public BigDecimal getValorUtilizadoPeloCliente() {
        return valorUtilizadoPeloCliente;
    }

    public void setValorUtilizadoPeloCliente(BigDecimal valorUtilizadoPeloCliente) {
        this.valorUtilizadoPeloCliente = valorUtilizadoPeloCliente;
    }

    public Contrato getContrato() {
        return contrato;
    }

    public void setContrato(Contrato contrato) {
        this.contrato = contrato;
    }

    public MovProduto getMovProduto() {
        return movProduto;
    }

    public void setMovProduto(MovProduto movProduto) {
        this.movProduto = movProduto;
    }

    public Pessoa getPessoa() {
        return pessoa;
    }

    public void setPessoa(Pessoa pessoa) {
        this.pessoa = pessoa;
    }

    public MovProduto getProdDevolucao() {
        return prodDevolucao;
    }

    public void setProdDevolucao(MovProduto prodDevolucao) {
        this.prodDevolucao = prodDevolucao;
    }

    public MovProduto getProdRecebiveis() {
        return prodRecebiveis;
    }

    public void setProdRecebiveis(MovProduto prodRecebiveis) {
        this.prodRecebiveis = prodRecebiveis;
    }

    public ReciboPagamento getReciboEditado() {
        return reciboEditado;
    }

    public void setReciboEditado(ReciboPagamento reciboEditado) {
        this.reciboEditado = reciboEditado;
    }

    public Usuario getUsuario() {
        return usuario;
    }

    public void setUsuario(Usuario usuario) {
        this.usuario = usuario;
    }
}
