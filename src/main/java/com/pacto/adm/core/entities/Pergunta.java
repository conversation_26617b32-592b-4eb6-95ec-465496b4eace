package com.pacto.adm.core.entities;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.OrderBy;
import java.util.HashSet;
import java.util.Set;

@Entity
public class Pergunta {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private String tipopergunta;
    private String descricao;
    private String tipoPergunta;

    @OneToMany(mappedBy = "pergunta")
    @OrderBy("nrQuestao asc")
    private Set<RespostaPergunta> respostaPerguntas;

    public Pergunta() {
    }

    public Pergunta(Integer codigo, String descricao, String tipopergunta, Set<RespostaPergunta> respostaPerguntas){
        this.tipopergunta = tipopergunta;
        this.codigo = codigo;
        this.descricao = descricao;
        this.respostaPerguntas = respostaPerguntas;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getTipopergunta() {
        return tipopergunta;
    }

    public void setTipopergunta(String tipopergunta) {
        this.tipopergunta = tipopergunta;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Set<RespostaPergunta> getRespostaPerguntas(){
        if (respostaPerguntas == null) {
            return new HashSet<>();
        }
        return respostaPerguntas;
    }

    public void setRespostaPerguntas(Set<RespostaPergunta> respostaPerguntas) {this.respostaPerguntas = respostaPerguntas;}

    public String getTipoPergunta() {
        return tipoPergunta;
    }

    public void setTipoPergunta(String tipoPergunta) {
        this.tipoPergunta = tipoPergunta;
    }
}
