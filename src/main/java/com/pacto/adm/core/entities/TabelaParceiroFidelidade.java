package com.pacto.adm.core.entities;

import com.pacto.config.annotations.RelationalField;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;

import javax.persistence.*;
import java.util.HashSet;
import java.util.Set;

@Entity
public class TabelaParceiroFidelidade {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private String nomeTabela;
    private boolean defaultRecorrencia;

    @RelationalField
    @ManyToOne
    @JoinColumn(name = "parceiroFidelidade", foreignKey = @ForeignKey(name = "fk_tabelaparceirofidelidade_parceirofidelidade"))
    @NotFound(action = NotFoundAction.IGNORE)
    private ParceiroFidelidade parceiroFidelidade;

    @OneToMany(mappedBy = "tabelaParceiroFidelidade", cascade = {CascadeType.MERGE, CascadeType.PERSIST, CascadeType.REFRESH}, orphanRemoval = true)
    private Set<TabelaParceiroFidelidadeItem> itens;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNomeTabela() {
        return nomeTabela;
    }

    public void setNomeTabela(String nomeTabela) {
        this.nomeTabela = nomeTabela;
    }

    public boolean isDefaultRecorrencia() {
        return defaultRecorrencia;
    }

    public void setDefaultRecorrencia(boolean defaultRecorrencia) {
        this.defaultRecorrencia = defaultRecorrencia;
    }

    public ParceiroFidelidade getParceiroFidelidade() {
        return parceiroFidelidade;
    }

    public void setParceiroFidelidade(ParceiroFidelidade parceiroFidelidade) {
        this.parceiroFidelidade = parceiroFidelidade;
    }

    public Set<TabelaParceiroFidelidadeItem> getItens() {
        if (this.itens == null) {
            this.itens = new HashSet<>();
        }
        return itens;
    }

    public void setItens(Set<TabelaParceiroFidelidadeItem> itens) {
        this.itens = itens;
    }
}
