package com.pacto.adm.core.entities;

import com.pacto.config.annotations.RelationalField;
import com.pacto.adm.core.entities.empresa.Empresa;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.ForeignKey;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "LocalAcesso", schema = "public")
public class LocalAcesso {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private String descricao;

    @Column(name = "nomecomputador")
    private String nomeComputador;

    @OneToOne
    @RelationalField
    @JoinColumn(name = "empresa", foreignKey = @ForeignKey(name = "fk_localacesso_empresa"))
    private Empresa empresa;

    @Column(name = "tempoentreacessos")
    private Integer tempoEntreAcessos;

    @Column(name = "servidorimpressoes")
    private String servidorImpressoes;

    @Column(name = "portaservidorimp")
    private Integer portaServidorImp;

    @Column(name = "pedirsenhalibparacadaacesso")
    private Boolean pedirSenhaLibParaCadaAcesso;

    @Column(name = "utilizarmodooffline")
    private Boolean utilizarModoOffLine;

    @Column(name = "tempoentreacessoscolaborador")
    private Integer tempoEntreAcessosColaborador;

    @Temporal(TemporalType.DATE)
    @Column(name = "databaseoffline")
    private Date dataBaseOffLine;

    @Temporal(TemporalType.DATE)
    @Column(name = "datadownloadbase")
    private Date dataDownloadBase;

    @Column(name = "tipolocalacesso")
    private Integer tipoLocalAcesso;

    @Column(name = "versaoacesso")
    private String versaoAcesso;

    private String parametros;

    @Column(name = "mostrarmsgdadoscadastraisincompleto")
    private Boolean mostrarMsgDadosCadastraisIncompleto;

    @Column(name = "pedirsenhacadastrarmaisbiometrias")
    private Boolean pedirSenhaCadastrarMaisBiometrias;

    @Column(name = "usarreconhecimento")
    private Boolean usarReconhecimento;

    @Column(name = "portaimagens")
    private Integer portaImagens;

    @Column(name = "urlservidorcamera")
    private String urlServidorCamera;

    @Column(name = "solicitarjustificativaliberacaomanual")
    private Boolean solicitarJustificativaLiberacaoManual;

    private String ip;

    @Column(name = "categorialocalacesso")
    private String categoriaLocalAcesso;

    @Column(name = "tempotoleranciasaida")
    private Integer tempoToleranciaSaida;

    @Column(name = "ignorarconsumocredito")
    private Boolean ignorarConsumoCredito;




}
