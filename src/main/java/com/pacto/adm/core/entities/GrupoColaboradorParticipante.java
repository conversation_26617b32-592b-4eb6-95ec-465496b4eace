package com.pacto.adm.core.entities;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.ForeignKey;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Transient;

@Entity
public class GrupoColaboradorParticipante {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;

    @Column(name = "tipovisao", length = 2)
    private String tipoVisao;

    @ManyToOne
    @JoinColumn(name = "colaboradorParticipante", foreignKey = @ForeignKey(name = "fk_grupocolaboradorparicipante_colaboradorparticipante"))
    private Colaborador colaboradorParticipante;

    @ManyToOne
    @JoinColumn(name = "grupocolaborador", foreignKey = @ForeignKey(name = "fk_grupocolaboradorparicipante_grupocolaborador"))
    private GrupoColaborador grupoColaborador;

    @Transient
    private Usuario usuarioParticipante;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getTipoVisao() {
        return tipoVisao;
    }

    public void setTipoVisao(String tipoVisao) {
        this.tipoVisao = tipoVisao;
    }

    public Colaborador getColaboradorParticipante() {
        return colaboradorParticipante;
    }

    public void setColaboradorParticipante(Colaborador colaboradorParticipante) {
        this.colaboradorParticipante = colaboradorParticipante;
    }

    public GrupoColaborador getGrupoColaborador() {
        return grupoColaborador;
    }

    public void setGrupoColaborador(GrupoColaborador grupoColaborador) {
        this.grupoColaborador = grupoColaborador;
    }

    public Usuario getUsuarioParticipante() {
        return usuarioParticipante;
    }

    public void setUsuarioParticipante(Usuario usuarioParticipante) {
        this.usuarioParticipante = usuarioParticipante;
    }

}
