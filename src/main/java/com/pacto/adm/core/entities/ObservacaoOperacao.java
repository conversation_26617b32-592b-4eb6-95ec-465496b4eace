package com.pacto.adm.core.entities;

import com.pacto.config.annotations.RelationalField;
import com.pacto.adm.core.enumerador.TipoObservacaoOperacaoEnum;
import com.pacto.config.utils.Calendario;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;

import javax.persistence.*;
import java.util.Date;

@Entity
public class ObservacaoOperacao {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private String justificativa;
    private Date dataOperacao;
    private String tipoOperacao;
    private Double valor;
    private String usuarioResponsavel;

    @RelationalField
    @ManyToOne
    @NotFound(action = NotFoundAction.IGNORE)
    @JoinColumn(name = "movParcela", foreignKey = @ForeignKey(name = "fk_observacaooperacao_movparcela"))
    private MovParcela movParcela;

    public ObservacaoOperacao() {
        this.justificativa = "";
        this.dataOperacao = Calendario.hoje();
        this.valor = 0.0;
        this.usuarioResponsavel = "";
        this.tipoOperacao = TipoObservacaoOperacaoEnum.NENHUM.getTipo();
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getJustificativa() {
        return justificativa;
    }

    public void setJustificativa(String justificativa) {
        this.justificativa = justificativa;
    }

    public Date getDataOperacao() {
        return dataOperacao;
    }

    public void setDataOperacao(Date dataOperacao) {
        this.dataOperacao = dataOperacao;
    }

    public String getTipoOperacao() {
        return tipoOperacao;
    }

    public void setTipoOperacao(String tipoObservacao) {
        this.tipoOperacao = tipoObservacao;
    }

    public Double getValor() {
        return valor;
    }

    public void setValor(Double valorOperacao) {
        this.valor = valorOperacao;
    }

    public String getUsuarioResponsavel() {
        return usuarioResponsavel;
    }

    public void setUsuarioResponsavel(String usuarioResponsavel) {
        this.usuarioResponsavel = usuarioResponsavel;
    }

    public MovParcela getMovParcela() {
        return movParcela;
    }

    public void setMovParcela(MovParcela movParcela) {
        this.movParcela = movParcela;
    }
}
