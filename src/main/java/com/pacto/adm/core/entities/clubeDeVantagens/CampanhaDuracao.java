package com.pacto.adm.core.entities.clubeDeVantagens;

import com.pacto.adm.core.entities.empresa.Empresa;
import com.pacto.adm.core.entities.financeiro.ItemVendaAvulsa;
import com.pacto.config.annotations.NomeEntidadeLog;
import com.pacto.config.annotations.RelationalField;

import javax.persistence.CascadeType;
import javax.persistence.Entity;
import javax.persistence.ForeignKey;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Transient;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

@Entity
@NomeEntidadeLog("CampanhaDuracao")
public class CampanhaDuracao  implements Cloneable{
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private Date dataInicial;
    private Date dataFinal;
    private String nome;
    private String descricao;
    @OneToMany(mappedBy = "campanhaDuracao", cascade = {CascadeType.MERGE, CascadeType.PERSIST, CascadeType.REFRESH}, orphanRemoval = true)
    private Set<ItemCampanha> itens;
    private Integer multiplicador;
    @RelationalField
    @ManyToOne
    @JoinColumn(name = "empresa", foreignKey = @ForeignKey(name = "campanhaduracao_fk_empresa"))
    private Empresa empresa;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Date getDataInicial() {
        return dataInicial;
    }

    public void setDataInicial(Date dataInicial) {
        this.dataInicial = dataInicial;
    }

    public Date getDataFinal() {
        return dataFinal;
    }

    public void setDataFinal(Date dataFinal) {
        this.dataFinal = dataFinal;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }



    public Set<ItemCampanha> getItens() {
        if (itens == null) {
            itens = new HashSet<>();
        }
        return itens;
    }

    public void setItens(Set<ItemCampanha> itens) {
        this.itens = itens;
    }

    public Integer getMultiplicador() {
        if (multiplicador == null) {
            multiplicador = 0;
        }
        return multiplicador;
    }

    public void setMultiplicador(Integer multiplicador) {
        this.multiplicador = multiplicador;
    }

    public Empresa getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Empresa empresa) {
        this.empresa = empresa;
    }

    public String getTextoCampanhaApresentar(){
        return (getMultiplicador()>0?" Campanha Ativa:"+getNome()+" Multiplicador:"+getMultiplicador():"");
    }
}
