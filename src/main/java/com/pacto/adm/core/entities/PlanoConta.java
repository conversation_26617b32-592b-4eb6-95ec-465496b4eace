package com.pacto.adm.core.entities;

import javax.persistence.ColumnResult;
import javax.persistence.ConstructorResult;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SqlResultSetMapping;
import javax.persistence.SqlResultSetMappings;

@Entity
@SqlResultSetMappings({
        @SqlResultSetMapping(
                name = "PlanoContaSimple",
                classes = @ConstructorResult(
                        targetClass = PlanoConta.class,
                        columns = {
                                @ColumnResult(name = "codigo", type = Integer.class),
                                @ColumnResult(name = "nome", type = String.class),
                                @ColumnResult(name = "insideltv", type = Boolean.class)
                        }
                )
        ),
})
public class PlanoConta {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private String nome;
    private Boolean insideltv;

    public PlanoConta() {
    }

    public PlanoConta(Integer codigo, String nome, Boolean insideltv) {
        this.codigo = codigo;
        this.nome = nome;
        this.insideltv = insideltv;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Boolean getInsideltv() {
        return insideltv;
    }

    public void setInsideltv(Boolean insideltv) {
        this.insideltv = insideltv;
    }
}
