package com.pacto.adm.core.entities.clubeDeVantagens;

import com.pacto.adm.core.entities.Cliente;
import com.pacto.adm.core.enumerador.TipoItemCampanhaEnum;
import com.pacto.adm.core.enumerador.TipoItemCampanhaEnumConverter;
import com.pacto.config.annotations.NomeEntidadeLog;
import com.pacto.config.annotations.RelationalField;

import javax.persistence.Column;
import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.ForeignKey;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import java.util.Date;

@Entity
@NomeEntidadeLog("HistoricoPontos")
public class HistoricoPontos implements Cloneable{
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;

    private String descricao;
    private Integer pontos;
    private Integer pontosTotal;

    private Integer produto;

    @RelationalField
    @ManyToOne
    @JoinColumn(name = "cliente", foreignKey = @ForeignKey(name = "historicopontos_cliente_fkey"))
    private Cliente cliente;

    private Date dataAula;
    private Date dataConfirmacao;

    private Integer codigoVenda;
    private Boolean entrada;
    @Convert(converter = TipoItemCampanhaEnumConverter.class)
    private TipoItemCampanhaEnum tipodepontos;

    @RelationalField
    @ManyToOne
    @JoinColumn(name = "codigoCampanha", foreignKey = @ForeignKey(name = "historicopontos_fk_campanha"))
    private CampanhaDuracao campanha;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Integer getPontos() {
        return pontos;
    }

    public void setPontos(Integer pontos) {
        this.pontos = pontos;
    }

    public Integer getPontosTotal() {
        return pontosTotal;
    }

    public void setPontosTotal(Integer pontosTotal) {
        this.pontosTotal = pontosTotal;
    }

    public Integer getProduto() {
        return produto;
    }

    public void setProduto(Integer produto) {
        this.produto = produto;
    }

    public Cliente getCliente() {
        return cliente;
    }

    public void setCliente(Cliente cliente) {
        this.cliente = cliente;
    }

    public Date getDataAula() {
        return dataAula;
    }

    public void setDataAula(Date dataAula) {
        this.dataAula = dataAula;
    }

    public Date getDataConfirmacao() {
        return dataConfirmacao;
    }

    public void setDataConfirmacao(Date dataConfirmacao) {
        this.dataConfirmacao = dataConfirmacao;
    }

    public Boolean getEntrada() {
        return entrada;
    }

    public void setEntrada(Boolean entrada) {
        this.entrada = entrada;
    }

    public TipoItemCampanhaEnum getTipodepontos() {
        return tipodepontos;
    }

    public void setTipodepontos(TipoItemCampanhaEnum tipodepontos) {
        this.tipodepontos = tipodepontos;
    }

    public CampanhaDuracao getCampanha() {
        return campanha;
    }

    public void setCampanha(CampanhaDuracao campanha) {
        this.campanha = campanha;
    }

    public Integer getCodigoVenda() {
        return codigoVenda;
    }

    public void setCodigoVenda(Integer codigoVenda) {
        this.codigoVenda = codigoVenda;
    }
}
