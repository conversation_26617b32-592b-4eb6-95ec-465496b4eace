package com.pacto.adm.core.entities;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.pacto.config.annotations.RelationalField;
import com.pacto.adm.core.entities.empresa.Empresa;
import com.pacto.adm.core.enumerador.AcaoObjcaoLeadEnum;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;

import javax.persistence.*;

@Entity
public class ConfiguracaoEmpresaRDStation {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private Integer acaoObjecao = AcaoObjcaoLeadEnum.NENHUMA.getCodigo();;
    private Boolean empresaUsaRd;
    private String chavePublica;
    private String chavePrivada;
    private String horaLimite;
    private String clientIdoAuthRds = "6d8f483f-882b-4087-9753-411acd143125";  // https://appstore.rdstation.com/pt-BR/publisher/13242251495369/apps/2629
    private String clientSecretoAuthRds = "0b0ae2af3a98412687e990f4247754bc"; // https://appstore.rdstation.com/pt-BR/publisher/13242251495369/apps/2629
    private String eventWeebHook;
    private boolean configAtualizarAlunoRdStationMarketing;
    private String accessTokenRdStationMarketing;
    private String refreshTokenRdStationMarketing;

    @RelationalField
    @ManyToOne
    @JoinColumn(name = "responsavelPadrao", foreignKey = @ForeignKey(name = "configuracaoempresardstation_usuario_fkey"))
    @NotFound(action = NotFoundAction.IGNORE)
    private Usuario responsavelPadrao;

    @JsonIgnore
    @OneToOne
    @JoinColumn(name = "empresa", foreignKey = @ForeignKey(name = "configuracaoempresardstation_empresa_fkey"))
    @NotFound(action = NotFoundAction.IGNORE)
    private Empresa empresa;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getAcaoObjecao() {
        return acaoObjecao;
    }

    public void setAcaoObjecao(Integer acaoObjecao) {
        this.acaoObjecao = acaoObjecao;
    }

    public Boolean getEmpresaUsaRd() {
        return empresaUsaRd;
    }

    public void setEmpresaUsaRd(Boolean empresaUsaRd) {
        this.empresaUsaRd = empresaUsaRd;
    }

    public String getChavePublica() {
        return chavePublica;
    }

    public void setChavePublica(String chavePublica) {
        this.chavePublica = chavePublica;
    }

    public String getChavePrivada() {
        return chavePrivada;
    }

    public void setChavePrivada(String chavePrivada) {
        this.chavePrivada = chavePrivada;
    }

    public String getHoraLimite() {
        return horaLimite;
    }

    public void setHoraLimite(String horaLimite) {
        this.horaLimite = horaLimite;
    }

    public String getClientIdoAuthRds() {
        return clientIdoAuthRds;
    }

    public void setClientIdoAuthRds(String clientIdoAuthRds) {
        this.clientIdoAuthRds = clientIdoAuthRds;
    }

    public String getClientSecretoAuthRds() {
        return clientSecretoAuthRds;
    }

    public void setClientSecretoAuthRds(String clientSecretoAuthRds) {
        this.clientSecretoAuthRds = clientSecretoAuthRds;
    }

    public String getEventWeebHook() {
        return eventWeebHook;
    }

    public void setEventWeebHook(String eventWeebHook) {
        this.eventWeebHook = eventWeebHook;
    }

    public Usuario getResponsavelPadrao() {
        return responsavelPadrao;
    }

    public void setResponsavelPadrao(Usuario responsavelPadrao) {
        this.responsavelPadrao = responsavelPadrao;
    }

    public Empresa getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Empresa empresa) {
        this.empresa = empresa;
    }

    public boolean isConfigAtualizarAlunoRdStationMarketing() {
        return configAtualizarAlunoRdStationMarketing;
    }

    public void setConfigAtualizarAlunoRdStationMarketing(boolean configAtualizarAlunoRdStationMarketing) {
        this.configAtualizarAlunoRdStationMarketing = configAtualizarAlunoRdStationMarketing;
    }

    public String getAccessTokenRdStationMarketing() {
        return accessTokenRdStationMarketing;
    }

    public void setAccessTokenRdStationMarketing(String accessTokenRdStationMarketing) {
        this.accessTokenRdStationMarketing = accessTokenRdStationMarketing;
    }

    public String getRefreshTokenRdStationMarketing() {
        return refreshTokenRdStationMarketing;
    }

    public void setRefreshTokenRdStationMarketing(String refreshTokenRdStationMarketing) {
        this.refreshTokenRdStationMarketing = refreshTokenRdStationMarketing;
    }
}
