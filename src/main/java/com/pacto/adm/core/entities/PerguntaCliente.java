package com.pacto.adm.core.entities;

import javax.persistence.CascadeType;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Transient;
import java.util.LinkedHashSet;
import java.util.Set;

@Entity
public class PerguntaCliente {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private Boolean multipla;
    private Boolean simples;
    private Boolean textual;
    private String tipoPergunta;
    private String descricao;

    @OneToMany(fetch = FetchType.EAGER, mappedBy = "perguntaCliente", cascade = {CascadeType.MERGE, CascadeType.PERSIST, CascadeType.REFRESH}, orphanRemoval = true)
    private Set<RespostaPergCliente> respostaPergCliente;

    @Transient
    private Boolean obrigatoria;
    @Transient
    private Integer perguntaCodigo;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Boolean getMultipla() {
        return multipla;
    }

    public void setMultipla(Boolean multipla) {
        this.multipla = multipla;
    }

    public Boolean getSimples() {
        return simples;
    }

    public void setSimples(Boolean simples) {
        this.simples = simples;
    }

    public Boolean getTextual() {
        return textual;
    }

    public Set<RespostaPergCliente> getRespostaPergCliente() {
        if(respostaPergCliente == null) {
            respostaPergCliente = new LinkedHashSet<>();
        }
        return respostaPergCliente;
    }

    public void setRespostaPergCliente(Set<RespostaPergCliente> respostaPergCliente) {
        this.respostaPergCliente = respostaPergCliente;
    }

    public void setTextual(Boolean textual) {
        this.textual = textual;
    }

    public String getTipoPergunta() {
        return tipoPergunta;
    }

    public void setTipoPergunta(String tipoPergunta) {
        this.tipoPergunta = tipoPergunta;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Boolean getObrigatoria() {
        return obrigatoria;
    }

    public void setObrigatoria(Boolean obrigatoria) {
        this.obrigatoria = obrigatoria;
    }

    public Integer getPerguntaCodigo() {
        return perguntaCodigo;
    }

    public void setPerguntaCodigo(Integer perguntaCodigo) {
        this.perguntaCodigo = perguntaCodigo;
    }
}
