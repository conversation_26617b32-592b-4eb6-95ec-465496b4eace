package com.pacto.adm.core.entities;

import javax.persistence.*;
import java.util.Date;

@Entity
@Table(name = "logApi")
public class LogApi {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private String descricaoToken;
    private Date dataUso;
    private String ip;
    private String method;
    private String uri;
    private String params;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricaoToken() {
        return descricaoToken;
    }

    public void setDescricaoToken(String descricaoToken) {
        this.descricaoToken = descricaoToken;
    }

    public Date getDataUso() {
        return dataUso;
    }

    public void setDataUso(Date dataUso) {
        this.dataUso = dataUso;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getMethod() {
        return method;
    }

    public void setMethod(String method) {
        this.method = method;
    }

    public String getUri() {
        return uri;
    }

    public void setUri(String uri) {
        this.uri = uri;
    }

    public String getParams() {
        return params;
    }

    public void setParams(String params) {
        this.params = params;
    }
}
