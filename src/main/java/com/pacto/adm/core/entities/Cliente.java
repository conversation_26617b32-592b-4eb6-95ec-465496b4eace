package com.pacto.adm.core.entities;

import com.pacto.adm.core.entities.contrato.PeriodoAcessoCliente;
import com.pacto.config.annotations.RelationalField;
import com.pacto.adm.core.entities.empresa.Empresa;
import com.pacto.config.utils.Calendario;
import com.pacto.config.utils.Uteis;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;

import javax.persistence.*;
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
public class Cliente {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private Integer codigoMatricula;
    private boolean parqPositivo;
    private String gymPassUniqueToken;
    private String gymPassTypeNumber;
    private Integer responsavelfreepass;
    private String matricula;
    private String situacao;

    @OneToOne
    @RelationalField
    @JoinColumn(name = "pessoa", foreignKey = @ForeignKey(name = "fk_cliente_pessoa"))
    private Pessoa pessoa;

    @RelationalField
    @ManyToOne
    @JoinColumn(name = "empresa", foreignKey = @ForeignKey(name = "fk_cliente_empresa"))
    @NotFound(action = NotFoundAction.IGNORE)
    private Empresa empresa;

    @RelationalField
    @ManyToOne
    @JoinColumn(name = "freepass", foreignKey = @ForeignKey(name = "fk_cliente_freepass"))
    @NotFound(action = NotFoundAction.IGNORE)
    private Produto freepass;

    @Transient
    private Boolean dadosSinteticoPreparados;

    public Cliente(Integer codigo) {
        this.codigo = codigo;
    }

    public Cliente(Pessoa pessoa) {
        this.pessoa = pessoa;
    }

    public Cliente(Integer codigoMatricula, String matricula, Pessoa pessoa) {
        this.codigoMatricula = codigoMatricula;
        this.matricula = matricula;
        this.pessoa = pessoa;
    }

    public Cliente(Integer codigo, String matricula, String situacao) {
        this.codigo = codigo;
        this.matricula = matricula;
        this.situacao = situacao;
    }

    public Cliente(Integer codigo, Integer codigoMatricula, String matricula, Pessoa pessoa) {
        this.codigo = codigo;
        this.codigoMatricula = codigoMatricula;
        this.matricula = matricula;
        this.pessoa = pessoa;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getCodigoMatricula() {
        return codigoMatricula;
    }

    public void setCodigoMatricula(Integer codigoMatricula) {
        this.codigoMatricula = codigoMatricula;
    }

    public Pessoa getPessoa() {
        return pessoa;
    }

    public Produto getFreepass() {
        return freepass;
    }

    public void setFreepass(Produto freepass) {
        this.freepass = freepass;
    }

    public void setPessoa(Pessoa pessoa) {
        this.pessoa = pessoa;
    }

    public boolean isParqPositivo() {
        return parqPositivo;
    }

    public void setParqPositivo(boolean parqPositivo) {
        this.parqPositivo = parqPositivo;
    }

    public String getGymPassUniqueToken() {
        return gymPassUniqueToken;
    }

    public void setGymPassUniqueToken(String gymPassUniqueToken) {
        this.gymPassUniqueToken = gymPassUniqueToken;
    }

    public String getGymPassTypeNumber() {
        return gymPassTypeNumber;
    }

    public Integer getResponsavelfreepass() {
        return responsavelfreepass;
    }

    public void setResponsavelfreepass(Integer responsavelfreepass) {
        this.responsavelfreepass = responsavelfreepass;
    }

    public Empresa getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Empresa empresa) {
        this.empresa = empresa;
    }

    public void setGymPassTypeNumber(String gymPassTypeNumber) {
        this.gymPassTypeNumber = gymPassTypeNumber;
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public String getMatricula() {
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }

    public PeriodoAcessoCliente gerarPeriodoAcessoBaseadoCliente() {
        PeriodoAcessoCliente obj = new PeriodoAcessoCliente();
        obj.setDataInicioAcesso(Calendario.hoje());
        obj.setDataFinalAcesso(Uteis.obterDataFutura2(obj.getDataInicioAcesso(), (getFreepass().getNrDiasVigencia() - 1)));
        obj.setPessoa(getPessoa().getCodigo());
        obj.setContrato(0);
        obj.setTipoAcesso("PL");
        obj.setResponsavel(getResponsavelfreepass());
        return obj;
    }
}
