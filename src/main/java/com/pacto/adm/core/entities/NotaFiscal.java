package com.pacto.adm.core.entities;

import com.pacto.adm.core.entities.empresa.Empresa;
import com.pacto.adm.core.enumerador.TipoNotaFiscalEnum;
import com.pacto.adm.core.enumerador.origemsistema.TipoNotaFiscalEnumConverter;
import com.pacto.config.annotations.NomeEntidadeLog;
import com.pacto.config.annotations.RelationalField;
import com.pacto.config.utils.UteisValidacao;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;
import org.json.JSONObject;

import javax.persistence.Column;
import javax.persistence.ConstraintMode;
import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.ForeignKey;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.util.Date;

@Entity
@NomeEntidadeLog("NotaFiscal")
public class NotaFiscal {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "dataregistro")
    private Date dataRegistro;
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "dataemissao")
    private Date dataEmissao;
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "dataautorizacao")
    private Date dataAutorizacao;
    @Column(name = "idempresaenotas")
    private String idEmpresaEnotas;
    @Column(name = "idexterno")
    private String idExterno;
    @Column(name = "idreferencia")
    private String idReferencia;
    @Column(name = "idpacto")
    private String idPacto;
    @Column(name = "jsonnota", columnDefinition = "TEXT")
    private String jsonNota;
    @Column(name = "jsonenvio", columnDefinition = "TEXT")
    private String jsonEnvio;
    @Column(name = "jsonretorno", columnDefinition = "TEXT")
    private String jsonRetorno;
    @Column(name = "jsonenviocancelamento", columnDefinition = "TEXT")
    private String jsonEnvioCancelamento;
    @Column(name = "jsonretornocancelamento", columnDefinition = "TEXT")
    private String jsonRetornoCancelamento;
    @Column(name = "jsonenvioinutilizar", columnDefinition = "TEXT")
    private String jsonEnvioInutilizar;
    @Column(name = "jsonretornoinutilizar", columnDefinition = "TEXT")
    private String jsonRetornoInutilizar;
    @Column(name = "statusnota")
    private String statusNota; //StatusNotaFiscalEnotasEnum
    @Column(name = "razaosocial")
    private String razaoSocial;
    @Column(name = "cpfcnpj")
    private String cpfCnpj;
    @Column(name = "nomecliente")
    private String nomeCliente;
    @Column(name = "numeronota")
    private String numeroNota;
    @Column(name = "chaveacesso")
    private String chaveAcesso;
    @Convert(converter = TipoNotaFiscalEnumConverter.class)
    @Column(name = "tipo", columnDefinition = "DEFAULT 0")
    private TipoNotaFiscalEnum tipo;
    private Double valor;
    private boolean excluido = false;
    @Column(name = "notafiscalanterior")
    private Integer notaFiscalAnterior;
    @Column(name = "notafiscalnova")
    private Integer notaFiscalNova;

    @ManyToOne
    @JoinColumn(name = "empresa", foreignKey = @ForeignKey(name = "fk_notafiscal_empresa"))
    private Empresa empresa;

    @RelationalField
    @ManyToOne
    @JoinColumn(name = "pessoa", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    @NotFound(action = NotFoundAction.IGNORE)
    private Pessoa pessoa;

    @RelationalField
    @ManyToOne
    @JoinColumn(name = "usuario", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    @NotFound(action = NotFoundAction.IGNORE)
    private Usuario usuario;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Date getDataRegistro() {
        return dataRegistro;
    }

    public void setDataRegistro(Date dataRegistro) {
        this.dataRegistro = dataRegistro;
    }

    public Date getDataEmissao() {
        return dataEmissao;
    }

    public void setDataEmissao(Date dataEmissao) {
        this.dataEmissao = dataEmissao;
    }

    public Date getDataAutorizacao() {
        return dataAutorizacao;
    }

    public void setDataAutorizacao(Date dataAutorizacao) {
        this.dataAutorizacao = dataAutorizacao;
    }

    public String getIdEmpresaEnotas() {
        return idEmpresaEnotas;
    }

    public void setIdEmpresaEnotas(String idEmpresaEnotas) {
        this.idEmpresaEnotas = idEmpresaEnotas;
    }

    public String getIdExterno() {
        return idExterno;
    }

    public void setIdExterno(String idExterno) {
        this.idExterno = idExterno;
    }

    public String getIdReferencia() {
        return idReferencia;
    }

    public void setIdReferencia(String idReferencia) {
        this.idReferencia = idReferencia;
    }

    public String getIdPacto() {
        return idPacto;
    }

    public void setIdPacto(String idPacto) {
        this.idPacto = idPacto;
    }

    public String getJsonNota() {
        return jsonNota;
    }

    public void setJsonNota(String jsonNota) {
        this.jsonNota = jsonNota;
    }

    public String getJsonEnvio() {
        return jsonEnvio;
    }

    public void setJsonEnvio(String jsonEnvio) {
        this.jsonEnvio = jsonEnvio;
    }

    public String getJsonRetorno() {
        return jsonRetorno;
    }

    public void setJsonRetorno(String jsonRetorno) {
        this.jsonRetorno = jsonRetorno;
    }

    public String getJsonEnvioCancelamento() {
        return jsonEnvioCancelamento;
    }

    public void setJsonEnvioCancelamento(String jsonEnvioCancelamento) {
        this.jsonEnvioCancelamento = jsonEnvioCancelamento;
    }

    public String getJsonRetornoCancelamento() {
        return jsonRetornoCancelamento;
    }

    public void setJsonRetornoCancelamento(String jsonRetornoCancelamento) {
        this.jsonRetornoCancelamento = jsonRetornoCancelamento;
    }

    public String getJsonEnvioInutilizar() {
        return jsonEnvioInutilizar;
    }

    public void setJsonEnvioInutilizar(String jsonEnvioInutilizar) {
        this.jsonEnvioInutilizar = jsonEnvioInutilizar;
    }

    public String getJsonRetornoInutilizar() {
        return jsonRetornoInutilizar;
    }

    public void setJsonRetornoInutilizar(String jsonRetornoInutilizar) {
        this.jsonRetornoInutilizar = jsonRetornoInutilizar;
    }

    public String getStatusNota() {
        return statusNota;
    }

    public void setStatusNota(String statusNota) {
        this.statusNota = statusNota;
    }

    public String getRazaoSocial() {
        return razaoSocial;
    }

    public void setRazaoSocial(String razaoSocial) {
        this.razaoSocial = razaoSocial;
    }

    public String getCpfCnpj() {
        return cpfCnpj;
    }

    public void setCpfCnpj(String cpfCnpj) {
        this.cpfCnpj = cpfCnpj;
    }

    public String getNomeCliente() {
        return nomeCliente;
    }

    public void setNomeCliente(String nomeCliente) {
        this.nomeCliente = nomeCliente;
    }

    public String getNumeroNota() {
        return numeroNota;
    }

    public void setNumeroNota(String numeroNota) {
        this.numeroNota = numeroNota;
    }

    public String getChaveAcesso() {
        return chaveAcesso;
    }

    public void setChaveAcesso(String chaveAcesso) {
        this.chaveAcesso = chaveAcesso;
    }

    public TipoNotaFiscalEnum getTipo() {
        return tipo;
    }

    public void setTipo(TipoNotaFiscalEnum tipo) {
        this.tipo = tipo;
    }

    public Double getValor() {
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public boolean isExcluido() {
        return excluido;
    }

    public void setExcluido(boolean excluido) {
        this.excluido = excluido;
    }

    public Integer getNotaFiscalAnterior() {
        return notaFiscalAnterior;
    }

    public void setNotaFiscalAnterior(Integer notaFiscalAnterior) {
        this.notaFiscalAnterior = notaFiscalAnterior;
    }

    public Integer getNotaFiscalNova() {
        return notaFiscalNova;
    }

    public void setNotaFiscalNova(Integer notaFiscalNova) {
        this.notaFiscalNova = notaFiscalNova;
    }

    public Empresa getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Empresa empresa) {
        this.empresa = empresa;
    }

    public Pessoa getPessoa() {
        return pessoa;
    }

    public void setPessoa(Pessoa pessoa) {
        this.pessoa = pessoa;
    }

    public Usuario getUsuario() {
        return usuario;
    }

    public void setUsuario(Usuario usuario) {
        this.usuario = usuario;
    }

    public String getSerie() {
        JSONObject json = null;
        try {
            json = new JSONObject(getJsonRetorno());
            String serie = "";
            if (getTipo().equals(TipoNotaFiscalEnum.NFSE)) {
                String serieRPS = json.optString("nfeSerieRps");
                if (UteisValidacao.emptyString(serieRPS)) {
                    serieRPS = json.optString("serieRps");
                }
                serie = serieRPS;
            } else if (getTipo().equals(TipoNotaFiscalEnum.NFCE) || getTipo().equals(TipoNotaFiscalEnum.NFE)) {
                serie = json.getString("nfeSerie");
            }

            return findSerieAttribute(serie, json);
        } catch (Exception ex) {
            return findSerieAttribute(null, json);
        }
    }

    private String findSerieAttribute(String serie, JSONObject json){
        if(serie == null || serie.isEmpty()){
            try{
                serie = json.optString("serie");
            }catch (Exception ignored){
                try{
                    serie = String.valueOf(json.getInt("serie"));
                }catch (Exception ignoredInt){}
            }
        }
        return serie;
    }

    public String getRps() {
        try {
            JSONObject json = new JSONObject(getJsonRetorno());
            if (getTipo().equals(TipoNotaFiscalEnum.NFSE) || getTipo().equals(TipoNotaFiscalEnum.NFE)) {
                String nrRPS = json.optString("nfeNumeroRps");
                if (UteisValidacao.emptyString(nrRPS)) {
                    nrRPS = json.optString("numeroRps");
                }
                return nrRPS;
            } else if (getTipo().equals(TipoNotaFiscalEnum.NFCE)) {
                return json.getString("nfeNumero");
            }
            return "";
        } catch (Exception ex) {
            return "";
        }
    }

    public String getLinkPDF() {
        try {
            JSONObject json = new JSONObject(getJsonRetorno());
            if (getTipo().equals(TipoNotaFiscalEnum.NFSE)) {
                String linkPDF = json.optString("nfeLinkPdf");
                if (UteisValidacao.emptyString(linkPDF)) {
                    linkPDF = json.optString("linkDownloadPDF");
                }
                return linkPDF;
            } else if (getTipo().equals(TipoNotaFiscalEnum.NFCE) || getTipo().equals(TipoNotaFiscalEnum.NFE)) {
                String linkPDF = json.optString("nfeLinkDanfe");
                if (UteisValidacao.emptyString(linkPDF)) {
                    linkPDF = json.optString("linkDanfe");
                }
                if (UteisValidacao.emptyString(linkPDF)) {
                    linkPDF = json.optString("linkDownloadPDF");
                }
                return linkPDF;
            }
            return "";
        } catch (Exception ex) {
            return "";
        }
    }

    public String getLinkXML() {
        try {
            JSONObject json = new JSONObject(getJsonRetorno());
            String linkXML = json.optString("nfeLinkXml");
            if (UteisValidacao.emptyString(linkXML)) {
                linkXML = json.optString("linkDownloadXML");
            }
            if (UteisValidacao.emptyString(linkXML)) {
                linkXML = json.optString("linkDownloadXml");
            }
            return linkXML;
        } catch (Exception ex) {
            return "";
        }
    }

}
