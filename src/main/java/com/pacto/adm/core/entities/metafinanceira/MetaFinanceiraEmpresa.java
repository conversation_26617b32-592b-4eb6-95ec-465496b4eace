package com.pacto.adm.core.entities.metafinanceira;

import com.pacto.adm.core.entities.empresa.Empresa;

import javax.persistence.Entity;
import javax.persistence.ForeignKey;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

@Entity
@Table(name = "metafinanceiraempresa")
public class MetaFinanceiraEmpresa {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;

    private Integer mes;
    private Integer ano;

    private String descricao;

    @ManyToOne
    @JoinColumn(name = "empresa", nullable = false, foreignKey = @ForeignKey(name = "fk_metafinanceiraempresa_empresa"))
    private Empresa empresa;

    public MetaFinanceiraEmpresa() {
    }

    public MetaFinanceiraEmpresa(Integer codigo, String descricao, Integer mes, Integer ano) {
        this.codigo = codigo;
        this.descricao = descricao;
        this.mes = mes;
        this.ano = ano;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Empresa getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Empresa empresa) {
        this.empresa = empresa;
    }

    public Integer getMes() {
        return mes;
    }

    public void setMes(Integer mes) {
        this.mes = mes;
    }

    public Integer getAno() {
        return ano;
    }

    public void setAno(Integer ano) {
        this.ano = ano;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
}
