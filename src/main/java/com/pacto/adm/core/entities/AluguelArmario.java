package com.pacto.adm.core.entities;

import com.pacto.config.annotations.RelationalField;
import com.pacto.adm.core.entities.financeiro.VendaAvulsa;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.ForeignKey;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import java.util.Date;

@Entity
@Table(name = "aluguelarmario")
public class AluguelArmario {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    @Column(name = "datacadastro")
    private Date dataCadastro;

    private Double valor;

    @Column(name = "fimOriginal")
    private Date fimOriginal;

    @Column(name = "datarenovacaoautomatica")
    private Date dataRenovacaoAutomatica;

    @Column(name = "contratoassinado")
    private Boolean contratoAssinado;

    @Column(name = "renovarautomatico")
    private Boolean renovarAutomatico;

    @Column(name = "datainicio")
    private Date dataInicio;

    @Column(name = "chavedevolvida")
    private Boolean chaveDevolvida;

    @RelationalField
    @ManyToOne
    @JoinColumn(name = "armario", foreignKey = @ForeignKey(name = "fk_aluguelarmario_armario"))
    @NotFound(action = NotFoundAction.IGNORE)
    private Armario armario;

    @RelationalField
    @ManyToOne
    @JoinColumn(name = "cliente", foreignKey = @ForeignKey(name = "fk_aluguelarmario_cliente"))
    @NotFound(action = NotFoundAction.IGNORE)
    private Cliente cliente;

    @RelationalField
    @ManyToOne
    @JoinColumn(name = "responsavelcadastro", foreignKey = @ForeignKey(name = "fk_aluguelarmario_responsavelcadastro"))
    @NotFound(action = NotFoundAction.IGNORE)
    private Usuario responsavelCadastro;

    @RelationalField
    @ManyToOne
    @JoinColumn(name = "movproduto", foreignKey = @ForeignKey(name = "fk_aluguelarmario_movproduto"))
    @NotFound(action = NotFoundAction.IGNORE)
    private MovProduto movProduto;

    @RelationalField
    @ManyToOne
    @JoinColumn(name = "vendaavulsa", foreignKey = @ForeignKey(name = "fk_armario_vendaavulsa"))
    @NotFound(action = NotFoundAction.IGNORE)
    private VendaAvulsa vendaAvulsa;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Date getDataCadastro() {
        return dataCadastro;
    }

    public void setDataCadastro(Date dataCadastro) {
        this.dataCadastro = dataCadastro;
    }

    public Double getValor() {
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public Date getDataRenovacaoAutomatica() {
        return dataRenovacaoAutomatica;
    }

    public void setDataRenovacaoAutomatica(Date dataRenovacaoAutomatica) {
        this.dataRenovacaoAutomatica = dataRenovacaoAutomatica;
    }

    public Boolean getContratoAssinado() {
        return contratoAssinado;
    }

    public void setContratoAssinado(Boolean contratoAssinado) {
        this.contratoAssinado = contratoAssinado;
    }

    public Boolean getRenovarAutomatico() {
        return renovarAutomatico;
    }

    public void setRenovarAutomatico(Boolean renovarAutomatico) {
        this.renovarAutomatico = renovarAutomatico;
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public Boolean getChaveDevolvida() {
        return chaveDevolvida;
    }

    public void setChaveDevolvida(Boolean chaveDevolvida) {
        this.chaveDevolvida = chaveDevolvida;
    }

    public Armario getArmario() {
        return armario;
    }

    public void setArmario(Armario armario) {
        this.armario = armario;
    }

    public Cliente getCliente() {
        return cliente;
    }

    public void setCliente(Cliente cliente) {
        this.cliente = cliente;
    }

    public Usuario getResponsavelCadastro() {
        return responsavelCadastro;
    }

    public void setResponsavelCadastro(Usuario responsavelCadastro) {
        this.responsavelCadastro = responsavelCadastro;
    }

    public MovProduto getMovProduto() {
        return movProduto;
    }

    public void setMovProduto(MovProduto movProduto) {
        this.movProduto = movProduto;
    }

    public VendaAvulsa getVendaAvulsa() {
        return vendaAvulsa;
    }

    public void setVendaAvulsa(VendaAvulsa vendaAvulsa) {
        this.vendaAvulsa = vendaAvulsa;
    }

    public Date getFimOriginal() {
        return fimOriginal;
    }

    public void setFimOriginal(Date fimOriginal) {
        this.fimOriginal = fimOriginal;
    }
}
