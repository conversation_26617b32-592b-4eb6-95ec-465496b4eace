package com.pacto.adm.core.entities;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.ForeignKey;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;

@Entity
public class ConfiguracaoSistema {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private Boolean utilizarServicoSesiSc;
    private Boolean realizarEnvioSesiSc;
    private Boolean controleAcessoMultiplasEmpresasPorPlano;
    private Integer carenciaRenovacao;

    @ManyToOne
    @JoinColumn(name = "questionarioprimeiracompra", foreignKey = @ForeignKey(name = "fk_empresa_questionarioprimeiracompra"))
    private Questionario questionarioPrimeiraCompra;
    @ManyToOne
    @JoinColumn(name = "questionarioprimeiravisita", foreignKey = @ForeignKey(name = "fk_configuracaosistema_questionarioprimeiravisita"))
    private Questionario questionarioPrimeiraVisita;
    @ManyToOne
    @JoinColumn(name = "questionariorematricula", foreignKey = @ForeignKey(name = "fk_configuracaosistema_questionariorematricula"))
    private Questionario questionarioRematricula;
    @ManyToOne
    @JoinColumn(name = "questionarioretorno", foreignKey = @ForeignKey(name = "fk_configuracaosistema_questionarioretorno"))
    private Questionario questionarioRetorno;
    @ManyToOne
    @JoinColumn(name = "questionarioretornocompra", foreignKey = @ForeignKey(name = "fk_empresa_questionarioretornocompra"))
    private Questionario questionarioRetornoCompra;
    private long nrDiasVigenteQuestionarioRetorno;
    private long nrDiasVigenteQuestionarioRematricula;
    private long nrDiasVigenteQuestionarioRetornoCompra;
    @Column(name = "chavepublicasesc")
    private String chavePublicaSESC;
    @Column(name = "usuarioapisescgo")
    private String usuarioApiSescGo;
    @Column(name = "senhaapisescgo")
    private String senhaApiSescGo;

    public Boolean getUtilizarServicoSesiSc() {
        return utilizarServicoSesiSc;
    }

    public Boolean getRealizarEnvioSesiSc() {
        return realizarEnvioSesiSc;
    }

    public Boolean getControleAcessoMultiplasEmpresasPorPlano() {
        return controleAcessoMultiplasEmpresasPorPlano;
    }

    public void setControleAcessoMultiplasEmpresasPorPlano(Boolean controleAcessoMultiplasEmpresasPorPlano) {
        this.controleAcessoMultiplasEmpresasPorPlano = controleAcessoMultiplasEmpresasPorPlano;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Boolean isUtilizarServicoSesiSc() {
        if(utilizarServicoSesiSc == null){
            utilizarServicoSesiSc = false;
        }
        return utilizarServicoSesiSc;
    }

    public void setUtilizarServicoSesiSc(Boolean utilizarServicoSesiSc) {
        this.utilizarServicoSesiSc = utilizarServicoSesiSc;
    }

    public Boolean isRealizarEnvioSesiSc() {
        if(realizarEnvioSesiSc == null){
            realizarEnvioSesiSc = false;
        }
        return realizarEnvioSesiSc;
    }

    public void setRealizarEnvioSesiSc(Boolean realizarEnvioSesiSc) {
        this.realizarEnvioSesiSc = realizarEnvioSesiSc;
    }

    public Integer getCarenciaRenovacao() {
        return carenciaRenovacao;
    }

    public void setCarenciaRenovacao(Integer carenciaRenovacao) {
        this.carenciaRenovacao = carenciaRenovacao;
    }

    public Questionario getQuestionarioPrimeiraCompra() {
        return questionarioPrimeiraCompra;
    }

    public void setQuestionarioPrimeiraCompra(Questionario questionarioPrimeiraCompra) {
        this.questionarioPrimeiraCompra = questionarioPrimeiraCompra;
    }

    public Questionario getQuestionarioPrimeiraVisita() {
        return questionarioPrimeiraVisita;
    }

    public void setQuestionarioPrimeiraVisita(Questionario questionarioPrimeiraVisita) {
        this.questionarioPrimeiraVisita = questionarioPrimeiraVisita;
    }

    public Questionario getQuestionarioRematricula() {
        return questionarioRematricula;
    }

    public void setQuestionarioRematricula(Questionario questionarioRematricula) {
        this.questionarioRematricula = questionarioRematricula;
    }

    public Questionario getQuestionarioRetorno() {
        return questionarioRetorno;
    }

    public void setQuestionarioRetorno(Questionario questionarioRetorno) {
        this.questionarioRetorno = questionarioRetorno;
    }

    public Questionario getQuestionarioRetornoCompra() {
        return questionarioRetornoCompra;
    }

    public void setQuestionarioRetornoCompra(Questionario questionarioRetornoCompra) {
        this.questionarioRetornoCompra = questionarioRetornoCompra;
    }

    public long getNrDiasVigenteQuestionarioRetorno() {
        return nrDiasVigenteQuestionarioRetorno;
    }

    public void setNrDiasVigenteQuestionarioRetorno(long nrDiasVigenteQuestionarioRetorno) {
        this.nrDiasVigenteQuestionarioRetorno = nrDiasVigenteQuestionarioRetorno;
    }

    public long getNrDiasVigenteQuestionarioRematricula() {
        return nrDiasVigenteQuestionarioRematricula;
    }

    public void setNrDiasVigenteQuestionarioRematricula(long nrDiasVigenteQuestionarioRematricula) {
        this.nrDiasVigenteQuestionarioRematricula = nrDiasVigenteQuestionarioRematricula;
    }

    public long getNrDiasVigenteQuestionarioRetornoCompra() {
        return nrDiasVigenteQuestionarioRetornoCompra;
    }

    public void setNrDiasVigenteQuestionarioRetornoCompra(long nrDiasVigenteQuestionarioRetornoCompra) {
        this.nrDiasVigenteQuestionarioRetornoCompra = nrDiasVigenteQuestionarioRetornoCompra;
    }

    public String getChavePublicaSESC() {
        if (chavePublicaSESC == null) {
            chavePublicaSESC = "";
        }
        return chavePublicaSESC;
    }

    public void setChavePublicaSESC(String chavePublicaSESC) {
        this.chavePublicaSESC = chavePublicaSESC;
    }

    public String getUsuarioApiSescGo() {
        return usuarioApiSescGo;
    }

    public void setUsuarioApiSescGo(String usuarioApiSescGo) {
        this.usuarioApiSescGo = usuarioApiSescGo;
    }

    public String getSenhaApiSescGo() {
        return senhaApiSescGo;
    }

    public void setSenhaApiSescGo(String senhaApiSescGo) {
        this.senhaApiSescGo = senhaApiSescGo;
    }
}
