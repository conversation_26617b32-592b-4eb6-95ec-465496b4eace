package com.pacto.adm.core.entities.objecao;

import com.pacto.adm.core.enumerador.objecaotipogrupo.ObjecaoTipoGrupoEnum;
import com.pacto.adm.core.enumerador.objecaotipogrupo.ObjecaoTipoGrupoEnumConverter;

import javax.persistence.Column;
import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "objecao")
public class Objecao {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;

    @Column(name = "ativo", columnDefinition = "DEFAULT true")
    private Boolean ativo;

    private String comentario;

    private String descricao;

    @Column(name = "grupo", length = 50)
    private String grupo;

    @Convert(converter = ObjecaoTipoGrupoEnumConverter.class)
    @Column(name = "tipogrupo", length = 2)
    private ObjecaoTipoGrupoEnum tipoGrupo;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Boolean getAtivo() {
        return ativo;
    }

    public void setAtivo(Boolean ativo) {
        this.ativo = ativo;
    }

    public String getComentario() {
        return comentario;
    }

    public void setComentario(String comentario) {
        this.comentario = comentario;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getGrupo() {
        return grupo;
    }

    public void setGrupo(String grupo) {
        this.grupo = grupo;
    }

    public ObjecaoTipoGrupoEnum getTipoGrupo() {
        return tipoGrupo;
    }

    public void setTipoGrupo(ObjecaoTipoGrupoEnum tipoGrupo) {
        this.tipoGrupo = tipoGrupo;
    }
}
