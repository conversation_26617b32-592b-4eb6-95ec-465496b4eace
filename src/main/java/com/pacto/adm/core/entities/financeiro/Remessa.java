package com.pacto.adm.core.entities.financeiro;

import com.pacto.config.annotations.RelationalField;
import com.pacto.adm.core.entities.empresa.Empresa;

import javax.persistence.*;

@Entity
public class Remessa {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private Integer situacaoRemessa;

    @ManyToOne
    @RelationalField
    @JoinColumn(name = "movParcela", foreignKey = @ForeignKey(name = "fk_cliente_empresa"))
    private Empresa empresa;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getSituacaoRemessa() {
        return situacaoRemessa;
    }

    public void setSituacaoRemessa(Integer situacaoRemessa) {
        this.situacaoRemessa = situacaoRemessa;
    }

    public Empresa getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Empresa empresa) {
        this.empresa = empresa;
    }
}
