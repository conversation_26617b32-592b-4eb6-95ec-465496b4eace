package com.pacto.adm.core.entities.grupodesconto;

import com.pacto.adm.core.enumerador.situacaoaluno.SituacaoAlunoConverter;
import com.pacto.adm.core.enumerador.situacaoaluno.SituacaoDoAlunoEnum;
import com.pacto.adm.core.enumerador.tipodesconto.TipoDescontoConverter;
import com.pacto.adm.core.enumerador.tipodesconto.TipoDescontoEnum;
import com.pacto.adm.core.enumerador.tipogrupo.TipoGrupoConverter;
import com.pacto.adm.core.enumerador.tipogrupo.TipoGrupoEnum;

import javax.persistence.Column;
import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;


@Entity
@Table(name = "grupo")
public class GrupoDesconto {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;

    @Column(name = "descricao", length = 45, nullable = false)
    private String descricao;

    @Column(name = "percentualdescontogrupo", nullable = false)
    private Double percentualDescontoGrupo;

    @Column(name = "quantidademinimaaluno", columnDefinition = "DEFAULT 0")
    private Integer quantidadeMinimaAluno;

    @Convert(converter = SituacaoAlunoConverter.class)
    @Column(name = "situacaoaluno", length = 2)
    private SituacaoDoAlunoEnum situacaoAluno;

    @Convert(converter = TipoGrupoConverter.class)
    @Column(name = "tipo", length = 2)
    private TipoGrupoEnum tipo;

    @Convert(converter = TipoDescontoConverter.class)
    @Column(name = "tipodesconto", length = 2, nullable = false)
    private TipoDescontoEnum tipoDesconto;

    @Column(name = "valordescontogrupo", nullable = false)
    private Double valorDescontoGrupo;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Double getPercentualDescontoGrupo() {
        return percentualDescontoGrupo;
    }

    public void setPercentualDescontoGrupo(Double percentualDescontoGrupo) {
        this.percentualDescontoGrupo = percentualDescontoGrupo;
    }

    public Integer getQuantidadeMinimaAluno() {
        return quantidadeMinimaAluno;
    }

    public void setQuantidadeMinimaAluno(Integer quantidadeMinimaAluno) {
        this.quantidadeMinimaAluno = quantidadeMinimaAluno;
    }

    public SituacaoDoAlunoEnum getSituacaoAluno() {
        return situacaoAluno;
    }

    public void setSituacaoAluno(SituacaoDoAlunoEnum situacaoAluno) {
        this.situacaoAluno = situacaoAluno;
    }

    public TipoGrupoEnum getTipo() {
        return tipo;
    }

    public void setTipo(TipoGrupoEnum tipo) {
        this.tipo = tipo;
    }

    public TipoDescontoEnum getTipoDesconto() {
        return tipoDesconto;
    }

    public void setTipoDesconto(TipoDescontoEnum tipoDesconto) {
        this.tipoDesconto = tipoDesconto;
    }

    public Double getValorDescontoGrupo() {
        return valorDescontoGrupo;
    }

    public void setValorDescontoGrupo(Double valorDescontoGrupo) {
        this.valorDescontoGrupo = valorDescontoGrupo;
    }
}
