package com.pacto.adm.core.entities;

import org.springframework.beans.BeanUtils;

import javax.persistence.ConstraintMode;
import javax.persistence.Entity;
import javax.persistence.ForeignKey;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import java.math.BigDecimal;
import java.util.Objects;

@Entity
public class MovProdutoParcela implements Cloneable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    @ManyToOne
    @JoinColumn(name = "recibopagamento", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private ReciboPagamento reciboPagamento;
    @ManyToOne
    @JoinColumn(name = "movparcela", foreignKey = @ForeignKey(name = "fk_movprodutoparcela_movparcela"))
    private MovParcela movParcela;
    @ManyToOne
    @JoinColumn(name = "movproduto", foreignKey = @ForeignKey(name = "fk_movprodutoparcela_movproduto"))
    private MovProduto movProduto;
    private BigDecimal valorPago;
    private Integer movParcelaOriginalMultaJuros;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public ReciboPagamento getReciboPagamento() {
        return reciboPagamento;
    }

    public void setReciboPagamento(ReciboPagamento reciboPagamento) {
        this.reciboPagamento = reciboPagamento;
    }

    public MovParcela getMovParcela() {
        return movParcela;
    }

    public void setMovParcela(MovParcela movParcela) {
        this.movParcela = movParcela;
    }

    public MovProduto getMovProduto() {
        return movProduto;
    }

    public void setMovProduto(MovProduto movProduto) {
        this.movProduto = movProduto;
    }

    public BigDecimal getValorPago() {
        return valorPago;
    }

    public void setValorPago(BigDecimal valorPago) {
        this.valorPago = valorPago;
    }

    public Integer getMovParcelaOriginalMultaJuros() {
        return movParcelaOriginalMultaJuros;
    }

    public void setMovParcelaOriginalMultaJuros(Integer movParcelaOriginalMultaJuros) {
        this.movParcelaOriginalMultaJuros = movParcelaOriginalMultaJuros;
    }

    public MovProdutoParcela clone() {
        MovProdutoParcela movProdutoParcela = new MovProdutoParcela();
        BeanUtils.copyProperties(this, movProdutoParcela);
        return movProdutoParcela;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        MovProdutoParcela that = (MovProdutoParcela) o;
        return Objects.equals(codigo, that.codigo)
                && Objects.equals(reciboPagamento, that.reciboPagamento)
                && Objects.equals(movParcela, that.movParcela)
                && Objects.equals(movProduto, that.movProduto)
                && Objects.equals(valorPago, that.valorPago)
                && Objects.equals(movParcelaOriginalMultaJuros, that.movParcelaOriginalMultaJuros);
    }

    @Override
    public int hashCode() {
        return Objects.hash(codigo, reciboPagamento, movParcela, movProduto, valorPago, movParcelaOriginalMultaJuros);
    }
}
