package com.pacto.adm.core.entities.clubeDeVantagens;

import com.pacto.adm.core.entities.empresa.Empresa;
import com.pacto.adm.core.enumerador.TipoItemCampanhaEnum;
import com.pacto.adm.core.enumerador.TipoItemCampanhaEnumConverter;
import com.pacto.config.annotations.NomeEntidadeLog;
import com.pacto.config.annotations.RelationalField;

import javax.persistence.Column;
import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.ForeignKey;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;

@Entity
@NomeEntidadeLog("ItemCampanha")
public class ItemCampanha implements Cloneable{
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;

    @RelationalField
    @ManyToOne
    @JoinColumn(name = "campanha", foreignKey = @ForeignKey(name = "fk_itemcampanha_campanhaduracao"))
    private CampanhaDuracao campanhaDuracao;

    private Integer chaveestrangeira;

    @Convert(converter = TipoItemCampanhaEnumConverter.class)
    @Column(name = "tipoitem")
    private TipoItemCampanhaEnum tipoItemCampanha;

    private Integer duracao;

    private Integer pontos;

    @RelationalField
    @ManyToOne
    @JoinColumn(name = "empresa", foreignKey = @ForeignKey(name = "itemcampanha_fk_empresa"))
    private Empresa empresa;

    @Column(columnDefinition = "default 'SEG,TER,QUA,QUI,SEX,SAB,DOM'")
    private String diasativos;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public CampanhaDuracao getCampanhaDuracao() {
        return campanhaDuracao;
    }

    public void setCampanhaDuracao(CampanhaDuracao campanha) {
        this.campanhaDuracao = campanha;
    }

    public Integer getChaveestrangeira() {
        return chaveestrangeira;
    }

    public void setChaveestrangeira(Integer chaveestrangeira) {
        this.chaveestrangeira = chaveestrangeira;
    }

    public TipoItemCampanhaEnum getTipoItemCampanha() {
        return tipoItemCampanha;
    }

    public void setTipoItemCampanha(TipoItemCampanhaEnum tipoItemCampanha) {
        this.tipoItemCampanha = tipoItemCampanha;
    }

    public Integer getDuracao() {
        return duracao;
    }

    public void setDuracao(Integer duracao) {
        this.duracao = duracao;
    }

    public Integer getPontos() {
        return pontos;
    }

    public void setPontos(Integer pontos) {
        this.pontos = pontos;
    }

    public Empresa getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Empresa empresa) {
        this.empresa = empresa;
    }

    public String getDiasativos() {
        return diasativos;
    }

    public void setDiasativos(String diasativos) {
        this.diasativos = diasativos;
    }
}
