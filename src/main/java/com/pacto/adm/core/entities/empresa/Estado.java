package com.pacto.adm.core.entities.empresa;

import com.pacto.config.annotations.NomeEntidadeLog;
import com.pacto.config.annotations.UseOnlyThisToLog;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;

@Entity
@NomeEntidadeLog("Estado")
public class Estado {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    @UseOnlyThisToLog
    private String nome;
    private String sigla;

    public Estado() {
    }

    public Estado(String sigla) {
        this.sigla = sigla;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getSigla() {
        return sigla;
    }

    public void setSigla(String sigla) {
        this.sigla = sigla;
    }
}
