package com.pacto.adm.core.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;
import java.util.List;

@JsonInclude
@Schema(name = "Aviso Interno", description = "Informações do aviso interno")
public class AvisoInternoDTO {

    @Schema(description = "Código único identificador do aviso interno", example = "83")
    private Integer codigo;

    @Schema(description = "Conteúdo do aviso interno", example = "FERIADO NO DIA PRIMEIRO DE MAIO - ACADEMIA FECHADA")
    private String conteudo;

    @Schema(description = "Data da publicação do aviso interno", example = "1745808000000")
    private Long dataPublicacao;

    @Schema(description = "Data de expiração do aviso interno", example = "1745971200000")
    private Long dataExpiracao;

    @Schema(description = "Indica se o aviso ainda está ativo", example = "false")
    private Boolean ativo;

    @Schema(description = "Indica se o aviso está vísivel para todas as pessoas", example = "true")
    private Boolean visivelParaTodos;

    @Schema(description = "Autor do aviso")
    private UsuarioDTO autor;

    @Schema(description = "Lista de destinatários do aviso (caso não seja mostrado para todos)", example = "[0,1,2,3,4,5]")
    private List<Integer> destinatarios;

    @Schema(description = "Lista de perfils que o aviso irá aparecer", example = "[0,1,2,3,4,5,6]")
    private List<Integer> perfis;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getConteudo() {
        return conteudo;
    }

    public void setConteudo(String conteudo) {
        this.conteudo = conteudo;
    }

    public Long getDataPublicacao() {
        return dataPublicacao;
    }

    public void setDataPublicacao(Long dataPublicacao) {
        this.dataPublicacao = dataPublicacao;
    }

    public Long getDataExpiracao() {
        return dataExpiracao;
    }

    public void setDataExpiracao(Long dataExpiracao) {
        this.dataExpiracao = dataExpiracao;
    }

    public Boolean getAtivo() {
        return ativo;
    }

    public void setAtivo(Boolean ativo) {
        this.ativo = ativo;
    }

    public Boolean getVisivelParaTodos() {
        return visivelParaTodos;
    }

    public void setVisivelParaTodos(Boolean visivelParaTodos) {
        this.visivelParaTodos = visivelParaTodos;
    }

    public UsuarioDTO getAutor() {
        return autor;
    }

    public void setAutor(UsuarioDTO autor) {
        this.autor = autor;
    }

    public List<Integer> getDestinatarios() {
        return destinatarios;
    }

    public void setDestinatarios(List<Integer> destinatarios) {
        this.destinatarios = destinatarios;
    }

    public List<Integer> getPerfis() {
        return perfis;
    }

    public void setPerfis(List<Integer> perfis) {
        this.perfis = perfis;
    }

}
