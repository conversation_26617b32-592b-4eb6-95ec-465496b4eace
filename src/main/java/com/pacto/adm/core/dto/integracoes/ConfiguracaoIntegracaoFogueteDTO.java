package com.pacto.adm.core.dto.integracoes;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.pacto.adm.core.dto.empresa.EmpresaDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@JsonInclude
@Schema(name = "Configuração de Integração com Foguete", description = "Configurações necessárias para integração com a plataforma Foguete.")
public class ConfiguracaoIntegracaoFogueteDTO {

    @Schema(description = "Código identificador da configuração da integração com o Foguete.", example = "1")
    private Integer codigo;

    @Schema(description = "Indica se a integração com o Foguete está habilitada.", example = "true")
    private Boolean habilitada;

    @Schema(description = "Token de autenticação da API do Foguete.", example = "foguete-token-123")
    private String tokenApi;

    @Schema(description = "Detalhes da empresa associada à configuração da integração.")
    private EmpresaDTO empresa;

    @Schema(description = "Código do produto relacionado à integração com o Foguete.", example = "101")
    private Integer produto;

    @Schema(description = "URL API do Foguete.", example = "https://dominio.com.br/api")
    private String urlApi;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Boolean getHabilitada() {
        if (habilitada == null) {
            habilitada = false;
        }
        return habilitada;
    }

    public void setHabilitada(Boolean habilitada) {
        this.habilitada = habilitada;
    }

    public String getTokenApi() {
        return tokenApi;
    }

    public void setTokenApi(String tokenApi) {
        this.tokenApi = tokenApi;
    }

    public EmpresaDTO getEmpresa() {
        return empresa;
    }

    public void setEmpresa(EmpresaDTO empresa) {
        this.empresa = empresa;
    }

    public Integer getProduto() {
        return produto;
    }

    public void setProduto(Integer produto) {
        this.produto = produto;
    }

    public String getUrlApi() {
        return urlApi;
    }

    public void setUrlApi(String urlApi) {
        this.urlApi = urlApi;
    }
}
