package com.pacto.adm.core.dto.integracoes;

import com.pacto.adm.core.dto.empresa.EmpresaDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;

@Schema(name = "Configuração de Integração com Kobana", description = "Contém os dados necessários para integrar a empresa com o sistema Kobana.")
public class ConfiguracaoIntegracaoKobanaDTO {

    @Schema(description = "Código identificador da integração com o Kobana.", example = "12345")
    private int codigo;

    @Schema(description = "Ambiente onde a integração com o Kobana está configurada.", example = "1")
    private int ambiente;

    @Schema(description = "E-mail registrado para a integração com o Kobana.", example = "<EMAIL>")
    private String email;

    @Schema(description = "CNPJ da empresa associada à integração com o Kobana.", example = "12345678000195")
    private String business_cnpj;

    @Schema(description = "Nome de usuário ou apelido utilizado na integração com o Kobana.", example = "empresa_nickname")
    private String nickname;

    @Schema(description = "Razão social da empresa associada à integração com o Kobana.", example = "Empresa Ltda")
    private String business_legal_name;

    @Schema(description = "Token de acesso à API do Kobana.", example = "xyz12345token")
    private String api_access_token;

    @Schema(description = "Indica se a integração com o Kobana está ativa.", example = "true")
    private boolean ativo;

    @Schema(description = "Empresa associada à integração com o Kobana.")
    private EmpresaDTO empresa;

    @Schema(description = "ID único da configuração da integração com o Kobana.", example = "789")
    private Integer id;

    @Schema(description = "Data de criação da configuração de integração com o Kobana.", example = "2022-04-15T14:30:00Z")
    private Date created_At;

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getBusiness_cnpj() {
        return business_cnpj;
    }

    public void setBusiness_cnpj(String business_cnpj) {
        this.business_cnpj = business_cnpj;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public int getCodigo() {
        return codigo;
    }

    public void setCodigo(int codigo) {
        this.codigo = codigo;
    }

    public EmpresaDTO getEmpresa() {
        return empresa;
    }

    public void setEmpresa(EmpresaDTO empresa) {
        this.empresa = empresa;
    }

    public boolean isAtivo() {
        return ativo;
    }

    public void setAtivo(boolean ativo) {
        this.ativo = ativo;
    }

    public String getBusiness_legal_name() {
        return business_legal_name;
    }

    public void setBusiness_legal_name(String business_legal_name) {
        this.business_legal_name = business_legal_name;
    }

    public String getApi_access_token() {
        return api_access_token;
    }

    public void setApi_access_token(String api_access_token) {
        this.api_access_token = api_access_token;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Date getCreated_At() {
        return created_At;
    }

    public void setCreated_At(Date created_At) {
        this.created_At = created_At;
    }

    public int getAmbiente() {
        return ambiente;
    }

    public void setAmbiente(int ambiente) {
        this.ambiente = ambiente;
    }
}
