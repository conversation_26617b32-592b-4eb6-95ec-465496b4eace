package com.pacto.adm.core.dto.integracoes;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.pacto.adm.core.dto.empresa.EmpresaDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@JsonInclude
@Schema(name = "Configuração de Integração GymPass", description = "Configurações para integração com o sistema GymPass.")
public class ConfiguracaoIntegracaoGymPassDTO {

    @Schema(description = "Código de identificação para integração com o GymPass.", example = "codigo-gympass-123")
    private String codigoGympass;

    @Schema(description = "Token de autenticação utilizado na integração com a API do GymPass.", example = "token-api-gympass-xyz")
    private String tokenApiGympass;

    @Schema(description = "Limite de acessos permitidos por dia para o GymPass.", example = "5")
    private Integer limiteDeAcessosPorDia;

    @Schema(description = "Limite de aulas permitidas por dia para o GymPass.", example = "3")
    private Integer limiteDeAulasPorDia;

    @Schema(description = "Detalhes da empresa associada à configuração de integração GymPass.")
    private EmpresaDTO empresa;

    public String getCodigoGympass() {
        return codigoGympass;
    }

    public void setCodigoGympass(String codigoGympass) {
        this.codigoGympass = codigoGympass;
    }

    public String getTokenApiGympass() {
        return tokenApiGympass;
    }

    public void setTokenApiGympass(String tokenApiGympass) {
        this.tokenApiGympass = tokenApiGympass;
    }

    public EmpresaDTO getEmpresa() {
        return empresa;
    }

    public void setEmpresa(EmpresaDTO empresa) {
        this.empresa = empresa;
    }

    public Integer getLimiteDeAcessosPorDia() {
        return limiteDeAcessosPorDia;
    }

    public void setLimiteDeAcessosPorDia(Integer limiteDeAcessosPorDia) {
        this.limiteDeAcessosPorDia = limiteDeAcessosPorDia;
    }

    public Integer getLimiteDeAulasPorDia() {
        return limiteDeAulasPorDia;
    }

    public void setLimiteDeAulasPorDia(Integer limiteDeAulasPorDia) {
        this.limiteDeAulasPorDia = limiteDeAulasPorDia;
    }
}
