package com.pacto.adm.core.dto.enveloperesposta.creditotreino;

import com.pacto.adm.core.dto.controlecreditotreino.ControleCreditoTreinoDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "Representação das respostas")
public class EnvelopeRespostaListControleCreditoTreinoDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private ControleCreditoTreinoDTO content;

    public ControleCreditoTreinoDTO getContent() {
        return content;
    }

    public void setContent(ControleCreditoTreinoDTO content) {
        this.content = content;
    }

    public final static String resposta = "{"
            + "  \"content\": [{" + EnvelopeRespostaControleCreditoTreinoDTO.atributos + " }],"
            + "  \"totalElements\": 1,"
            + "  \"totalPages\": 1,"
            + "  \"first\": true,"
            + "  \"last\": true,"
            + "  \"size\": 10,"
            + "  \"number\": 0"
            + "}";



}
