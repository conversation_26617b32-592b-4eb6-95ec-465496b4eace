package com.pacto.adm.core.dto.enveloperesposta.tiposvariaveis;

import io.swagger.v3.oas.annotations.media.Schema;


//@Schema(name = "Resposta Número Inteiro", description = "Representação das respostas das requisições que devolvem um número inteiro")
public class EnvelopeRespostaInteger {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas", example = "5")
    private Integer content;

    public Integer getContent() {
        return content;
    }

    public void setContent(Integer content) {
        this.content = content;
    }

    public static final String resposta = "{\"content\": 5}";

}
