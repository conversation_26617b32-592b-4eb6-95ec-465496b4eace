package com.pacto.adm.core.dto.enveloperesposta.sorteio;

import com.pacto.adm.core.dto.ReciboPagamentoDTO;
import com.pacto.adm.core.dto.SorteioDTO;
import com.pacto.adm.core.dto.enveloperesposta.cliente.EnvelopeRespostaClienteDTO;
import com.pacto.adm.core.dto.enveloperesposta.empresa.EnvelopeRespostaEmpresaDTO;
import com.pacto.adm.core.dto.enveloperesposta.pagamento.EnvelopeRespostaMovPagamentoDTO;
import com.pacto.adm.core.dto.enveloperesposta.usuario.EnvelopeRespostaUsuarioDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "Representação das respostas")
public class EnvelopeRespostaSorteioDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private SorteioDTO content;

    public SorteioDTO getContent() {
        return content;
    }

    public void setContent(SorteioDTO content) {
        this.content = content;
    }


    public static final String atributos =
            "\"codigo\": 123, "
                    + "\"nome\": \"Sorteio de Aniversário\", "
                    + "\"dataSorteio\": \"2025-06-15T10:00:00Z\", "
                    + "\"data\": \"15/06/2025\", "
                    + "\"observacoes\": \"Sorteio válido para todos os clientes cadastrados.\", "
                    + "\"cliente\": {" + EnvelopeRespostaClienteDTO.atributos + "}, "
                    + "\"usuario\": {" + EnvelopeRespostaUsuarioDTO.atributos + "}, "
                    + "\"nomeUsuario\": \"João Silva\", "
                    + "\"parcelas\": [1, 2, 3, 4, 5]";

    public static final String resposta = "{\"content\": {" + atributos + "}}";
    public static final String requestBody = "{" + atributos + "}";


}
