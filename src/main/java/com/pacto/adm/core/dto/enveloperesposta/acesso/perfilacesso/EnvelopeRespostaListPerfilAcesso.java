package com.pacto.adm.core.dto.enveloperesposta.acesso.perfilacesso;

import com.pacto.adm.core.dto.enveloperesposta.acesso.permissao.EnvelopeRespostaPermissao;
import com.pacto.adm.core.dto.swagger.RepresentacaoPaginadorDTO;
import com.pacto.adm.core.entities.PerfilAcesso;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

//@Schema(name = "Respostas Lista de Perfis de Acesso", description = "Representação das respostas contendo uma lista de perfis de acesso ao sistema")
public class EnvelopeRespostaListPerfilAcesso extends RepresentacaoPaginadorDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private List<PerfilAcesso> content;

    public List<PerfilAcesso> getContent() {
        return content;
    }

    public void setContent(List<PerfilAcesso> content) {
        this.content = content;
    }
    public final static String resposta = "{"
            + "  \"content\": [{" + EnvelopeRespostaPerfilAcesso.atributos + " }],"
            + "  \"totalElements\": 1,"
            + "  \"totalPages\": 1,"
            + "  \"first\": true,"
            + "  \"last\": true,"
            + "  \"size\": 10,"
            + "  \"number\": 0"
            + "}";


}
