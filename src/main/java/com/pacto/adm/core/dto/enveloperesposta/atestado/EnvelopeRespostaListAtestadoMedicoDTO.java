package com.pacto.adm.core.dto.enveloperesposta.atestado;

import com.pacto.adm.core.dto.AtestadoMedicoDTO;
import com.pacto.adm.core.dto.enveloperesposta.EnvelopeRespostaPaginacaoDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

//@Schema(name = "Resposta Lista de Atestados Médico", description = "Representação das respostas contendo as informações dos atestados")
public class EnvelopeRespostaListAtestadoMedicoDTO extends EnvelopeRespostaPaginacaoDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private List<AtestadoMedicoDTO> content;

    public List<AtestadoMedicoDTO> getContent() {
        return content;
    }

    public void setContent(List<AtestadoMedicoDTO> content) {
        this.content = content;
    }

    public final static String resposta = "{"
            + "  \"content\": [{" + EnvelopeRespostaAtestadoMedicoDTO.atributos + " }],"
            + "  \"totalElements\": 1,"
            + "  \"totalPages\": 1,"
            + "  \"first\": true,"
            + "  \"last\": true,"
            + "  \"size\": 10,"
            + "  \"number\": 0"
            + "}";

}
