package com.pacto.adm.core.dto.enveloperesposta.vendaavulsa;

import com.pacto.adm.core.dto.VendaAvulsaDTO;
import com.pacto.adm.core.dto.enveloperesposta.EnvelopeRespostaItemVendaAvulsaDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

@Schema(description = "Representação das respostas")
public class EnvelopeRespostaListVendaAvulsaDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private List<VendaAvulsaDTO> content;

    public List<VendaAvulsaDTO> getContent() {
        return content;
    }

    public void setContent(List<VendaAvulsaDTO> content) {
        this.content = content;
    }

    public final static String resposta = "{"
            + "  \"content\": [{" + EnvelopeRespostaVendaAvulsaDTO.atributos + " }],"
            + "  \"totalElements\": 1,"
            + "  \"totalPages\": 1,"
            + "  \"first\": true,"
            + "  \"last\": true,"
            + "  \"size\": 10,"
            + "  \"number\": 0"
            + "}";
}
