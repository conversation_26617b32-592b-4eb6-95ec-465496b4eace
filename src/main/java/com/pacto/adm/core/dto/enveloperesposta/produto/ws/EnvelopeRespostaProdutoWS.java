package com.pacto.adm.core.dto.enveloperesposta.produto.ws;

import com.pacto.adm.core.dto.negociacao.ProdutoWS;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "Representação das respostas")
public class EnvelopeRespostaProdutoWS {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private ProdutoWS content;

    public ProdutoWS getContent() {
        return content;
    }

    public void setContent(ProdutoWS content) {
        this.content = content;
    }


    public static final String atributos =
            "\"codigo\": 101, "
                    + "\"descricao\": \"Kit Treinamento Funcional\", "
                    + "\"valor\": 149.90, "
                    + "\"nrDiasVigencia\": 30, "
                    + "\"tipoProduto\": \"Equipamento\", "
                    + "\"dataCobranca\": \"2025-06-01\"";

    public static final String resposta = "{\"content\": {" + atributos + "}}";
    public static final String requestBody = "{" + atributos + "}";

}
