package com.pacto.adm.core.dto;

import java.util.Date;

public class ClienteComGympassDTO {

    private ClienteDTO cliente;
    private ContratoDTO contrato;
    private Date dataInicio;
    private Integer qtdDiasGympass;
    private UsuarioDTO responsavel;
    private String tokenGympass;

    public ClienteComGympassDTO(Date dataInicio, Integer qtdDiasGympass, String tokenGympass, ClienteDTO cliente, UsuarioDTO responsavel, ContratoDTO contrato) {
        this.dataInicio = dataInicio;
        this.qtdDiasGympass = qtdDiasGympass;
        this.cliente = cliente;
        this.responsavel = responsavel;
        this.contrato = contrato;
        this.tokenGympass = tokenGympass;
    }

    public ClienteDTO getCliente() {
        return cliente;
    }

    public ContratoDTO getContrato() {
        return contrato;
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public Integer getQtdDiasGympass() {
        return qtdDiasGympass;
    }

    public UsuarioDTO getResponsavel() {
        return responsavel;
    }

    public String getTokenGympass() {
        return tokenGympass;
    }
}
