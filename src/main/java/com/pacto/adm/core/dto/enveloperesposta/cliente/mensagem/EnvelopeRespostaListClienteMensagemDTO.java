package com.pacto.adm.core.dto.enveloperesposta.cliente.mensagem;

import com.pacto.adm.core.dto.ClienteMensagemDTO;
import com.pacto.adm.core.dto.enveloperesposta.EnvelopeRespostaPaginacaoDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

//@Schema(name = "Resposta Lista de Classificação", description = "Representação das respostas contendo uma lista de classificação")
public class EnvelopeRespostaListClienteMensagemDTO extends EnvelopeRespostaPaginacaoDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private List<ClienteMensagemDTO> content;

    public List<ClienteMensagemDTO> getContent() {
        return content;
    }

    public void setContent(List<ClienteMensagemDTO> content) {
        this.content = content;
    }

    public final static String resposta = "{"
            + "  \"content\": [{" + EnvelopeRespostaClienteMensagemDTO.atributos + " }],"
            + "  \"totalElements\": 1,"
            + "  \"totalPages\": 1,"
            + "  \"first\": true,"
            + "  \"last\": true,"
            + "  \"size\": 10,"
            + "  \"number\": 0"
            + "}";

}
