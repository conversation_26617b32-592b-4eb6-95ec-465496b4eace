package com.pacto.adm.core.dto.enveloperesposta.modalidade.valor;

import com.pacto.adm.core.dto.negociacao.ValorModalidadeDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "Representação das respostas")
public class EnvelopeRespostaValorModalidadeDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private ValorModalidadeDTO content;

    public ValorModalidadeDTO getContent() {
        return content;
    }

    public void setContent(ValorModalidadeDTO content) {
        this.content = content;
    }

    public static final String atributos =
            "\"modalidade\": 12, "
                    + "\"totalCreditos\": 20, "
                    + "\"pacote\": 3, "
                    + "\"valor\": 250.00";

    public static final String resposta = "{\"content\": {" + atributos + "}}";
    public static final String requestBody = "{" + atributos + "}";


}
