package com.pacto.adm.core.dto;

import com.pacto.adm.core.dto.empresa.EmpresaDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import java.util.Date;

@Schema(description = "Informações das parcelas")
public class MovParcelaDTO {

    @Schema(description = "Código único identificador da parcela", example = "1")
    private Integer codigo;

    @Schema(description = "Código do contrato associado à parcela", example = "10")
    private Integer contrato;

    @Schema(description = "Detalhes da empresa associada à parcela")
    private EmpresaDTO empresa;

    @Schema(description = "Descrição da parcela", example = "Parcela referente ao mês de Janeiro")
    private String descricao;

    @Schema(description = "Data de vencimento da parcela", example = "2023-01-31")
    private Date dataVencimento;

    @Schema(description = "Data de lançamento da parcela", example = "2023-01-01")
    private Date dataLancamento;

    @Schema(description = "Valor da parcela", example = "199.99")
    private BigDecimal valor;

    @Schema(description = "Situação atual da parcela (ex.: PG, EA, RG, CA)", example = "PG")
    private String situacao;

    @Schema(description = "Código da pessoa associada à parcela", example = "5")
    private Integer pessoa;

    @Schema(description = "Descrição da situação da parcela", example = "Pago")
    private String situacaoDescricao;

    @Schema(description = "Número de tentativas de pagamento realizadas", example = "2")
    private Integer nrTentativas;
    private Date dataPagamento;

    public MovParcelaDTO() {
    }

    public MovParcelaDTO(Integer codigo, BigDecimal valor) {
        this.codigo = codigo;
        this.valor = valor;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getContrato() {
        return contrato;
    }

    public void setContrato(Integer contrato) {
        this.contrato = contrato;
    }

    public EmpresaDTO getEmpresa() {
        return empresa;
    }

    public void setEmpresa(EmpresaDTO empresa) {
        this.empresa = empresa;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public BigDecimal getValor() {
        return valor;
    }

    public void setValor(BigDecimal valor) {
        this.valor = valor;
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public Date getDataVencimento() {
        return dataVencimento;
    }

    public void setDataVencimento(Date dataVencimento) {
        this.dataVencimento = dataVencimento;
    }

    public Date getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(Date dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public Integer getPessoa() {
        return pessoa;
    }

    public void setPessoa(Integer pessoa) {
        this.pessoa = pessoa;
    }

    public void setSituacaoDescricao(String situacaoDescricao) {

        this.situacaoDescricao = situacaoDescricao;
    }

    public String getSituacaoDescricao() {
        return situacaoDescricao;
    }

    public Integer getNrTentativas() {
        return nrTentativas;
    }

    public void setNrTentativas(Integer nrTentativas) {
        this.nrTentativas = nrTentativas;
    }

    public Date getDataPagamento() {
        return dataPagamento;
    }

    public void setDataPagamento(Date dataPagamento) {
        this.dataPagamento = dataPagamento;
    }
}
