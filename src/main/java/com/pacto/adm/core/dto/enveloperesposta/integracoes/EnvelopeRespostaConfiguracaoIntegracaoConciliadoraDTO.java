package com.pacto.adm.core.dto.enveloperesposta.integracoes;

import com.pacto.adm.core.dto.enveloperesposta.empresa.EnvelopeRespostaEmpresaDTO;
import com.pacto.adm.core.dto.integracoes.ConfiguracaoIntegracaoConciliadoraDTO;
import io.swagger.v3.oas.annotations.media.Schema;

public class EnvelopeRespostaConfiguracaoIntegracaoConciliadoraDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private ConfiguracaoIntegracaoConciliadoraDTO content;

    public ConfiguracaoIntegracaoConciliadoraDTO getContent() {
        return content;
    }

    public void setContent(ConfiguracaoIntegracaoConciliadoraDTO content) {
        this.content = content;
    }

    public static final String atributos =
            "\"usarConciliadora\": false, "
                    + "\"empresaConciliadora\": \"Empresa XYZ\", "
                    + "\"senhaConciliadora\": \"senha-conciliadora-123\", "
                    + "\"empresa\": {" + EnvelopeRespostaEmpresaDTO.atributos + "}";

    public static final String atributosSemEmpresa =
            "\"usarConciliadora\": false, "
                    + "\"empresaConciliadora\": \"Empresa XYZ\", "
                    + "\"senhaConciliadora\": \"senha-conciliadora-123\", "
                    + "\"empresa\": \"INFORMAÇÕES DA EMPRESA\"";

    public final static String resposta = "{\"content\": {" + atributos + "}}";
    public final static String requestBody = "{" + atributos + "}";

}
