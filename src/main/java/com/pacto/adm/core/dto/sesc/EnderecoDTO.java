package com.pacto.adm.core.dto.sesc;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@NoArgsConstructor
@Data
public class EnderecoDTO {
    private Integer codigo;
    private Integer pessoa;
    private String endereco;
    private String complemento;
    private String numero;
    private String bairro;
    private String cep;
    private String tipoEndereco;
    private Boolean enderecoCorrespondencia;
    private String ltdlng;
    private Date dataatualizacao;
    private Boolean enfileirado;
}
