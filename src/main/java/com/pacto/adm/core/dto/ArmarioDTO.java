package com.pacto.adm.core.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;

@JsonInclude
@Schema(name = "Armário", description = "Informações detalhadas do armário")
public class ArmarioDTO {

    @Schema(description = "Código único identificador do armário", example = "1")
    private Integer codigo;

    @Schema(description = "Descrição do Armário", example = "Armário Grande")
    private String descricao;

    @Schema(description = "Tamanho do Armário", example = "2")
    private Integer tamanhoArmario;

    @Schema(description = "Código da empresa que possui este armário", example = "1")
    private Integer empresa;

    @Schema(description = "Código do responsável pelo cadastro do armário no sistema", example = "3")
    private Integer responsavelCadastro;

    @Schema(description = "Data que foi cadastrado o armário", example = "2024-03-01T00:00:00Z")
    private Date dataCadastro;

    @Schema(description = "Status do armário", example = "Armário Alugado")
    private Integer status;

    @Schema(description = "Númeração do armário", example = "2")
    private Integer numeracao;

    @Schema(description = "Código do aluguel atual que o armário está vinculado", example = "109")
    private Integer aluguelAtual;

    @Schema(description = "Código do grupo do armário", example = "1")
    private String grupo;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Integer getTamanhoArmario() {
        return tamanhoArmario;
    }

    public void setTamanhoArmario(Integer tamanhoArmario) {
        this.tamanhoArmario = tamanhoArmario;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public Integer getResponsavelCadastro() {
        return responsavelCadastro;
    }

    public void setResponsavelCadastro(Integer responsavelCadastro) {
        this.responsavelCadastro = responsavelCadastro;
    }

    public Date getDataCadastro() {
        return dataCadastro;
    }

    public void setDataCadastro(Date dataCadastro) {
        this.dataCadastro = dataCadastro;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getNumeracao() {
        return numeracao;
    }

    public void setNumeracao(Integer numeracao) {
        this.numeracao = numeracao;
    }

    public Integer getAluguelAtual() {
        return aluguelAtual;
    }

    public void setAluguelAtual(Integer aluguelAtual) {
        this.aluguelAtual = aluguelAtual;
    }

    public String getGrupo() {
        return grupo;
    }

    public void setGrupo(String grupo) {
        this.grupo = grupo;
    }
}
