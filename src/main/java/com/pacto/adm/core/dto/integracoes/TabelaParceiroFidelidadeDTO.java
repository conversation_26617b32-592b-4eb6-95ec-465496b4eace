package com.pacto.adm.core.dto.integracoes;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

@JsonInclude
public class TabelaParceiroFidelidadeDTO {

    @Schema(description = "Código único identificador da tabela de fidelidade", example = "1")
    private Integer codigo;

    @Schema(description = "Nome da tabela de fidelidade", example = "Tabela Padrão de Pontos")
    private String nomeTabela;

    @Schema(description = "Indica se esta tabela é a padrão para recorrências", example = "true")
    private boolean defaultRecorrencia;

    @Schema(description = "Lista de itens associados à tabela de fidelidade")
    private List<TabelaParceiroFidelidadeItemDTO> itens;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNomeTabela() {
        return nomeTabela;
    }

    public void setNomeTabela(String nomeTabela) {
        this.nomeTabela = nomeTabela;
    }

    public boolean isDefaultRecorrencia() {
        return defaultRecorrencia;
    }

    public void setDefaultRecorrencia(boolean defaultRecorrencia) {
        this.defaultRecorrencia = defaultRecorrencia;
    }

    public List<TabelaParceiroFidelidadeItemDTO> getItens() {
        return itens;
    }

    public void setItens(List<TabelaParceiroFidelidadeItemDTO> itens) {
        this.itens = itens;
    }
}
