package com.pacto.adm.core.dto.integracoes;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.ArrayList;
import java.util.List;

@JsonInclude
@Data
@Schema(name = "Configurações de Integrações das Empresas", description = "Contém os parâmetros de configuração para todas as integrações possíveis de uma empresa com sistemas terceiros.")
public class ConfiguracoesIntegracoesEmpresasDTO {

    // ADM
    @Schema(description = "Configuração da integração com o sistema MyWellness.")
    private ConfiguracaoIntegracaoMyWellnessDTO configuracaoIntegracaoMyWellness;

    @Schema(description = "Configuração da integração com o sistema MentorWeb.")
    private ConfiguracaoIntegracaoMentorWebDTO configuracaoIntegracaoMentorWeb;

    @Schema(description = "Configuração da integração com o sistema de estacionamento.")
    private ConfiguracaoIntegracaoEstacionamentoDTO configuracaoIntegracaoEstacionamento;

    @Schema(description = "Configuração da integração com o sistema CDL/SPC.")
    private ConfiguracaoIntegracaoCDLSPCDTO configuracaoIntegracaoCDLSPC;

    @Schema(description = "Configuração da integração com os recursos do FacilitePay.")
    private ConfiguracaoIntegracaoRecursosFacilitePayDTO configuracaoIntegracaoRecursosFacilitePay;

    @Schema(description = "Configuração da integração com o sistema Delsoft.")
    private ConfiguracaoIntegracaoDelsoftDTO configuracaoIntegracaoDelsoft;

    @Schema(description = "Configuração da integração com o sistema Vitio.")
    private ConfiguracaoIntegracaoVitioDTO configuracaoIntegracaoVitio;

    @Schema(description = "Configuração da integração com o parceiro de fidelidade.")
    private ConfiguracaoIntegracaoParceiroFidelidadeDTO configuracaoIntegracaoParceiroFidelidade;

    @Schema(description = "Configuração da integração com o sistema Gympass.")
    private ConfiguracaoIntegracaoGymPassDTO configuracaoIntegracaoGympass;

    @Schema(description = "Configuração da integração com o sistema GoGood.")
    private ConfiguracaoIntegracaoGoGoodDTO configuracaoIntegracaoGoGood;

    @Schema(description = "Configuração da integração com a plataforma Nuvemshop.")
    private ConfiguracaoIntegracaoNuvemshopDTO configuracaoIntegracaoNuvemshop;

    @Schema(description = "Configuração da integração com o sistema Foguete.")
    private ConfiguracaoIntegracaoFogueteDTO configuracaoIntegracaoFoguete;

    @Schema(description = "Configuração para envio de acesso na rede Pratique.")
    private ConfiguracaoIntegracaoEnvioAcessoPratiqueDTO configuracaoIntegracaoEnvioAcessoPratique;

    // CRM
    @Schema(description = "Configuração da integração com o sistema de notificações via Webhook.")
    private ConfiguracaoIntegracaoNotificacaoWebhookDTO configuracaoIntegracaoNotificacaoWebhook;

    @Schema(description = "Configuração da integração com o sistema AmigoFit.")
    private ConfiguracaoIntegracaoAmigoFitDTO configuracaoIntegracaoAmigoFit;

    @Schema(description = "Configuração da integração com o sistema WeHelp.")
    private ConfiguracaoIntegracaoWeHelpDTO configuracaoIntegracaoWeHelp;

    @Schema(description = "Configuração da integração com o sistema BuzzLead.")
    private ConfiguracaoIntegracaoBuzzLeadDTO configuracaoIntegracaoBuzzLead;

    @Schema(description = "Configuração da integração com o RD Station.")
    private ConfiguracaoEmpresaRDStationDTO configuracaoIntegracaoRDStation;

    @Schema(description = "Configuração da integração com o HubSpot.")
    private ConfiguracaoEmpresaHubSpotDTO configuracaoIntegracaoHubSpot;

    @Schema(description = "Configuração da integração com o Bitrix24.")
    private ConfiguracaoEmpresaBitrix24DTO configuracaoEmpresaBitrix24;

    @Schema(description = "Configuração da integração para envio de SMS.")
    private ConfiguracaoIntegracaoSmsDTO configuracaoIntegracaoSms;

    @Schema(description = "Configuração da integração com o WordPress.")
    private ConfiguracaoIntegracaoWordPressDTO configuracaoIntegracaoWordPress;

    @Schema(description = "Configuração da integração com a plataforma Join.")
    private ConfiguracaoIntegracaoJoinDTO configuracaoIntegracaoJoin;

    @Schema(description = "Configuração da integração genérica para recebimento de leads.")
    private ConfiguracaoIntegracaoGenericaLeadsDTO configuracaoIntegracaoGenericaLeads;

    @Schema(description = "Configuração da integração genérica para recebimento de leads do Gymbot.")
    private ConfiguracaoIntegracaoGenericaLeadsGymbotDTO configuracaoIntegracaoGenericaLeadsGymbot;

    @Schema(description = "Configurações das integrações com bots de conversa.")
    private List<ConfiguracaoIntegracaoBotConversaDTO> configuracaoIntegracaoBotConversa;

    @Schema(description = "Configurações das integrações com o Gymbot Pro.")
    private List<ConfiguracaoIntegracaoGymbotProDTO> configuracaoIntegracaoGymbotPro;

    @Schema(description = "Configuração da integração com o ManyChat.")
    private ConfiguracaoIntegracaoManyChatDTO configuracaoIntegracaoManyChat;
    // Financeiro
    @Schema(description = "Configuração da integração com o relatório do sistema F360.")
    private ConfiguracaoIntegracaoF360RelatorioDTO configuracaoIntegracaoF360Relatorio;

    @Schema(description = "Configuração da integração com o sistema contábil AlterData.")
    private ConfiguracaoIntegracaoSistemaContabilAlterDataDTO configuracaoIntegracaoSistemaContabilAlterData;

    @Schema(description = "Configuração da integração com a conciliadora financeira.")
    private ConfiguracaoIntegracaoConciliadoraDTO configuracaoIntegracaoConciliadora;
    // PactoPay
    @Schema(description = "Configuração da integração com o PjBank.")
    private ConfiguracaoIntegracaoPjBankDTO configuracaoIntegracaoPjBank;
    // Treino
    @Schema(description = "Configuração da integração com o sistema Spivi.")
    private ConfiguracaoIntegracaoSpiviDTO configuracaoIntegracaoSpivi;
    //Total PASS
    @Schema(description = "Configuração da integração com o TotalPass.")
    private ConfiguracaoIntegracaoTotalPassDTO configuracaoIntegracaoTotalpass;

    @Schema(description = "Configurações das integrações com a plataforma Pluggy.")
    private List<ConfiguracaoIntegracaoPluggyDTO> configuracaoIntegracaoPluggy;
    // Financeiro
    @Schema(description = "Configuração da integração com o sistema financeiro Kobana.")
    private ConfiguracaoIntegracaoKobanaDTO configuracaoIntegracaoKobana;
    // Sistema SESC
    @Schema(description = "Configuração de Integração com o SESC")
    private ConfiguracaoIntegracaoSescDfDTO configuracaoIntegracaoSescDf;

    public ConfiguracoesIntegracoesEmpresasDTO() {
        this.configuracaoIntegracaoMyWellness = new ConfiguracaoIntegracaoMyWellnessDTO();
        this.configuracaoIntegracaoMentorWeb = new ConfiguracaoIntegracaoMentorWebDTO();
        this.configuracaoIntegracaoEstacionamento = new ConfiguracaoIntegracaoEstacionamentoDTO();
        this.configuracaoIntegracaoCDLSPC = new ConfiguracaoIntegracaoCDLSPCDTO();
        this.configuracaoIntegracaoDelsoft = new ConfiguracaoIntegracaoDelsoftDTO();
        this.configuracaoIntegracaoVitio = new ConfiguracaoIntegracaoVitioDTO();
        this.configuracaoIntegracaoParceiroFidelidade = new ConfiguracaoIntegracaoParceiroFidelidadeDTO();
        this.configuracaoIntegracaoGympass = new ConfiguracaoIntegracaoGymPassDTO();
        this.configuracaoIntegracaoNuvemshop = new ConfiguracaoIntegracaoNuvemshopDTO();
        this.configuracaoIntegracaoFoguete = new ConfiguracaoIntegracaoFogueteDTO();

        this.configuracaoIntegracaoNotificacaoWebhook = new ConfiguracaoIntegracaoNotificacaoWebhookDTO();
        this.configuracaoIntegracaoAmigoFit = new ConfiguracaoIntegracaoAmigoFitDTO();
        this.configuracaoIntegracaoWeHelp = new ConfiguracaoIntegracaoWeHelpDTO();
        this.configuracaoIntegracaoBuzzLead = new ConfiguracaoIntegracaoBuzzLeadDTO();
        this.configuracaoIntegracaoRDStation = new ConfiguracaoEmpresaRDStationDTO();
        this.configuracaoIntegracaoHubSpot = new ConfiguracaoEmpresaHubSpotDTO();
        this.setConfiguracaoEmpresaBitrix24(new ConfiguracaoEmpresaBitrix24DTO());

        this.configuracaoIntegracaoF360Relatorio = new ConfiguracaoIntegracaoF360RelatorioDTO();
        this.configuracaoIntegracaoConciliadora = new ConfiguracaoIntegracaoConciliadoraDTO();
        this.configuracaoIntegracaoSistemaContabilAlterData = new ConfiguracaoIntegracaoSistemaContabilAlterDataDTO();

        this.configuracaoIntegracaoPjBank = new ConfiguracaoIntegracaoPjBankDTO();
        this.configuracaoIntegracaoKobana = new ConfiguracaoIntegracaoKobanaDTO();

        this.configuracaoIntegracaoSpivi = new ConfiguracaoIntegracaoSpiviDTO();
        this.configuracaoIntegracaoPluggy = new ArrayList<>();
        this.setConfiguracaoIntegracaoBotConversa(new ArrayList<>());
        this.setConfiguracaoIntegracaoGymbotPro(new ArrayList<>());

        this.configuracaoIntegracaoSescDf = new ConfiguracaoIntegracaoSescDfDTO();
    }
}
