package com.pacto.adm.core.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.pacto.adm.core.entities.Pessoa;
import io.swagger.v3.oas.annotations.media.Schema;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(description = "Informações do Cliente que realizou algum acesso a academia", name = "Cliente Local de Acesso")
public class ClienteLocalDeAcessoDTO {

    @Schema(description = "Código único identificador das informações do Cliente Local de Acesso", example = "93")
    private Integer codigo;

    @Schema(description = "Código da matrícula identificador do Cliente", example = "445")
    private Integer codigoMatricula;

    @Schema(description = "Matrícula identificadora do cliente", example = "000445")
    private String matricula;

    @Schema(description = "Situacao do cliente que realizou o acesso", example = "ACESSO_AUTORIZADO")
    private String situacao;

    @Schema(description = "Informações completas do cliente")
    private Pessoa pessoa;


    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getCodigoMatricula() {
        return codigoMatricula;
    }

    public void setCodigoMatricula(Integer codigoMatricula) {
        this.codigoMatricula = codigoMatricula;
    }

    public Pessoa getPessoa() {
        return pessoa;
    }

    public void setPessoa(Pessoa pessoa) {
        this.pessoa = pessoa;
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public String getMatricula() {
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }
}
