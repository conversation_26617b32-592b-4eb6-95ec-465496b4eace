package com.pacto.adm.core.dto.negociacao;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.ArrayList;
import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(name = "Configurações Contrato", description = "Dados e configurações para geração de um contrato ou simulação de um contrato.")
public class ConfigsContratoDTO {

    @Schema(description = "Código do contrato base utilizado como referência.", example = "1024")
    private Integer contratoBase;

    @Schema(description = "Código da empresa associada ao contrato.", example = "1")
    private Integer empresa;

    @Schema(description = "Código do usuário que está realizando a operação.", example = "123")
    private Integer usuario;

    @Schema(description = "Código do usuário que autorizou o desconto, se aplicável.", example = "456")
    private Integer usuarioAutorizouDesconto;

    @Schema(description = "Código do usuário responsável pela alteração da data base, se houver.", example = "789")
    private Integer usuarioDataBase;

    @Schema(description = "Código do plano selecionado para o contrato.", example = "201")
    private Integer plano;

    @Schema(description = "Código do cliente vinculado ao contrato.", example = "345")
    private Integer cliente;

    @Schema(description = "Código da duração do plano.", example = "6")
    private Integer duracao;

    @Schema(description = "Código da condição de pagamento escolhida.", example = "2")
    private Integer condicao;

    @Schema(description = "Código do horário escolhido para o plano.", example = "15")
    private Integer horario;

    @Schema(description = "Código do convênio utilizado no contrato.", example = "30")
    private Integer codigoConvenio;

    @Schema(description = "Dia do vencimento do cartão, se aplicável.", example = "10")
    private Integer vencimentoCartao;

    @Schema(description = "Código do convênio que aplica desconto adicional.", example = "40")
    private Integer convenioDesconto;

    @Schema(description = "Valor fixo de desconto extra aplicado ao contrato.", example = "50.00")
    private Double descontoExtraValor;

    @Schema(description = "Percentual de desconto extra aplicado ao contrato.", example = "10.5")
    private Double descontoExtraPercentual;

    @Schema(description = "Observações adicionais sobre o contrato.", example = "Contrato da Academia")
    private String observacao;

    @Schema(description = "Cupom de desconto aplicado ao contrato.", example = "PROMO2025")
    private String cupom;

    @Schema(description = "Data de lançamento do contrato (timestamp).", example = "1715472000000")
    private Long dataLancamento;

    @Schema(description = "Data da primeira parcela (timestamp).", example = "1718054400000")
    private Long diaPrimeiraParcela;

    @Schema(description = "Tipo de contrato selecionado.", example = "Mensal")
    private String tipoContrato;

    @Schema(description = "Data de início da vigência do contrato (timestamp).", example = "1715472000000")
    private Long inicio;

    @Schema(description = "Indica se deve ser gerado link de pagamento.", example = "true")
    private Boolean gerarLink = false;

    @Schema(description = "Indica se os valores devem ser arredondados.", example = "true")
    private Boolean arredondar = false;

    @Schema(description = "Dados de arredondamento aplicados à negociação.")
    private ArredondamentoParcelaDTO arredondamento;

    @Schema(description = "Lista de pacotes selecionados no contrato.")
    private List<PlanoModalidadeDTO> pacotes = new ArrayList<>();

    @Schema(description = "Lista de modalidades contratadas.")
    private List<PlanoModalidadeDTO> modalidades = new ArrayList<>();

    @Schema(description = "Lista de produtos vinculados ao contrato.")
    private List<PlanoProdutoDTO> produtos = new ArrayList<>();

    @Schema(description = "Configurações avançadas do contrato.")
    private ConfigsAvancadasDTO configuracoesAvancadas;

    @Schema(description = "Informações sobre o crédito do plano de duração.")
    private PlanoDuracaoCreditoDTO credito;

    @Schema(description = "Lista de parcelas do contrato.")
    private List<ParcelasEditarNegociacaoNovo> parcelas = new ArrayList<>();

    @Schema(description = "Origem do sistema onde o contrato foi criado.", example = "1")
    private Integer origemSistema;

    @Schema(description = "Indica se houve desconto por renovação antecipada.", example = "false")
    private Boolean descontoRenovacaoAntecipada = false;

    @Schema(description = "Indica se a data de início do contrato foi alterada.", example = "true")
    private Boolean alterouDataInicioContrato;

    @Schema(description = "Indica se o tipo de contrato foi alterado.", example = "false")
    private Boolean alterouTipoContrato;

    public List<PlanoModalidadeDTO> getPacotes() {
        return pacotes;
    }

    public void setPacotes(List<PlanoModalidadeDTO> pacotes) {
        this.pacotes = pacotes;
    }

    public List<ParcelasEditarNegociacaoNovo> getParcelas() {
        return parcelas;
    }

    public void setParcelas(List<ParcelasEditarNegociacaoNovo> parcelas) {
        this.parcelas = parcelas;
    }

    public PlanoDuracaoCreditoDTO getCredito() {
        return credito;
    }

    public void setCredito(PlanoDuracaoCreditoDTO credito) {
        this.credito = credito;
    }

    public ConfigsAvancadasDTO getConfiguracoesAvancadas() {
        return configuracoesAvancadas;
    }

    public void setConfiguracoesAvancadas(ConfigsAvancadasDTO configuracoesAvancadas) {
        this.configuracoesAvancadas = configuracoesAvancadas;
    }

    public Integer getContratoBase() {
        return contratoBase;
    }

    public void setContratoBase(Integer contratoBase) {
        this.contratoBase = contratoBase;
    }

    public Integer getVencimentoCartao() {
        return vencimentoCartao;
    }

    public void setVencimentoCartao(Integer vencimentoCartao) {
        this.vencimentoCartao = vencimentoCartao;
    }

    public List<PlanoProdutoDTO> getProdutos() {
        return produtos;
    }

    public void setProdutos(List<PlanoProdutoDTO> produtos) {
        this.produtos = produtos;
    }

    public Long getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(Long dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public Integer getDuracao() {
        return duracao;
    }

    public void setDuracao(Integer duracao) {
        this.duracao = duracao;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public Integer getPlano() {
        return plano;
    }

    public void setPlano(Integer plano) {
        this.plano = plano;
    }

    public List<PlanoModalidadeDTO> getModalidades() {
        return modalidades;
    }

    public void setModalidades(List<PlanoModalidadeDTO> modalidades) {
        this.modalidades = modalidades;
    }

    public Integer getCondicao() {
        return condicao;
    }

    public void setCondicao(Integer condicao) {
        this.condicao = condicao;
    }

    public Integer getCliente() {
        return cliente;
    }

    public void setCliente(Integer cliente) {
        this.cliente = cliente;
    }

    public Integer getHorario() {
        return horario;
    }

    public void setHorario(Integer horario) {
        this.horario = horario;
    }

    public Boolean getGerarLink() {
        return gerarLink;
    }

    public void setGerarLink(Boolean gerarLink) {
        this.gerarLink = gerarLink;
    }

    public Integer getUsuario() {
        return usuario;
    }

    public void setUsuario(Integer usuario) {
        this.usuario = usuario;
    }

    public Integer getUsuarioDataBase() {
        return usuarioDataBase;
    }

    public void setUsuarioDataBase(Integer usuarioDataBase) {
        this.usuarioDataBase = usuarioDataBase;
    }

    public Long getInicio() {
        return inicio;
    }

    public void setInicio(Long inicio) {
        this.inicio = inicio;
    }

    public String getTipoContrato() {
        return tipoContrato;
    }

    public void setTipoContrato(String tipoContrato) {
        this.tipoContrato = tipoContrato;
    }

    public Integer getConvenioDesconto() {
        return convenioDesconto;
    }

    public void setConvenioDesconto(Integer convenioDesconto) {
        this.convenioDesconto = convenioDesconto;
    }

    public Double getDescontoExtraValor() {
        return descontoExtraValor;
    }

    public void setDescontoExtraValor(Double descontoExtraValor) {
        this.descontoExtraValor = descontoExtraValor;
    }

    public Double getDescontoExtraPercentual() {
        return descontoExtraPercentual;
    }

    public void setDescontoExtraPercentual(Double descontoExtraPercentual) {
        this.descontoExtraPercentual = descontoExtraPercentual;
    }

    public String getCupom() {
        return cupom;
    }

    public void setCupom(String cupom) {
        this.cupom = cupom;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public Long getDiaPrimeiraParcela() {
        return diaPrimeiraParcela;
    }

    public void setDiaPrimeiraParcela(Long diaPrimeiraParcela) {
        this.diaPrimeiraParcela = diaPrimeiraParcela;
    }

    public Boolean getArredondar() {
        if(arredondar == null){
            arredondar = false;
        }
        return arredondar;
    }

    public void setArredondar(Boolean arredondar) {
        this.arredondar = arredondar;
    }

    public ArredondamentoParcelaDTO getArredondamento() {
        return arredondamento;
    }

    public void setArredondamento(ArredondamentoParcelaDTO arredondamento) {
        this.arredondamento = arredondamento;
    }

    public Integer getOrigemSistema() {
        return origemSistema;
    }

    public void setOrigemSistema(Integer origemSistema) {
        this.origemSistema = origemSistema;
    }

    public Integer getCodigoConvenio() {
        return codigoConvenio;
    }

    public void setCodigoConvenio(Integer codigoConvenio) {
        this.codigoConvenio = codigoConvenio;
    }

    public Boolean getDescontoRenovacaoAntecipada() {
        return descontoRenovacaoAntecipada;
    }

    public void setDescontoRenovacaoAntecipada(Boolean descontoRenovacaoAntecipada) {
        this.descontoRenovacaoAntecipada = descontoRenovacaoAntecipada;
    }

    public Integer getUsuarioAutorizouDesconto() {
        return usuarioAutorizouDesconto;
    }

    public void setUsuarioAutorizouDesconto(Integer usuarioAutorizouDesconto) {
        this.usuarioAutorizouDesconto = usuarioAutorizouDesconto;
    }

    public Boolean getAlterouDataInicioContrato() {
        return alterouDataInicioContrato;
    }

    public void setAlterouDataInicioContrato(Boolean alterouDataInicioContrato) {
        this.alterouDataInicioContrato = alterouDataInicioContrato;
    }

    public Boolean getAlterouTipoContrato() {
        return alterouTipoContrato;
    }

    public void setAlterouTipoContrato(Boolean alterouTipoContrato) {
        this.alterouTipoContrato = alterouTipoContrato;
    }
}
