package com.pacto.adm.core.dto.enveloperesposta.vendaavulsa;

import com.pacto.adm.core.dto.negociacao.ResultadoVendaDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "Representação das respostas")
public class EnvelopeRespostaResultadoVendaDTO {


    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private ResultadoVendaDTO content;

    public ResultadoVendaDTO getContent() {
        return content;
    }

    public void setContent(ResultadoVendaDTO content) {
        this.content = content;
    }

    public static final String atributos =
            "\"venda\": 1001, "
                    + "\"link\": \"https://pactosolucoes.com.br/venda/1001\", "
                    + "\"whatsapp\": \"https://wa.me/5511999999999\"";

    public static final String resposta = "{\"content\": {" + atributos + "}}";
    public static final String requestBody = "{" + atributos + "}";


}
