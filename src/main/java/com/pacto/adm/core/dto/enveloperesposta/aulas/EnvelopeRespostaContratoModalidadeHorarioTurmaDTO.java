package com.pacto.adm.core.dto.enveloperesposta.aulas;

import com.pacto.adm.core.dto.nivelturma.NivelTurmaDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "Representação das respostas")
public class EnvelopeRespostaContratoModalidadeHorarioTurmaDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private NivelTurmaDTO content;

    public NivelTurmaDTO getContent() {
        return content;
    }

    public void setContent(NivelTurmaDTO content) {
        this.content = content;
    }

    public static final String atributos =
            "\"codigo\": 40, "
                    + "\"percOcupacao\": 75.0, "
                    + "\"percDesconto\": 10.0, "
                    + "\"horarioTurma\": {" + EnvelopeRespostaHorarioTurmaDTO.atributos + "}";

    public final static String resposta = "{\"content\": {" + atributos + "}}";

}
