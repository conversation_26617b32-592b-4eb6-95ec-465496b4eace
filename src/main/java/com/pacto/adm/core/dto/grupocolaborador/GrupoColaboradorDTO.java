package com.pacto.adm.core.dto.grupocolaborador;

import com.pacto.adm.core.dto.UsuarioDTO;
import com.pacto.adm.core.dto.empresa.EmpresaDTO;

import java.util.List;

public class GrupoColaboradorDTO {

    private Integer codigo;
    private String descricao;
    private EmpresaDTO empresa;
    private UsuarioDTO gerente;
    private List<GrupoColaboradorParticipanteDTO> grupoColaboradorParticipantes;
    private String situacaoGrupo;
    private String tipoGrupo;
    private boolean semGrupo;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public EmpresaDTO getEmpresa() {
        return empresa;
    }

    public void setEmpresa(EmpresaDTO empresa) {
        this.empresa = empresa;
    }

    public UsuarioDTO getGerente() {
        return gerente;
    }

    public void setGerente(UsuarioDTO gerente) {
        this.gerente = gerente;
    }

    public List<GrupoColaboradorParticipanteDTO> getGrupoColaboradorParticipantes() {
        return grupoColaboradorParticipantes;
    }

    public void setGrupoColaboradorParticipantes(List<GrupoColaboradorParticipanteDTO> grupoColaboradorParticipantes) {
        this.grupoColaboradorParticipantes = grupoColaboradorParticipantes;
    }

    public String getSituacaoGrupo() {
        return situacaoGrupo;
    }

    public void setSituacaoGrupo(String situacaoGrupo) {
        this.situacaoGrupo = situacaoGrupo;
    }

    public String getTipoGrupo() {
        return tipoGrupo;
    }

    public void setTipoGrupo(String tipoGrupo) {
        this.tipoGrupo = tipoGrupo;
    }

    public boolean isSemGrupo() {
        return semGrupo;
    }

    public void setSemGrupo(boolean semGrupo) {
        this.semGrupo = semGrupo;
    }
}
