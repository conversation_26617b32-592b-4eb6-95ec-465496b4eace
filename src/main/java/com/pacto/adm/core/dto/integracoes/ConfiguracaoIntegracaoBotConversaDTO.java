package com.pacto.adm.core.dto.integracoes;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.pacto.adm.core.dto.empresa.EmpresaDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@JsonInclude
@Schema(name = "Configuração de Integração Bot de Conversa", description = "Configurações para integração com o Bot de Conversa para automação de interações.")
public class ConfiguracaoIntegracaoBotConversaDTO {

    @Schema(description = "Código identificador da configuração do Bot de Conversa.", example = "123")
    private Integer codigo;

    @Schema(description = "Indica se a integração com o Bot de Conversa está ativa.", example = "true")
    private Boolean ativo;

    @Schema(description = "URL do WebHook utilizado para integração com o Bot de Conversa.", example = "https://api.exemplo.com/webhook-bot")
    private String urlWebHooBotConversa;

    @Schema(description = "Código da empresa associada à configuração do Bot de Conversa.", example = "1")
    private Integer empresa;

    @Schema(description = "Descrição da configuração do Bot de Conversa.", example = "Integração com Bot para atendimento ao cliente.")
    private String descricao;

    @Schema(description = "Tipo de fluxo utilizado na integração do Bot de Conversa.", example = "1")
    private Integer tipoFluxo;

    @Schema(description = "Fase do fluxo do Bot de Conversa.", example = "INICIAL")
    private String fase;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getUrlWebHooBotConversa() {
        return urlWebHooBotConversa;
    }

    public void setUrlWebHooBotConversa(String urlWebHooBotConversa) {
        this.urlWebHooBotConversa = urlWebHooBotConversa;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Integer getTipoFluxo() {
        return tipoFluxo;
    }

    public void setTipoFluxo(Integer tipoFluxo) {
        this.tipoFluxo = tipoFluxo;
    }

    public String getFase() {
        return fase;
    }

    public void setFase(String fase) {
        this.fase = fase;
    }

    public Boolean getAtivo() {
        return ativo;
    }

    public void setAtivo(Boolean ativo) {
        this.ativo = ativo;
    }
}
