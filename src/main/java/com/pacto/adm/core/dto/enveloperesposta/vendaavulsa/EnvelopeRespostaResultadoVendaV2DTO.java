package com.pacto.adm.core.dto.enveloperesposta.vendaavulsa;

import com.pacto.adm.core.dto.enveloperesposta.parcelas.EnvelopeRespostaParcelasDTO;
import com.pacto.adm.core.dto.negociacao.ResultadoVendaDTO;
import com.pacto.adm.core.dto.negociacao.ResultadoVendaV2DTO;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "Representação das respostas")
public class EnvelopeRespostaResultadoVendaV2DTO {


    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private ResultadoVendaV2DTO content;

    public ResultadoVendaV2DTO getContent() {
        return content;
    }

    public void setContent(ResultadoVendaV2DTO content) {
        this.content = content;
    }

    public static final String atributos =
            "\"resultadoVendaDTO\": {" + EnvelopeRespostaResultadoVendaDTO.atributos + "}, "
                    + "\"parcelasDTO\": [{" + EnvelopeRespostaParcelasDTO.atributos + "}]";

    public static final String resposta = "{\"content\": {" + atributos + "}}";

    public static final String requestBody = "{" + atributos + "}";



}
