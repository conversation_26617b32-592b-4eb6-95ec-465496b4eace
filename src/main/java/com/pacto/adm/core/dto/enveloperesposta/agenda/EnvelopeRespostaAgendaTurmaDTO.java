package com.pacto.adm.core.dto.enveloperesposta.agenda;

import com.pacto.adm.core.dto.empresa.EmpresaDTO;
import com.pacto.adm.core.dto.enveloperesposta.empresa.EnvelopeRespostaEmpresaConfigEstacionamentoDTO;
import com.pacto.adm.core.dto.enveloperesposta.empresa.EnvelopeRespostaParceiroFidelidadeDTO;
import com.pacto.adm.core.dto.enveloperesposta.endereco.EnvelopeRespostaCidade;
import com.pacto.adm.core.dto.enveloperesposta.endereco.EnvelopeRespostaEstado;
import com.pacto.adm.core.dto.negociacao.AgendaTurmaDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "Representação das respostas")
public class EnvelopeRespostaAgendaTurmaDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private AgendaTurmaDTO content;

    public AgendaTurmaDTO getContent() {
        return content;
    }

    public void setContent(AgendaTurmaDTO content) {
        this.content = content;
    }


    public static final String atributos =
            "\"horario\": \"08:00 - 09:00\", " +
                    "\"aulasDiaSemana\": {" +
                    "  \"SEGUNDA_FEIRA\": [" +
                    "    {" +
                    "      \"codigo\": 1," +
                    "      \"modalidade\": 101," +
                    "      \"ocupacao\": 12," +
                    "      \"capacidade\": 20," +
                    "      \"diaSemana\": \"Segunda-feira\"," +
                    "      \"turma\": \"Turma A\"," +
                    "      \"inicio\": \"08:00\"," +
                    "      \"fim\": \"09:00\"," +
                    "      \"ambiente\": \"Sala 101\"," +
                    "      \"professor\": \"Prof. João Silva\"," +
                    "      \"coletiva\": false," +
                    "      \"bloquearMatriculasAcimaLimite\": false," +
                    "      \"idadeMinimaMeses\": 72," +
                    "      \"idadeMaximaMeses\": 144," +
                    "      \"niveis\": [1, 2]," +
                    "      \"nivelCodigoMgb\": \"1\"" +
                    "    }" +
                    "  ]" +
                    "}";

    public static final String resposta = "{\"content\": {" + atributos + "}}";
    public static final String requestBody = "{" + atributos + "}";
}
