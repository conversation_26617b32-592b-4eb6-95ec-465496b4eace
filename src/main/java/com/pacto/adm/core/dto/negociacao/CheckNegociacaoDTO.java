package com.pacto.adm.core.dto.negociacao;

import com.pacto.adm.core.dto.ClienteRestricaoDTO;
import com.pacto.adm.core.dto.nivelturma.NivelTurmaDTO;
import com.pacto.adm.core.enumerador.TipoAutorizacaoCobrancaEnum;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;
import java.util.Map;

@Schema(name = "Check Negociação", description = "Verificação de negociação com base em planos e créditos sugeridos.")
public class CheckNegociacaoDTO {

    @Schema(description = "Código do plano sugerido para o cliente.", example = "5")
    private Integer planoSugerido;

    @Schema(description = "Quantidade de créditos sugeridos para o cliente.", example = "100")
    private Integer creditosSugerido;

    @Schema(description = "Código do plano de renovação sugerido.", example = "6")
    private Integer planoRenovacao;

    @Schema(description = "Código do plano base para o cliente.", example = "2")
    private Integer planoBase;

    @Schema(description = "Valor total das parcelas em aberto para o cliente.", example = "150.00")
    private Double valorParcelasAberto;

    @Schema(description = "Limite de data para rematrícula, em formato timestamp.", example = "1650518400000")
    private Long limiteDataRematricula;

    @Schema(description = "Data de término do contrato ativo, em formato timestamp.", example = "1672444800000")
    private Long finalContratoAtivo;

    @Schema(description = "Código do contrato de renovação associado.", example = "101")
    private Integer codigoContratoRenovacao;

    @Schema(description = "Código do contrato de rematrícula associado.", example = "102")
    private Integer codigoContratoRematricula;

    @Schema(description = "Indica se é permitido ter um contrato concomitante.", example = "false")
    private Boolean permiteContratoConcomitante = false;

    @Schema(description = "Nível do aluno, utilizado para determinar o plano ou categoria.", example = "3")
    private Integer nivelAluno;

    @Schema(description = "Código do convênio de cobrança aplicado ao cliente.", example = "2")
    private Integer convenioCobranca;

    @Schema(description = "Tipo de autorização para cobrança." +
            "Valores disponíveis:  0 - (Nenhum), 1 - Cartão de Crédito, 2 - Débito em Conta Corrente, 3 - Boleto Bancário, 4 - Pix;",
            example = "PIX", implementation = TipoAutorizacaoCobrancaEnum.class)
    private String tipoAutorizacaoCobranca;

    @Schema(description = "Número do cartão associado à cobrança.", example = "1234567890123456")
    private String nrsCartao;

    @Schema(description = "Data de validade do cartão de cobrança.", example = "12/25")
    private String validadeCartao;

    @Schema(description = "Lista de horários recomendados para o cliente.")
    private List<HorarioTurmaAgendaDTO> horariosTurma;

    @Schema(description = "Indica se deve usar produtos no contrato.", example = "false")
    private Boolean usaProdutos = false;

    @Schema(description = "Indica se deve usar desconto no contrato.", example = "true")
    private Boolean usaDesconto = false;

    @Schema(description = "Lista de restrições associadas ao cliente na negociação.")
    private List<ClienteRestricaoDTO> clienteRestricoes;

    public String getTipoAutorizacaoCobranca() {
        return tipoAutorizacaoCobranca;
    }

    public void setTipoAutorizacaoCobranca(String tipoAutorizacaoCobranca) {
        this.tipoAutorizacaoCobranca = tipoAutorizacaoCobranca;
    }

    public String getNrsCartao() {
        return nrsCartao;
    }

    public void setNrsCartao(String nrsCartao) {
        this.nrsCartao = nrsCartao;
    }

    public String getValidadeCartao() {
        return validadeCartao;
    }

    public void setValidadeCartao(String validadeCartao) {
        this.validadeCartao = validadeCartao;
    }

    public Integer getPlanoSugerido() {
        return planoSugerido;
    }

    public void setPlanoSugerido(Integer planoSugerido) {
        this.planoSugerido = planoSugerido;
    }

    public Integer getPlanoBase() {
        return planoBase;
    }

    public void setPlanoBase(Integer planoBase) {
        this.planoBase = planoBase;
    }

    public Integer getCodigoContratoRematricula() {
        return codigoContratoRematricula;
    }

    public void setCodigoContratoRematricula(Integer codigoContratoRematricula) {
        this.codigoContratoRematricula = codigoContratoRematricula;
    }

    public Boolean getPermiteContratoConcomitante() {
        return permiteContratoConcomitante;
    }

    public void setPermiteContratoConcomitante(Boolean permiteContratoConcomitante) {
        this.permiteContratoConcomitante = permiteContratoConcomitante;
    }

    public Double getValorParcelasAberto() {
        return valorParcelasAberto;
    }

    public void setValorParcelasAberto(Double valorParcelasAberto) {
        this.valorParcelasAberto = valorParcelasAberto;
    }

    public Long getFinalContratoAtivo() {
        return finalContratoAtivo;
    }

    public void setFinalContratoAtivo(Long finalContratoAtivo) {
        this.finalContratoAtivo = finalContratoAtivo;
    }

    public Integer getCodigoContratoRenovacao() {
        return codigoContratoRenovacao;
    }

    public void setCodigoContratoRenovacao(Integer codigoContratoRenovacao) {
        this.codigoContratoRenovacao = codigoContratoRenovacao;
    }

    public Integer getPlanoRenovacao() {
        return planoRenovacao;
    }

    public void setPlanoRenovacao(Integer planoRenovacao) {
        this.planoRenovacao = planoRenovacao;
    }

    public Integer getNivelAluno() {
        return nivelAluno;
    }

    public void setNivelAluno(Integer nivelAluno) {
        this.nivelAluno = nivelAluno;
    }

    public List<HorarioTurmaAgendaDTO> getHorariosTurma() {
        return horariosTurma;
    }

    public void setHorariosTurma(List<HorarioTurmaAgendaDTO> horariosTurma) {
        this.horariosTurma = horariosTurma;
    }

    public Boolean getUsaProdutos() {
        return usaProdutos;
    }

    public void setUsaProdutos(Boolean usaProdutos) {
        this.usaProdutos = usaProdutos;
    }

    public Boolean getUsaDesconto() {
        return usaDesconto;
    }

    public void setUsaDesconto(Boolean usaDesconto) {
        this.usaDesconto = usaDesconto;
    }

    public List<ClienteRestricaoDTO> getClienteRestricoes() {
        return clienteRestricoes;
    }

    public void setClienteRestricoes(List<ClienteRestricaoDTO> clienteRestricoes) {
        this.clienteRestricoes = clienteRestricoes;
    }

    public Long getLimiteDataRematricula() {
        return limiteDataRematricula;
    }

    public void setLimiteDataRematricula(Long limiteDataRematricula) {
        this.limiteDataRematricula = limiteDataRematricula;
    }

    public Integer getConvenioCobranca() {
        return convenioCobranca;
    }

    public void setConvenioCobranca(Integer convenioCobranca) {
        this.convenioCobranca = convenioCobranca;
    }

    public Integer getCreditosSugerido() {
        return creditosSugerido;
    }

    public void setCreditosSugerido(Integer creditosSugerido) {
        this.creditosSugerido = creditosSugerido;
    }
}
