package com.pacto.adm.core.dto.enveloperesposta.integracoes;

import com.pacto.adm.core.dto.enveloperesposta.empresa.EnvelopeRespostaParceiroFidelidadeDTO;
import com.pacto.adm.core.dto.enveloperesposta.integracoes.alterdata.EnvelopeRespostaConfiguracaoContabilAlterData;
import com.pacto.adm.core.dto.enveloperesposta.integracoes.botconversa.EnvelopeRespostaConfiguracaoIntegracaoBotConversaDTO;
import com.pacto.adm.core.dto.enveloperesposta.integracoes.facilitepay.EnvelopeRespostaConfiguracaoIntegracaoFacilitePayDTO;
import com.pacto.adm.core.dto.enveloperesposta.integracoes.spivi.EnvelopeRespostaConfiguracaoIntegracaoSpiviDTO;
import com.pacto.adm.core.dto.integracoes.ConfiguracoesIntegracoesEmpresasDTO;
import io.swagger.v3.oas.annotations.media.Schema;

public class EnvelopeRespostaConfiguracaoIntegracoesEmpresasDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private ConfiguracoesIntegracoesEmpresasDTO content;

    public ConfiguracoesIntegracoesEmpresasDTO getContent() {
        return content;
    }

    public void setContent(ConfiguracoesIntegracoesEmpresasDTO content) {
        this.content = content;
    }

    public static final String atributosSemEmpresa =
            "\"configuracaoIntegracaoMyWellness\": {" + EnvelopeRespostaConfiguracaoIntegracaoMyWellnessDTO.atributosSemEmpresa + "}, "
                    + "\"configuracaoIntegracaoMentorWeb\": {" + EnvelopeRespostaConfiguracaoIntegracaoMentorWeb.atributosSemEmpresa + "}, "
                    + "\"configuracaoIntegracaoEstacionamento\": {" + EnvelopeRespostaConfiguracaoIntegracaoEstacionamentoDTO.atributosSemEmpresa + "}, "
                    + "\"configuracaoIntegracaoCDLSPC\": {" + EnvelopeRespostaConfiguracaoIntegracaoCDLSPCDTO.atributosSemEmpresa + "}, "
                    + "\"configuracaoIntegracaoRecursosFacilitePay\": {" + EnvelopeRespostaConfiguracaoIntegracaoFacilitePayDTO.atributosSemEmpresa + "}, "
                    + "\"configuracaoIntegracaoDelsoft\": {" + EnvelopeRespostaConfiguracaoIntegracaoDelsoftDTO.atributosSemEmpresa + "}, "
                    + "\"configuracaoIntegracaoVitio\": {" + EnvelopeRespostaConfiguracaoIntegracaoVitioDTO.atributosSemEmpresa + "}, "
                    + "\"configuracaoIntegracaoParceiroFidelidade\": {" + EnvelopeRespostaParceiroFidelidadeDTO.atributos + "}, "
                    + "\"configuracaoIntegracaoGympass\": {" + EnvelopeRespostaConfiguracaoIntegracaoGymPassDTO.atributosSemEmpresa + "}, "
                    + "\"configuracaoIntegracaoGoGood\": {" + EnvelopeRespostaConfiguracaoIntegracaoGoGoodDTO.atributosSemEmpresa + "}, "
                    + "\"configuracaoIntegracaoNuvemshop\": {" + EnvelopeRespostaConfiguracaoIntegracaoNuvemshopDTO.atributos + "}, "
                    + "\"configuracaoIntegracaoFoguete\": {" + EnvelopeRespostaConfiguracaoIntegracaoFogueteDTO.atributosSemEmpresa + "}, "
                    + "\"configuracaoIntegracaoEnvioAcessoPratique\": {" + EnvelopeRespostaConfiguracaoIntegracaoEnvioAcessoPratiqueDTO.atributosSemEmpresa + "}, "
                    + "\"configuracaoIntegracaoNotificacaoWebhook\": {" + EnvelopeRespostaConfiguracaoIntegracaoNotificacaoWebhookDTO.atributosSemEmpresa + "}, "
                    + "\"configuracaoIntegracaoAmigoFit\": {" + EnvelopeRespostaConfiguracaoIntegracaoAmigoFitDTO.atributosSemEmpresa + "}, "
                    + "\"configuracaoIntegracaoWeHelp\": {" + EnvelopeRespostaConfiguracaoIntegracaoWeHelpDTO.atributosSemEmpresa + "}, "
                    + "\"configuracaoIntegracaoBuzzLead\": {" + EnvelopeRespostaConfiguracaoIntegracaoBuzzLeadDTO.atributosSemEmpresa + "}, "
                    + "\"configuracaoIntegracaoRDStation\": {" + EnvelopeRespostaConfiguracaoEmpresaRDStationDTO.atributosSemEmpresa + "}, "
                    + "\"configuracaoIntegracaoHubSpot\": {" + EnvelopeRespostaConfiguracaoEmpresaHubSpotDTO.atributos + "}, "
                    + "\"configuracaoEmpresaBitrix24\": {" + EnvelopeRespostaConfiguracaoEmpresaBitrix24DTO.atributos + "}, "
                    + "\"configuracaoIntegracaoSms\": {" + EnvelopeRespostaConfiguracaoIntegracaoSmsDTO.atributosSemEmpresa + "}, "
                    + "\"configuracaoIntegracaoWordPress\": {" + EnvelopeRespostaConfiguracaoIntegracaoWordPressDTO.atributosSemEmpresa + "}, "
                    + "\"configuracaoIntegracaoJoin\": {" + EnvelopeRespostaConfiguracaoIntegracaoJoinDTO.atributosSemEmpresa + "}, "
                    + "\"configuracaoIntegracaoGenericaLeads\": {" + EnvelopeRespostaConfiguracaoIntegracaoGenericaLeadsDTO.atributosSemEmpresa + "}, "
                    + "\"configuracaoIntegracaoGenericaLeadsGymbot\": {" + EnvelopeRespostaConfiguracaoIntegracaoGenericaLeadsGymbotDTO.atributosSemEmpresa + "}, "
                    + "\"configuracaoIntegracaoBotConversa\": [ {" + EnvelopeRespostaConfiguracaoIntegracaoBotConversaDTO.atributos + "} ], "
                    + "\"configuracaoIntegracaoGymbotPro\": [ {" + EnvelopeRespostaConfiguracaoIntegracaoGymBotProDTO.atributos + "} ], "
                    + "\"configuracaoIntegracaoManyChat\": {" + EnvelopeRespostaConfiguracaoIntegracaoManyChatDTO.atributosSemEmpresa + "}, "
                    + "\"configuracaoIntegracaoF360Relatorio\": {" + EnvelopeRespostaConfiguracaoIntegracaoF360DTO.atributosSemEmpresa + "}, "
                    + "\"configuracaoIntegracaoSistemaContabilAlterData\": {" + EnvelopeRespostaConfiguracaoContabilAlterData.atributos + "}, "
                    + "\"configuracaoIntegracaoConciliadora\": {" + EnvelopeRespostaConfiguracaoIntegracaoConciliadoraDTO.atributosSemEmpresa + "}, "
                    + "\"configuracaoIntegracaoPjBank\": {" + EnvelopeRespostaConfiguracaoPjBankDTO.atributosSemEmpresa + "}, "
                    + "\"configuracaoIntegracaoSpivi\": {" + EnvelopeRespostaConfiguracaoIntegracaoSpiviDTO.atributosSemEmpresa + "}, "
                    + "\"configuracaoIntegracaoTotalpass\": {" + EnvelopeRespostaConfiguracaoIntegracaoTotalPassDTO.atributos + "}, "
                    + "\"configuracaoIntegracaoPluggy\": [ {" + EnvelopeRespostaConfiguracaoIntegracaoPluggyDTO.atributos + "} ], "
                    + "\"configuracaoIntegracaoKobana\": {" + EnvelopeRespostaConfiguracaoIntegracaoKobanaDTO.atributosSemEmpresa + "}";

    public final static String resposta = "{\"content\": {" + atributosSemEmpresa + "}}";
    public final static String requestBody = "{" + atributosSemEmpresa + "}";


}
