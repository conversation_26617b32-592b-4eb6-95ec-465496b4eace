package com.pacto.adm.core.dto.auladesmarcada;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(name = "Contador de Reposições", description = "Informações da reposição de aulas")
public class ContadorReposicoesDTO {

    @Schema(description = "Quantidade de reposições disponíveis", example = "3")
    private int reposicoesDisponiveis = 0;

    @Schema(description = "Quantidade de reposições utilizadas", example = "1")
    private int reposicoesUtilizadas = 0;

    @Schema(description = "Quantidade de reposições expiradas", example = "0")
    private int reposicoesExpiradas = 0;

    public int getReposicoesDisponiveis() {
        return reposicoesDisponiveis;
    }

    public void setReposicoesDisponiveis(int reposicoesDisponiveis) {
        this.reposicoesDisponiveis = reposicoesDisponiveis;
    }

    public int getReposicoesUtilizadas() {
        return reposicoesUtilizadas;
    }

    public void setReposicoesUtilizadas(int reposicoesUtilizadas) {
        this.reposicoesUtilizadas = reposicoesUtilizadas;
    }

    public int getReposicoesExpiradas() {
        return reposicoesExpiradas;
    }

    public void setReposicoesExpiradas(int reposicoesExpiradas) {
        this.reposicoesExpiradas = reposicoesExpiradas;
    }
}
