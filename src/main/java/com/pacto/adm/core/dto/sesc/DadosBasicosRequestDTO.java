package com.pacto.adm.core.dto.sesc;

import com.pacto.adm.core.dto.sesc.sescdf.ClienteSescDfDTO;
import com.pacto.adm.core.dto.sesc.sescgo.ClienteSescGoDTO;
import com.pacto.adm.core.dto.sesc.sescgo.EnderecoClienteSescGoDTO;
import com.pacto.config.utils.UteisValidacao;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class DadosBasicosRequestDTO {
    private Integer codigoPessoa;
    private String fotoBase64OrUrl;
    private String dataCadastro;
    private String nome;
    private String nomeRegistro;
    private String dataNasc;
    private Integer tipoPessoa;
    private String cpf;
    private String rg;
    private String inscricaoEstadual;
    private String inscricaoMunicipal;
    private String cnpj;
    private String cfdf;
    private boolean estrangeira;
    private String rne;
    private String nacionalidade;
    private String naturalidade;
    private String sexo;
    private String genero;
    private String estadoCivil;
    private String nomePai;
    private String emailPai;
    private String cpfPai;
    private String rgPai;
    private String nomeMae;
    private String emailMae;
    private String cpfMae;
    private String rgMae;
    private String passaporte;
    private String rgOrgao;
    private String rgUf;
    private String dataNascimentoResponsavel;
    private String contatoEmergencia;
    private String telefoneEmergencia;
    private String webPage;
    private String cpfResponsavelEmpresa;
    private String nomeResponsavelEmpresa;
    private String nomeRespFinanceiro;
    private String emailRespFinanceiro;
    private String cpfRespFinanceiro;
    private String rgRespFinanceiro;
    private String situacaoCliente;
    private List<EnderecoDTO> endereco;
    private List<EmailDTO> email;
    private List<TelefoneDTO> telefone;

    public DadosBasicosRequestDTO(ClienteSescDfDTO clienteSesc) {
        this.setCpf(clienteSesc.getCpf());
        this.setNome(clienteSesc.getNome());
        this.setNomeRegistro(clienteSesc.getNomesocial());
        this.setDataNasc(clienteSesc.getDatanascimento().toString());
        this.setNomePai(clienteSesc.getPai());
        this.setNomeMae(clienteSesc.getMae());
        this.setNacionalidade(clienteSesc.getNacionalidade());
        this.setNaturalidade(clienteSesc.getNaturalidade());
    }

    public DadosBasicosRequestDTO(ClienteSescGoDTO clienteSesc, EnderecoClienteSescGoDTO enderecoCliente) {
        this.setCpf(clienteSesc.getNuCpf());
        this.setNome(clienteSesc.getNmCliente());
        this.setDataNasc(clienteSesc.getDtNascimen().toString());
        this.setNomePai(clienteSesc.getNmPai());
        this.setNomeMae(clienteSesc.getNmMae());
        this.setNacionalidade(clienteSesc.getDsNacional());
        this.setNaturalidade(clienteSesc.getDsNatural());
        this.setFotoBase64OrUrl(clienteSesc.getFoto());

        if (!UteisValidacao.emptyString(clienteSesc.getEmail())) {
            this.email = new ArrayList<>();

            final EmailDTO emailDTO = new EmailDTO();
            emailDTO.setEmail(clienteSesc.getEmail());

            this.email.add(emailDTO);
        }

        if (!UteisValidacao.emptyString(clienteSesc.getDddTelefone()) && !UteisValidacao.emptyString(clienteSesc.getTelefone())) {
            this.telefone = new ArrayList<>();

            final TelefoneDTO telefoneDTO = new TelefoneDTO();
            telefoneDTO.setDdi(clienteSesc.getDddTelefone());
            telefoneDTO.setNumero(clienteSesc.getTelefone());
            telefoneDTO.setTipoTelefone("CE");
            telefoneDTO.setDescricao("Telefone principal");

            telefone.add(telefoneDTO);
        }

        if (enderecoCliente != null) {
            this.endereco = new ArrayList<>();

            final EnderecoDTO enderecoDTO = new EnderecoDTO();
            enderecoDTO.setEndereco(enderecoCliente.getLogradouro());
            enderecoDTO.setNumero(enderecoCliente.getNumeroImovel());
            enderecoDTO.setComplemento(enderecoCliente.getComplemento());
            enderecoDTO.setBairro(enderecoCliente.getBairro());
            enderecoDTO.setCep(enderecoCliente.getCep());
            enderecoDTO.setTipoEndereco("RE");

            this.endereco.add(enderecoDTO);
        }
    }
}
