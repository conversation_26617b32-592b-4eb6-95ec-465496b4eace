package com.pacto.adm.core.dto.enveloperesposta.aulas;

import com.pacto.adm.core.dto.contratomodalidade.ContratoModalidadeTurmaDTO;
import com.pacto.adm.core.dto.enveloperesposta.turma.EnvelopeRespostaTurmaDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "Representação das respostas")
public class EnvelopeRespostaContratoModalidadeTurmoDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private ContratoModalidadeTurmaDTO content;

    public ContratoModalidadeTurmaDTO getContent() {
        return content;
    }

    public void setContent(ContratoModalidadeTurmaDTO content) {
        this.content = content;
    }

    public static final String atributos =
            "\"codigo\": 30, "
                    + "\"turma\": {" + EnvelopeRespostaTurmaDTO.atributos + "}, "
                    + "\"horarios\": ["
                    + "{" + EnvelopeRespostaContratoModalidadeHorarioTurmaDTO.atributos + "}"
                    + "], "
                    + "\"totalAulasDesmarcadasContratoPassado\": 2, "
                    + "\"presencas\": 15, "
                    + "\"faltas\": 3, "
                    + "\"totalAulas\": 20, "
                    + "\"quantidadeCreditoCompra\": 10, "
                    + "\"quantidadeCreditoDisponivel\": 4, "
                    + "\"dataInicioMatricula\": \"2024-01-15T00:00:00Z\", "
                    + "\"dataFimMatricula\": \"2024-12-15T00:00:00Z\", "
                    + "\"totalAulasDesmarcadasSemReposicao\": 1, "
                    + "\"totalAulasHoje\": 2, "
                    + "\"totalReposicoesPresentes\": 3, "
                    + "\"reposicoes\": 5, "
                    + "\"desmarcadas\": 4";
    public final static String resposta = "{\"content\": {" + atributos + "}}";

}
