package com.pacto.adm.core.dto.filtros;

import com.pacto.adm.core.controller.json.base.SuperJSON;
import com.pacto.adm.core.enumerador.OperacaoLinhaDoTempoEnum;
import com.pacto.adm.core.enumerador.TipoLinhaDoTempoEnum;
import com.pacto.config.utils.UteisValidacao;
import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class FiltroLinhaDoTempoJSON extends SuperJSON {

    private Integer matricula;
    private Integer contrato;
    private Integer tipo;
    private Long dtInicio;
    private Long dtFim;
    private List<Integer> operacao;


    public FiltroLinhaDoTempoJSON(JSONObject filters) {
        if (filters != null) {
            this.matricula = filters.optInt("matricula");
            this.contrato = filters.optInt("contrato");
            this.tipo = filters.optInt("tipo");
            this.dtInicio = filters.get("dtInicio").equals(JSONObject.NULL) ? null : filters.optLong("dtInicio");
            this.dtFim = filters.get("dtFim").equals(JSONObject.NULL) ? null : filters.optLong("dtFim");
            try {
                this.operacao = new ArrayList<>();
                JSONArray array = filters.optJSONArray("operacao");
                if (array != null) {
                    for (int e = 0; e < array.length(); e++) {
                        this.operacao.add(array.getInt(e));
                    }
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }
            this.processarTipo();
        }
    }

    private void processarTipo() {
        if (!UteisValidacao.emptyNumber(this.getTipo())) {
            TipoLinhaDoTempoEnum tipoLinhaDoTempoEnum = TipoLinhaDoTempoEnum.getFromCodigo(this.getTipo());
            if (tipoLinhaDoTempoEnum != null) {
                this.operacao = new ArrayList<>();
                for (OperacaoLinhaDoTempoEnum operacao : OperacaoLinhaDoTempoEnum.values()) {
                    if (tipoLinhaDoTempoEnum.equals(TipoLinhaDoTempoEnum.TODOS) ||
                            operacao.getTipo().equals(tipoLinhaDoTempoEnum)) {
                        this.operacao.add(operacao.getCodigo());
                    }
                }
            }
        }
    }

    public Integer getMatricula() {
        return matricula;
    }

    public void setMatricula(Integer matricula) {
        this.matricula = matricula;
    }

    public Long getDtInicio() {
        return dtInicio;
    }

    public void setDtInicio(Long dtInicio) {
        this.dtInicio = dtInicio;
    }

    public Long getDtFim() {
        return dtFim;
    }

    public void setDtFim(Long dtFim) {
        this.dtFim = dtFim;
    }

    public Date getDtInicio_Date() {
        try {
            if (this.dtInicio != null) {
                return new Date(this.dtInicio);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return null;
    }

    public Date getDtFim_Date() {
        try {
            if (this.dtFim != null) {
                return new Date(this.dtFim);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return null;
    }

    public List<Integer> getOperacao() {
        return operacao;
    }

    public void setOperacao(List<Integer> operacao) {
        this.operacao = operacao;
    }

    public Integer getContrato() {
        return contrato;
    }

    public void setContrato(Integer contrato) {
        this.contrato = contrato;
    }

    public Integer getTipo() {
        return tipo;
    }

    public void setTipo(Integer tipo) {
        this.tipo = tipo;
    }
}
