package com.pacto.adm.core.dto.enveloperesposta.convenio;

import com.pacto.adm.core.dto.negociacao.ConvenioDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "Representação das respostas")
public class EnvelopeRespostaConvenioDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private ConvenioDTO content;

    public ConvenioDTO getContent() {
        return content;
    }

    public void setContent(ConvenioDTO content) {
        this.content = content;
    }


    public static final String atributos =
            "\"codigo\": 5, "
                    + "\"descricao\": \"Convênio Empresa X\"";

    public final static String resposta = "{\"content\": {" + atributos + "}}";
    public final static String requestBody = "{" + atributos + "}";


}
