package com.pacto.adm.core.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;

@Schema(name = "Histórico do Contrato", description = "Informações do histórico do contrato")
public class HistoricoContratoDTO {

    @Schema(description = "Código único identificador do histórico do contrato", example = "1234")
    private Integer codigo;
    @Schema(description = "Código único identificador do contrato", example = "120")
    private Integer contrato;

    @Schema(description = "Descrição do histórico", example = "MATRICULA")
    private String descricao;

    @Schema(description = "Tipo do histórico", example = "MA")
    private String tipoHistorico;

    @Schema(description = "Data de registro do histórico", example = "2025-05-07T00:00:00")
    private Date dataRegistro; //dataCadastro

    @Schema(description = "Situação relativa ao histórico", example = "MA")
    private String situacaoRelativaHistorico;     //tipoHistorico

    @Schema(description = "Data de início da situação", example = "2025-05-07T00:00:00")
    private Date dataInicioSituacao;

    @Schema(description = "Data final da situação", example = "2025-05-07T00:00:00")
    private Date dataFinalSituacao;

    @Schema(description = "Usuário responsável pelo registro do histórico")
    private UsuarioDTO responsavelRegistro;

    @Schema(description = "Responsável pela liberação da mudança do histórico")
    private UsuarioDTO responsavelLiberacaoMudancaHistorico;

    @Schema(description = "Indica se deve ter um retorno manual do histórico", example = "false")
    private Boolean retornoManual = false;

    @Schema(description = "Data de início", example = "2025-05-07T00:00:00")
    private Date dataInicioTemporal;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getContrato() {
        return contrato;
    }

    public void setContrato(Integer contrato) {
        this.contrato = contrato;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getTipoHistorico() {
        return tipoHistorico;
    }

    public void setTipoHistorico(String tipoHistorico) {
        this.tipoHistorico = tipoHistorico;
    }

    public Date getDataRegistro() {
        return dataRegistro;
    }

    public void setDataRegistro(Date dataRegistro) {
        this.dataRegistro = dataRegistro;
    }

    public String getSituacaoRelativaHistorico() {
        return situacaoRelativaHistorico;
    }

    public void setSituacaoRelativaHistorico(String situacaoRelativaHistorico) {
        this.situacaoRelativaHistorico = situacaoRelativaHistorico;
    }

    public Date getDataInicioSituacao() {
        return dataInicioSituacao;
    }

    public void setDataInicioSituacao(Date dataInicioSituacao) {
        this.dataInicioSituacao = dataInicioSituacao;
    }

    public Date getDataFinalSituacao() {
        return dataFinalSituacao;
    }

    public void setDataFinalSituacao(Date dataFinalSituacao) {
        this.dataFinalSituacao = dataFinalSituacao;
    }

    public UsuarioDTO getResponsavelRegistro() {
        return responsavelRegistro;
    }

    public void setResponsavelRegistro(UsuarioDTO responsavelRegistro) {
        this.responsavelRegistro = responsavelRegistro;
    }

    public UsuarioDTO getResponsavelLiberacaoMudancaHistorico() {
        return responsavelLiberacaoMudancaHistorico;
    }

    public void setResponsavelLiberacaoMudancaHistorico(UsuarioDTO responsavelLiberacaoMudancaHistorico) {
        this.responsavelLiberacaoMudancaHistorico = responsavelLiberacaoMudancaHistorico;
    }

    public Boolean getRetornoManual() {
        return retornoManual;
    }

    public void setRetornoManual(Boolean retornoManual) {
        this.retornoManual = retornoManual;
    }

    public Date getDataInicioTemporal() {
        return dataInicioTemporal;
    }

    public void setDataInicioTemporal(Date dataInicioTemporal) {
        this.dataInicioTemporal = dataInicioTemporal;
    }
}
