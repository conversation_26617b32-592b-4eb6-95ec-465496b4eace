package com.pacto.adm.core.dto.empresa;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.pacto.adm.core.dto.integracoes.ConfiguracaoEmpresaRDStationDTO;
import com.pacto.adm.core.dto.integracoes.EmpresaConfigEstacionamentoDTO;
import com.pacto.adm.core.dto.integracoes.ParceiroFidelidadeDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(name = "EmpresaLocalDeAcesso", description = "Informações sobre os locais de acesso da empresa, incluindo localização e status.")
public class EmpresaLocalDeAcessoDTO {

    @Schema(description = "Código único identificador do local de acesso.", example = "160")
    private Integer codigo;

    @Schema(description = "Nome do local de acesso.", example = "Unidade Centro")
    private String nome;

    @Schema(description = "Indica se o local de acesso está ativo.", example = "true")
    private boolean ativa;

    @Schema(description = "Setor ou departamento do local de acesso.", example = "Musculação")
    private String setor;

    @Schema(description = "Detalhes do estado onde o local de acesso está situado.")
    private EstadoDTO estado;

    @Schema(description = "Detalhes da cidade onde o local de acesso está situado.")
    private CidadeDTO cidade;

    public EmpresaLocalDeAcessoDTO() {
    }

    public EmpresaLocalDeAcessoDTO(Integer codigo, String nome, String setor, EstadoDTO estado, CidadeDTO cidade) {
        this.codigo = codigo;
        this.nome = nome;
        this.setor = setor;
        this.estado = estado;
        this.cidade = cidade;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getSetor() {
        return setor;
    }

    public void setSetor(String setor) {
        this.setor = setor;
    }

    public EstadoDTO getEstado() {
        return estado;
    }

    public void setEstado(EstadoDTO estado) {
        this.estado = estado;
    }

    public CidadeDTO getCidade() {
        return cidade;
    }

    public void setCidade(CidadeDTO cidade) {
        this.cidade = cidade;
    }


    public boolean isAtiva() {
        return ativa;
    }

    public void setAtiva(boolean ativa) {
        this.ativa = ativa;
    }


}
