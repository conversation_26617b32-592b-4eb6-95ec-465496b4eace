package com.pacto.adm.core.dto.enveloperesposta.indicacao;

import com.pacto.adm.core.dto.enveloperesposta.cliente.EnvelopeRespostaClienteDTO;
import com.pacto.adm.core.dto.enveloperesposta.cliente.questionarios.EnvelopeRespostaEventoDTO;
import com.pacto.adm.core.dto.enveloperesposta.colaborador.EnvelopeRespostaColaboradorDTO;
import com.pacto.adm.core.dto.enveloperesposta.usuario.EnvelopeRespostaUsuarioDTO;
import com.pacto.adm.core.dto.indicacao.IndicacaoDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

//@Schema(name = "Respostas Aluguel de Armários", description = "Representação das respostas contendo uma lista de aluguel de armários")
public class EnvelopeRespostaListIndicacaoDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private List<IndicacaoDTO> content;

    public List<IndicacaoDTO> getContent() {
        return content;
    }

    public void setContent(List<IndicacaoDTO> content) {
        this.content = content;
    }

    public final static String resposta = "{"
            + "  \"content\": [{" + EnvelopeRespostaIndicacaoDTO.atributos + " }],"
            + "  \"totalElements\": 1,"
            + "  \"totalPages\": 1,"
            + "  \"first\": true,"
            + "  \"last\": true,"
            + "  \"size\": 10,"
            + "  \"number\": 0"
            + "}";




}
