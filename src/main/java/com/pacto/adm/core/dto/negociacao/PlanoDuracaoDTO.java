package com.pacto.adm.core.dto.negociacao;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.ArrayList;
import java.util.List;

@Schema(name = "Plano Duração", description = "Informações sobre a duração do plano, incluindo condições e créditos.")
public class PlanoDuracaoDTO {

    @Schema(description = "Código identificador da duração do plano.", example = "1")
    private Integer codigo;

    @Schema(description = "Número de meses da duração do plano.", example = "12")
    private Integer nrMeses;

    @Schema(description = "Descrição da duração do plano.", example = "Plano anual")
    private String descricao;

    @Schema(description = "Lista de condições associadas à duração do plano.")
    private List<PlanoDuracaoCondicaoDTO> condicoes = new ArrayList<>();

    @Schema(description = "Lista de créditos associados à duração do plano.")
    private List<PlanoDuracaoCreditoDTO> creditos = new ArrayList<>();

    public List<PlanoDuracaoCondicaoDTO> getCondicoes() {
        return condicoes;
    }

    public void setCondicoes(List<PlanoDuracaoCondicaoDTO> condicoes) {
        this.condicoes = condicoes;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Integer getNrMeses() {
        return nrMeses;
    }

    public void setNrMeses(Integer nrMeses) {
        this.nrMeses = nrMeses;
    }

    public List<PlanoDuracaoCreditoDTO> getCreditos() {
        return creditos;
    }

    public void setCreditos(List<PlanoDuracaoCreditoDTO> creditos) {
        this.creditos = creditos;
    }
}
