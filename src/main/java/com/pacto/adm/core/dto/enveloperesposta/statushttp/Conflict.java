package com.pacto.adm.core.dto.enveloperesposta.statushttp;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "Resposta para tentativas de inserção de registros já existentes (HTTP 409)")
public class Conflict {

    @Schema(description = "Código do erro", example = "registro_duplicado")
    private String error;

    @Schema(description = "Mensagem indicando que o registro já existe", example = "Este registro já está cadastrado no sistema.")
    private String message;

    public String getError() {
        return error;
    }

    public void setError(String error) {
        this.error = error;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
