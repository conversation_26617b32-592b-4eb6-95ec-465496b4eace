package com.pacto.adm.core.dto.enveloperesposta.integracoes.spivi;

import com.pacto.adm.core.dto.enveloperesposta.empresa.EnvelopeRespostaEmpresaDTO;
import com.pacto.adm.core.dto.integracoes.ConfiguracaoIntegracaoSpiviDTO;
import io.swagger.v3.oas.annotations.media.Schema;

public class EnvelopeRespostaConfiguracaoIntegracaoSpiviDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private ConfiguracaoIntegracaoSpiviDTO content;

    public ConfiguracaoIntegracaoSpiviDTO getContent() {
        return content;
    }

    public void setContent(ConfiguracaoIntegracaoSpiviDTO content) {
        this.content = content;
    }

    public static final String atributos =
            "\"habilitada\": true, "
                    + "\"sourceName\": \"spivi-integration-source\", "
                    + "\"siteId\": 12345, "
                    + "\"spiviPassword\": \"spivi-password-xyz\", "
                    + "\"empresa\": {" + EnvelopeRespostaEmpresaDTO.atributos + "}";
    public static final String atributosSemEmpresa =
            "\"habilitada\": true, "
                    + "\"sourceName\": \"spivi-integration-source\", "
                    + "\"siteId\": 12345, "
                    + "\"spiviPassword\": \"spivi-password-xyz\", "
                    + "\"empresa\": \"INFORMAÇÕES DA EMPRESA\"";

    public final static String resposta = "{\"content\": {" + atributos + "}}";
    public final static String requestBody = "{" + atributos + "}";


}
