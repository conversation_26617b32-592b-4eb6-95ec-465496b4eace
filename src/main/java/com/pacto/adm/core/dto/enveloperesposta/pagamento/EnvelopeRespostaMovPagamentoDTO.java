package com.pacto.adm.core.dto.enveloperesposta.pagamento;

import com.pacto.adm.core.dto.MovPagamentoDTO;
import com.pacto.adm.core.dto.enveloperesposta.empresa.EnvelopeRespostaEmpresaDTO;
import com.pacto.adm.core.dto.enveloperesposta.usuario.EnvelopeRespostaUsuarioDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "Representação das respostas")
public class EnvelopeRespostaMovPagamentoDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private MovPagamentoDTO content;

    public MovPagamentoDTO getContent() {
        return content;
    }

    public void setContent(MovPagamentoDTO content) {
        this.content = content;
    }

    public static final String atributos =
            "\"codigo\": 1, "
                    + "\"reciboPagamento\": 10, "
                    + "\"empresa\": {" + EnvelopeRespostaEmpresaDTO.atributos + "}, "
                    + "\"nomePessoaPagador\": \"<PERSON>\", "
                    + "\"dataLancamento\": \"2023-10-15\", "
                    + "\"valorTotal\": 299.99, "
                    + "\"descricaoFormaPagamento\": \"Cartão de Crédito\", "
                    + "\"usuario\": {" + EnvelopeRespostaUsuarioDTO.atributos + "}, "
                    + "\"formaPagamento\": {" + EnvelopeRespostaFormaPagamentoDTO.atributos + "}, "
                    + "\"statusConciliadora\": 1, "
                    + "\"credito\": true, "
                    + "\"valor\": 299.99";

    public final static String resposta = "{\"content\": {" + atributos + "}}";
    public final static String requestBody = "{" + atributos + "}";


}
