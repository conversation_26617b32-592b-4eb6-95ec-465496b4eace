package com.pacto.adm.core.dto.negociacao;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

@Schema(name = "Resultado Venda V2", description = "Retorno da venda com informações adicionais de parcelas.")
public class ResultadoVendaV2DTO {

    @Schema(description = "Resultado da venda com link e contato.")
    private ResultadoVendaDTO resultadoVendaDTO;

    @Schema(description = "Lista de parcelas relacionadas à venda.")
    private List<ParcelasDTO> parcelasDTO;

    public ResultadoVendaDTO getResultadoVendaDTO() {
        return resultadoVendaDTO;
    }

    public void setResultadoVendaDTO(ResultadoVendaDTO resultadoVendaDTO) {
        this.resultadoVendaDTO = resultadoVendaDTO;
    }

    public List<ParcelasDTO> getParcelasDTO() {
        return parcelasDTO;
    }

    public void setParcelasDTO(List<ParcelasDTO> parcelasDTO) {
        this.parcelasDTO = parcelasDTO;
    }
}
