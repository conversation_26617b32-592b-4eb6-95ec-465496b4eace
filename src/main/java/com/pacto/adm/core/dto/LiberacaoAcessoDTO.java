package com.pacto.adm.core.dto;

import com.pacto.adm.core.dto.empresa.EmpresaDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor

@Schema(description = "Informações da Liberação de Acesso", name = "Liberação de Acesso")
public class LiberacaoAcessoDTO {

    @Schema(description = "Código único identificador da Liberação de Acesso", example = "112")
    private Integer codigo;

    @Schema(description = "Pessoa vinculada a liberação de acesso")
    private PessoaDTO pessoa;

    @Schema(description = "Tipo da liberação", example = "ENTRADA")
    private Integer tipoLiberacao;

    @Schema(description = "Sentido da liberação", example = "E")
    private String sentido;

    @Schema(description = "Local que foi realizado a liberação do acesso")
    private LocalDeAcessoDTO localAcesso;

    @Schema(description = "Coletor que realizou a liberação do acesso")
    private ColetorDTO coletor;

    @Schema(description = "Usuário vinculado a liberação do acesso acesso")
    private UsuarioDTO usuario;

    @Schema(description = "Empresa que realizou a liberação do acesso")
    private EmpresaDTO empresa;

    @Schema(description = "Data e hora que foi realizado o acesso", example = "2025-04-08T00:00:00Z")
    private Date dataHora;

    @Schema(description = "Justificativa da liberação do acesso", example = "Entrada")
    private String justificativa;

    @Schema(description = "Data e hora da justificativa da liberação do acesso", example = "2025-04-08T00:00:00Z")
    private Date dataHoraJustificativa;

    @Schema(description = "Usuário que fez a justificativa da liberação de acesso")
    private UsuarioDTO usuarioJustificou;

    @Schema(description = "Nome genérico da liberação", example = "ENTRADA DE PESSOA")
    private String nomeGenerico;

}
