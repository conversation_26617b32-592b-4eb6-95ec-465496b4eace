package com.pacto.adm.core.dto.enveloperesposta.contrato;

import com.pacto.adm.core.dto.JustificativaOperacaoDTO;
import com.pacto.adm.core.dto.enveloperesposta.empresa.EnvelopeRespostaEmpresaDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "Representação das respostas")
public class EnvelopeRespostaJustificativaOperacaoDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private JustificativaOperacaoDTO content;

    public JustificativaOperacaoDTO getContent() {
        return content;
    }

    public void setContent(JustificativaOperacaoDTO content) {
        this.content = content;
    }


    public static final String atributos =
                    "\"codigo\": 5867, "
                    + "\"tipoOperacao\": \"MENSAL\", "
                    + "\"descricao\": \"Adição de atestado médico ao contrato\", "
                    + "\"empresa\": {" + EnvelopeRespostaEmpresaDTO.atributos + "}, "
                    + "\"isentarMultaCancelamento\": false, "
                    + "\"naoCobrarParcelasAtrasadasCancelamento\": false, "
                    + "\"necessarioAnexarComprovante\": true, "
                    + "\"apresentarTodasEmpresas\": true, "
                    + "\"ativa\":true";


    public final static String resposta = "{ \"content\": {"+ atributos + "}}";
}
