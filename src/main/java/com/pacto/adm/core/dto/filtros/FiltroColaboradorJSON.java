package com.pacto.adm.core.dto.filtros;

import org.json.JSONObject;

public class FiltroColaboradorJSON {

    private String parametro;
    private Integer empresa;

    public FiltroColaboradorJSON() {
    }

    public FiltroColaboradorJSON(JSONObject filters) {
        if (filters != null) {
            this.parametro = filters.optString("quickSearchValue").toUpperCase();
            if (filters.has("empresa")) {
                this.empresa = filters.getInt("empresa");
            }
        }
    }

    public String getParametro() {
        return parametro;
    }

    public void setParametro(String parametro) {
        this.parametro = parametro;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }
}
