package com.pacto.adm.core.dto;

public class PagamentoMovParcelaDTO {

    private double valorpago;
    private ReciboPagamentoDTO recibo;
    private MovParcelaDTO parcela;
    private MovPagamentoDTO pagamento;


    public double getValorpago() {
        return valorpago;
    }

    public void setValorpago(double valorpago) {
        this.valorpago = valorpago;
    }

    public ReciboPagamentoDTO getRecibo() {
        return recibo;
    }

    public void setRecibo(ReciboPagamentoDTO recibo) {
        this.recibo = recibo;
    }

    public MovParcelaDTO getParcela() {
        return parcela;
    }

    public void setParcela(MovParcelaDTO parcela) {
        this.parcela = parcela;
    }

    public MovPagamentoDTO getPagamento() {
        return pagamento;
    }

    public void setPagamento(MovPagamentoDTO pagamento) {
        this.pagamento = pagamento;
    }
}
