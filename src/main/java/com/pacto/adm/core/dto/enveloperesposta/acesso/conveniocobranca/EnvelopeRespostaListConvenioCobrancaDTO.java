package com.pacto.adm.core.dto.enveloperesposta.acesso.conveniocobranca;

import com.pacto.adm.core.dto.conveniocobranca.ConvenioCobrancaDTO;
import com.pacto.adm.core.dto.enveloperesposta.acesso.EnvelopeRespostaAcessoClienteDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

@Schema(description = "Representação das respostas")
public class EnvelopeRespostaListConvenioCobrancaDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private List<ConvenioCobrancaDTO> content;

    public List<ConvenioCobrancaDTO> getContent() {
        return content;
    }

    public void setContent(List<ConvenioCobrancaDTO> content) {
        this.content = content;
    }

    public final static String resposta = "{"
            + "  \"content\": [{" + EnvelopeRespostaAcessoClienteDTO.atributos + " }],"
            + "  \"totalElements\": 1,"
            + "  \"totalPages\": 1,"
            + "  \"first\": true,"
            + "  \"last\": true,"
            + "  \"size\": 10,"
            + "  \"number\": 0"
            + "}";
}
