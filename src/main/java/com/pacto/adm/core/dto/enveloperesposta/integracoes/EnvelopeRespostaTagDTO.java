package com.pacto.adm.core.dto.enveloperesposta.integracoes;

import com.pacto.adm.core.dto.integracaomanychat.TagDTO;
import io.swagger.v3.oas.annotations.media.Schema;

public class EnvelopeRespostaTagDTO {

    @Schema(description = "Conteú<PERSON> da resposta contendo as informações encontradas")
    private TagDTO content;

    public TagDTO getContent() {
        return content;
    }

    public void setContent(TagDTO content) {
        this.content = content;
    }

    public static final String atributos =
            "\"id\": 101, "
                    + "\"name\": \"Campanha Verão 2025\"";

    public static final String resposta = "{\"content\": {" + atributos + "}}";
    public static final String requestBody = "{" + atributos + "}";




}
