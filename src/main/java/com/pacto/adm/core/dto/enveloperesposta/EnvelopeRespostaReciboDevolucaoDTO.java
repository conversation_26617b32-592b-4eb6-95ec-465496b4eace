package com.pacto.adm.core.dto.enveloperesposta;

import com.pacto.adm.core.dto.enveloperesposta.contrato.EnvelopeRespostaContratoDTO;
import com.pacto.adm.core.dto.enveloperesposta.pessoa.EnvelopeRespostaPessoaDTO;
import com.pacto.adm.core.dto.enveloperesposta.usuario.EnvelopeRespostaUsuarioDTO;
import com.pacto.adm.core.dto.recibodevolucao.ReciboDevolucaoDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

@Schema(description = "Representação das respostas")
public class EnvelopeRespostaReciboDevolucaoDTO extends EnvelopeRespostaPaginacaoDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private List<ReciboDevolucaoDTO> content;

    public List<ReciboDevolucaoDTO> getContent() {
        return content;
    }

    public void setContent(List<ReciboDevolucaoDTO> content) {
        this.content = content;
    }

    public static final String atributos =
            "\"codigo\": 1, "
                    + "\"dataDevolucao\": 1698765432000, "
                    + "\"devolucaoManual\": true, "
                    + "\"liberacao\": false, "
                    + "\"liberacaoDevolucao\": true, "
                    + "\"quitacao\": false, "
                    + "\"quitacaoManual\": true, "
                    + "\"valorBaseContrato\": 500.00, "
                    + "\"valorContaCorrente\": 300.00, "
                    + "\"valorContrato\": 1000.00, "
                    + "\"valorDevolucao\": 200.00, "
                    + "\"valorDevolvidoEmDinheiro\": 150.00, "
                    + "\"valorMultaCancelamento\": 50.00, "
                    + "\"valorOriginal\": 1000.00, "
                    + "\"valorRealDevolucao\": 250.00, "
                    + "\"valorTaxaCancelamento\": 30.00, "
                    + "\"valorTotalPagoPeloCliente\": 800.00, "
                    + "\"valorTotalSomaProdutoContratos\": 1200.00, "
                    + "\"valorUtilizadoPeloCliente\": 600.00, "
                    + "\"contrato\": {" + EnvelopeRespostaContratoDTO.atributos + "}, "
                    + "\"movProduto\": \"INFORMAÇÕES DO PRODUTO\", "
                    + "\"pessoa\": {" + EnvelopeRespostaPessoaDTO.atributos + "}, "
                    + "\"prodDevolucao\": \"INFORMAÇÕES DO PRODUTO\", "
                    + "\"prodRecebiveis\": \"INFORMAÇÕES DO PRODUTO\", "
                    + "\"reciboEditado\": \"INFORMAÇÕES DO PRODUTO\", "
                    + "\"usuario\": {" + EnvelopeRespostaUsuarioDTO.atributos + "}";
}
