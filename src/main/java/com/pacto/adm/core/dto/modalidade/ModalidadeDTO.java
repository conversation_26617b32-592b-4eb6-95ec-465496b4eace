package com.pacto.adm.core.dto.modalidade;

import com.pacto.adm.core.dto.ArquivoDTO;
import com.pacto.adm.core.dto.TipoModalidadeDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

@Schema(name = "Modalidade", description = "Informações detalhadas sobre a modalidade, incluindo valores, status e tipo.")
public class ModalidadeDTO {

    @Schema(description = "Código único identificador da modalidade.", example = "90")
    private Integer codigo;

    @Schema(description = "Nome da modalidade.", example = "Musculação")
    private String nome;

    @Schema(description = "Número de vezes por semana que a modalidade é praticada.", example = "3")
    private Integer nrVezes;

    @Schema(description = "Detalhes do tipo de modalidade, como individual ou em grupo.", example = "1", implementation = TipoModalidadeDTO.class)
    private TipoModalidadeDTO tipoModalidade;

    @Schema(description = "Valor mensal cobrado pela modalidade.", example = "150.00")
    private Double valorMensal;

    @Schema(description = "Indica se a modalidade está ativa.", example = "true")
    private Boolean ativo;

    @Schema(description = "Indica se a modalidade utiliza turma.", example = "true")
    private Boolean utilizarturma;

    @Schema(description = "Chave da foto associada à modalidade.", example = "modalidade_001.jpg")
    private String fotoKey;

    @Schema(description = "URL completa da foto associada à modalidade.", example = "https://exemplo.com/imagens/modalidade_001.jpg")
    private String fotoKeyUrlFull;

    @Schema(description = "Arquivo associado à modalidade, contendo detalhes como nome e tipo.")
    private ArquivoDTO arquivo;

    @Schema(description = "Lista de empresas associadas à modalidade.")
    private List<ModalidadeEmpresaDTO> empresasModalidade;

    @Schema(description = "Lista de produtos sugeridos para a modalidade.")
    private List<ProdutoSugeridoDTO> produtosSugeridos;

    @Schema(description = "Indica se há contratos vendidos para a modalidade.", example = "true")
    private boolean temContratoVendido;

    @Schema(description = "Indica se a modalidade é de CrossFit.", example = "false")
    private Boolean crossfit;


    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Integer getNrVezes() {
        return nrVezes;
    }

    public void setNrVezes(Integer nrVezes) {
        this.nrVezes = nrVezes;
    }

    public TipoModalidadeDTO getTipoModalidade() {
        return tipoModalidade;
    }

    public void setTipoModalidade(TipoModalidadeDTO tipoModalidade) {
        this.tipoModalidade = tipoModalidade;
    }

    public Double getValorMensal() {
        return valorMensal;
    }

    public void setValorMensal(Double valorMensal) {
        this.valorMensal = valorMensal;
    }

    public Boolean getAtivo() {
        return ativo;
    }

    public void setAtivo(Boolean ativo) {
        this.ativo = ativo;
    }

    public Boolean getUtilizarturma() {
        return utilizarturma;
    }

    public void setUtilizarturma(Boolean utilizarturma) {
        this.utilizarturma = utilizarturma;
    }

    public String getFotoKey() {
        return fotoKey;
    }

    public void setFotoKey(String fotoKey) {
        this.fotoKey = fotoKey;
    }

    public ArquivoDTO getArquivo() {
        return arquivo;
    }

    public void setArquivo(ArquivoDTO arquivo) {
        this.arquivo = arquivo;
    }

    public List<ModalidadeEmpresaDTO> getEmpresasModalidade() {
        return empresasModalidade;
    }

    public void setEmpresasModalidade(List<ModalidadeEmpresaDTO> empresasModalidade) {
        this.empresasModalidade = empresasModalidade;
    }

    public List<ProdutoSugeridoDTO> getProdutosSugeridos() {
        return produtosSugeridos;
    }

    public void setProdutosSugeridos(List<ProdutoSugeridoDTO> produtosSugeridos) {
        this.produtosSugeridos = produtosSugeridos;
    }

    public String getFotoKeyUrlFull() {
        return fotoKeyUrlFull;
    }

    public void setFotoKeyUrlFull(String fotoKeyUrlFull) {
        this.fotoKeyUrlFull = fotoKeyUrlFull;
    }

    public boolean isTemContratoVendido() {
        return temContratoVendido;
    }

    public void setTemContratoVendido(boolean temContratoVendido) {
        this.temContratoVendido = temContratoVendido;
    }

    public Boolean getCrossfit() {
        return crossfit;
    }

    public void setCrossfit(Boolean crossfit) {
        this.crossfit = crossfit;
    }
}

