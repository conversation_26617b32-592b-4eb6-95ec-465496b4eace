package com.pacto.adm.core.dto.observacaooperacao;

import com.pacto.adm.core.dto.MovParcelaDTO;
import com.pacto.adm.core.dto.PessoaDTO;

import java.util.Date;

public class ObservacaoOperacaoDTO {
    private Integer codigo;
    private String justificativa;
    private Date dataOperacao;
    private String tipoObservacao;
    private MovParcelaDTO movParcela;
    private Double valorOperacao;
    private String usuarioResponsavel;
    private String tipoCancelamento;
    private PessoaDTO pessoa;
    private Integer codigoCliente;
    private String matriculaCliente;
    private Integer codigoMatriculaCliente;

    public ObservacaoOperacaoDTO(
            Integer codigo, String justificativa, Date dataOperacao, String tipoObservacao, MovParcelaDTO movParcela,
            Double valorOperacao, String usuarioResponsavel, String tipoCancelamento, PessoaDTO pessoa,
            Integer codigoCliente, String matriculaCliente, Integer codigoMatriculaCliente
    ) {
        this.codigo = codigo;
        this.justificativa = justificativa;
        this.dataOperacao = dataOperacao;
        this.tipoObservacao = tipoObservacao;
        this.movParcela = movParcela;
        this.valorOperacao = valorOperacao;
        this.usuarioResponsavel = usuarioResponsavel;
        this.tipoCancelamento = tipoCancelamento;
        this.pessoa = pessoa;
        this.codigoCliente = codigoCliente;
        this.matriculaCliente = matriculaCliente;
        this.codigoMatriculaCliente = codigoMatriculaCliente;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getJustificativa() {
        return justificativa;
    }

    public void setJustificativa(String justificativa) {
        this.justificativa = justificativa;
    }

    public Date getDataOperacao() {
        return dataOperacao;
    }

    public void setDataOperacao(Date dataOperacao) {
        this.dataOperacao = dataOperacao;
    }

    public String getTipoObservacao() {
        return tipoObservacao;
    }

    public void setTipoObservacao(String tipoObservacao) {
        this.tipoObservacao = tipoObservacao;
    }

    public MovParcelaDTO getMovParcela() {
        return movParcela;
    }

    public void setMovParcela(MovParcelaDTO movParcela) {
        this.movParcela = movParcela;
    }

    public Double getValorOperacao() {
        return valorOperacao;
    }

    public void setValorOperacao(Double valorOperacao) {
        this.valorOperacao = valorOperacao;
    }

    public String getUsuarioResponsavel() {
        return usuarioResponsavel;
    }

    public void setUsuarioResponsavel(String usuarioResponsavel) {
        this.usuarioResponsavel = usuarioResponsavel;
    }

    public String getTipoCancelamento() {
        return tipoCancelamento;
    }

    public void setTipoCancelamento(String tipoCancelamento) {
        this.tipoCancelamento = tipoCancelamento;
    }

    public PessoaDTO getPessoa() {
        return pessoa;
    }

    public void setPessoa(PessoaDTO pessoa) {
        this.pessoa = pessoa;
    }

    public Integer getCodigoCliente() {
        return codigoCliente;
    }

    public void setCodigoCliente(Integer codigoCliente) {
        this.codigoCliente = codigoCliente;
    }

    public String getMatriculaCliente() {
        return matriculaCliente;
    }

    public void setMatriculaCliente(String matriculaCliente) {
        this.matriculaCliente = matriculaCliente;
    }

    public Integer getCodigoMatriculaCliente() {
        return codigoMatriculaCliente;
    }

    public void setCodigoMatriculaCliente(Integer codigoMatriculaCliente) {
        this.codigoMatriculaCliente = codigoMatriculaCliente;
    }
}
