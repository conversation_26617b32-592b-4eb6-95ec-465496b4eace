package com.pacto.adm.core.dto.controlecreditotreino;

import com.pacto.adm.core.dto.AcessoClienteDTO;
import com.pacto.adm.core.dto.ContratoDTO;
import com.pacto.adm.core.dto.HorarioTurmaDTO;
import com.pacto.adm.core.dto.modalidade.ModalidadeDTO;
import com.pacto.adm.core.dto.UsuarioDTO;
import com.pacto.adm.core.dto.auladesmarcada.AulaDesmarcadaDTO;
import com.pacto.adm.core.dto.reposicao.ReposicaoDTO;
import com.pacto.adm.core.enumerador.TipoOperacaoCreditoTreinoEnum;

import java.util.Date;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;

@Schema(name = "Controle Credito Treino", description = "Controle detalhado dos créditos de treino, incluindo operações, modalidades e ajustes.")
public class ControleCreditoTreinoDTO {

    @Schema(description = "Código único identificador do controle de crédito.", example = "210")
    private Integer codigo;

    @Schema(description = "Quantidade de créditos envolvidos na operação.", example = "5")
    private Integer quantidade;

    @Schema(description = "Data de lançamento do crédito.", example = "2023-07-01T00:00:00Z")
    private Date dataLancamento;

    @Schema(description = "Data da operação do crédito.", example = "2023-07-02T00:00:00Z")
    private Date dataOperacao;

    @Schema(description = "Observações adicionais sobre a operação.", example = "Crédito adicionado por reposição.")
    private String observacao;

    @Schema(description = "Tipo da operação realizada com o crédito de treino." +
            "Valores disponíveis:" +
            "1 - COMPRA" +
            "2 - UTILIZACAO" +
            "3 - NAO_COMPARECIMENTO:" +
            "4 - MARCOU_AULA" +
            "5 - DESMARCOU_AULA" +
            "6 - REPOSICAO" +
            "7 - AJUSTE_MANUAL" +
            "8 - TRANSFERENCIA_SALDO" +
            "9 - MANUTENCAO_MODALIDADE" +
            "10 - CANCELAMENTO_CONTRATO" +
            "11 - AJUSTE_MENSAL" +
            "12 - TRANSFERENCIA_ENTRE_ALUNOS_ORIGEM" +
            "13 - TRANSFERENCIA_ENTRE_ALUNOS_DESTINO")
    private TipoOperacaoCreditoTreinoEnum tipoOperacaoCreditoTreino;

    @Schema(description = "Detalhes da aula desmarcada relacionada à operação.")
    private AulaDesmarcadaDTO aulaDesmarcada;

    @Schema(description = "Detalhes do horário da turma relacionado à falta.")
    private HorarioTurmaDTO horarioTurmaFalta;

    @Schema(description = "Detalhes do contrato de origem relacionado ao crédito.")
    private ContratoDTO contratoOrigem;

    @Schema(description = "Detalhes da modalidade relacionada ao crédito.")
    private ModalidadeDTO modalidade;

    @Schema(description = "Detalhes da reposição associada ao crédito.")
    private ReposicaoDTO reposicao;

    @Schema(description = "Detalhes do acesso do cliente relacionado à operação.")
    private AcessoClienteDTO acessoCliente;

    @Schema(description = "Informações do usuário que realizou a operação.")
    private UsuarioDTO usuario;

    @Schema(description = "Detalhes do contrato associado ao crédito de treino.")
    private ContratoDTO contrato;

    @Schema(description = "Código do tipo de ajuste manual aplicado ao crédito de treino.", example = "2")
    private Integer codigoTipoAjusteManualCreditoTreino;

    @Schema(description = "Saldo atual de créditos após a operação.", example = "10")
    private Integer saldo;

    @Schema(description = "Descrição da aula marcada relacionada à operação.", example = "Aula de Pilates - Quarta 18h")
    private String descricaoAulaMarcada;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getQuantidade() {
        return quantidade;
    }

    public void setQuantidade(Integer quantidade) {
        this.quantidade = quantidade;
    }

    public Date getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(Date dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public Date getDataOperacao() {
        return dataOperacao;
    }

    public void setDataOperacao(Date dataOperacao) {
        this.dataOperacao = dataOperacao;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public TipoOperacaoCreditoTreinoEnum getTipoOperacaoCreditoTreino() {
        return tipoOperacaoCreditoTreino;
    }

    public void setTipoOperacaoCreditoTreino(TipoOperacaoCreditoTreinoEnum tipoOperacaoCreditoTreino) {
        this.tipoOperacaoCreditoTreino = tipoOperacaoCreditoTreino;
    }

    public AulaDesmarcadaDTO getAulaDesmarcada() {
        return aulaDesmarcada;
    }

    public void setAulaDesmarcada(AulaDesmarcadaDTO aulaDesmarcada) {
        this.aulaDesmarcada = aulaDesmarcada;
    }

    public HorarioTurmaDTO getHorarioTurmaFalta() {
        return horarioTurmaFalta;
    }

    public void setHorarioTurmaFalta(HorarioTurmaDTO horarioTurmaFalta) {
        this.horarioTurmaFalta = horarioTurmaFalta;
    }

    public ContratoDTO getContratoOrigem() {
        return contratoOrigem;
    }

    public void setContratoOrigem(ContratoDTO contratoOrigem) {
        this.contratoOrigem = contratoOrigem;
    }

    public ModalidadeDTO getModalidade() {
        return modalidade;
    }

    public void setModalidade(ModalidadeDTO modalidade) {
        this.modalidade = modalidade;
    }

    public ReposicaoDTO getReposicao() {
        return reposicao;
    }

    public void setReposicao(ReposicaoDTO reposicao) {
        this.reposicao = reposicao;
    }

    public AcessoClienteDTO getAcessoCliente() {
        return acessoCliente;
    }

    public void setAcessoCliente(AcessoClienteDTO acessoCliente) {
        this.acessoCliente = acessoCliente;
    }

    public UsuarioDTO getUsuario() {
        return usuario;
    }

    public void setUsuario(UsuarioDTO usuario) {
        this.usuario = usuario;
    }

    public ContratoDTO getContrato() {
        return contrato;
    }

    public void setContrato(ContratoDTO contrato) {
        this.contrato = contrato;
    }

    public Integer getCodigoTipoAjusteManualCreditoTreino() {
        return codigoTipoAjusteManualCreditoTreino;
    }

    public void setCodigoTipoAjusteManualCreditoTreino(Integer codigoTipoAjusteManualCreditoTreino) {
        this.codigoTipoAjusteManualCreditoTreino = codigoTipoAjusteManualCreditoTreino;
    }

    public Integer getSaldo() {
        return saldo;
    }

    public void setSaldo(Integer saldo) {
        this.saldo = saldo;
    }

    public String getDescricaoAulaMarcada() {
        return descricaoAulaMarcada;
    }

    public void setDescricaoAulaMarcada(String descricaoAulaMarcada) {
        this.descricaoAulaMarcada = descricaoAulaMarcada;
    }
}
