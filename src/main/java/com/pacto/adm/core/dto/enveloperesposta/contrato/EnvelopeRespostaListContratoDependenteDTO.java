package com.pacto.adm.core.dto.enveloperesposta.contrato;

import com.pacto.adm.core.dto.ContratoDependenteDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

@Schema(description = "Representação das respostas")
public class EnvelopeRespostaListContratoDependenteDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private List<ContratoDependenteDTO> content;

    public List<ContratoDependenteDTO> getContent() {
        return content;
    }

    public void setContent(List<ContratoDependenteDTO> content) {
        this.content = content;
    }

    public final static String resposta = "{"
            + "  \"content\": [{" + EnvelopeRespostaContratoDependenteDTO.atributos + " }],"
            + "  \"totalElements\": 1,"
            + "  \"totalPages\": 1,"
            + "  \"first\": true,"
            + "  \"last\": true,"
            + "  \"size\": 10,"
            + "  \"number\": 0"
            + "}";
}
