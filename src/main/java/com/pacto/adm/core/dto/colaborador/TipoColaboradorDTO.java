package com.pacto.adm.core.dto.colaborador;

import io.swagger.v3.oas.annotations.media.Schema;

public class TipoColaboradorDTO {
    @Schema(description = "Código único identificador do tipo de colaborador", example = "1")
    private Integer codigo;

    @Schema(description = "Descrição detalhada do tipo de colaborador", example = "Gerente de Vendas")
    private String descricao;

    public TipoColaboradorDTO() {
    }

    public TipoColaboradorDTO(Integer codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
}
