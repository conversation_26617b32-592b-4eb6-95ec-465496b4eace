package com.pacto.adm.core.dto.enveloperesposta.usuario;

import com.pacto.adm.core.dto.UsuarioDTO;
import com.pacto.adm.core.dto.enveloperesposta.EnvelopeRespostaPaginacaoDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

//@Schema(name = "Resposta Lista de Usuários", description = "Representação das respostas envolvendo lista de usuários")
public class EnvelopeRespostaListUsuarioDTO extends EnvelopeRespostaPaginacaoDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private List<UsuarioDTO> content;

    public List<UsuarioDTO> getContent() {
        return content;
    }

    public void setContent(List<UsuarioDTO> content) {
        this.content = content;
    }

    public final static String resposta = "{"
            + "  \"content\": [{" + EnvelopeRespostaUsuarioDTO.atributos + " }],"
            + "  \"totalElements\": 1,"
            + "  \"totalPages\": 1,"
            + "  \"first\": true,"
            + "  \"last\": true,"
            + "  \"size\": 10,"
            + "  \"number\": 0"
            + "}";
}
