package com.pacto.adm.core.dto.negociacao;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;

@Data
@Schema(name = "Cliente Negociação", description = "Informações do cliente envolvido em uma negociação.")
public class ClienteNegociacaoDTO {

    @Schema(description = "Código único identificador do cliente negociação.", example = "101")
    private Integer codigo;

    @Schema(description = "Código da empresa que está ocorrendo a negociação.", example = "3")
    private Integer empresa;

    @Schema(description = "Código do cliente na tabela pessoa.", example = "456")
    private Integer pessoa;

    @Schema(description = "Idade do cliente em meses.", example = "240")
    private Integer idadeEmMeses = 0;

    @Schema(description = "Número da matrícula do cliente.", example = "4312")
    private String matricula;

    @Schema(description = "Nome completo do cliente.", example = "Ana Beatriz Costa")
    private String nome;

    @Schema(description = "CPF da pessoa", example = "123.456.789-10")
    private String cpf;

    @Schema(description = "URL da foto do cliente.", example = "https://exemplo.com/fotos/cliente123.jpg")
    private String urlFoto;

    @Schema(description = "Categoria atual do cliente.", example = "ACADEMIA")
    private String categoria;

    @Schema(description = "Código identificador da categoria do cliente.", example = "5")
    private Integer codigoCategoria;

    @Schema(description = "Data de início da negociação.", example = "2022-01-01")
    private String inicio;

    @Schema(description = "Data de término da negociação.", example = "2022-12-31")
    private String fim;

    @Schema(description = "Situação atual do cliente na negociação.", example = "ATIVO")
    private String situacao;

    @Schema(description = "Indica se o cliente possui um identificador Vindi.", example = "true")
    private Boolean possuiIdVindi;

    @Schema(description = "Identificador do cliente na plataforma Vindi.", example = "789456")
    private Integer idVindi;

    public Integer getIdadeEmMeses() {
        return idadeEmMeses;
    }

    public void setIdadeEmMeses(Integer idadeEmMeses) {
        this.idadeEmMeses = idadeEmMeses;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getMatricula() {
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getUrlFoto() {
        return urlFoto;
    }

    public void setUrlFoto(String urlFoto) {
        this.urlFoto = urlFoto;
    }

    public String getCategoria() {
        return categoria;
    }

    public void setCategoria(String categoria) {
        this.categoria = categoria;
    }

    public String getInicio() {
        return inicio;
    }

    public void setInicio(String inicio) {
        this.inicio = inicio;
    }

    public String getFim() {
        return fim;
    }

    public void setFim(String fim) {
        this.fim = fim;
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public Integer getPessoa() {
        return pessoa;
    }

    public void setPessoa(Integer pessoa) {
        this.pessoa = pessoa;
    }

    public Integer getCodigoCategoria() {
        return codigoCategoria;
    }

    public void setCodigoCategoria(Integer codigoCategoria) {
        this.codigoCategoria = codigoCategoria;
    }

    public Boolean isPossuiIdVindi() {
        return possuiIdVindi;
    }

    public void setPossuiIdVindi(Boolean possuiIdVindi) {
        this.possuiIdVindi = possuiIdVindi;
    }

    public Integer getIdVindi() {
        return idVindi;
    }

    public void setIdVindi(Integer idVindi) {
        this.idVindi = idVindi;
    }

}
