package com.pacto.adm.core.dto.enveloperesposta.contrato;

import com.pacto.adm.core.dto.HistoricoContratoDTO;
import com.pacto.adm.core.dto.enveloperesposta.usuario.EnvelopeRespostaUsuarioDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "Representação das respostas")
public class EnvelopeRespostaHistoricoContratoDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private HistoricoContratoDTO content;

    public HistoricoContratoDTO getContent() {
        return content;
    }

    public void setContent(HistoricoContratoDTO content) {
        this.content = content;
    }

    public static final String atributos =
            "\"codigo\": 1234, "
                    + "\"contrato\": 120, "
                    + "\"descricao\": \"MATRICULA\", "
                    + "\"tipoHistorico\": \"MA\", "
                    + "\"dataRegistro\": \"2025-05-07T00:00:00\", "
                    + "\"situacaoRelativaHistorico\": \"MA\", "
                    + "\"dataInicioSituacao\": \"2025-05-07T00:00:00\", "
                    + "\"dataFinalSituacao\": \"2025-05-07T00:00:00\", "
                    + "\"responsavelRegistro\": {" + EnvelopeRespostaUsuarioDTO.atributos + "}, "
                    + "\"responsavelLiberacaoMudancaHistorico\": {" + EnvelopeRespostaUsuarioDTO.atributos + "}, "
                    + "\"retornoManual\": false, "
                    + "\"dataInicioTemporal\": \"2025-05-07T00:00:00\"";

    public final static String resposta = "{\"content\": {" + atributos + "}}";

}
