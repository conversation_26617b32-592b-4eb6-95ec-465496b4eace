package com.pacto.adm.core.dto.negociacao;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(name = "Convênio", description = "Informações sobre convênios disponíveis para o plano ou negociação.")
public class ConvenioDTO {

    @Schema(description = "Código identificador do convênio.", example = "5")
    private Integer codigo;

    @Schema(description = "Descrição do convênio.", example = "Convênio Empresa X")
    private String descricao;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

}
