package com.pacto.adm.core.dto.integracoes;

import com.pacto.adm.core.dto.empresa.EmpresaDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(name = "Configuração de Integração WeHelp", description = "Configurações para integração com o sistema WeHelp.")
public class ConfiguracaoIntegracaoWeHelpDTO {

    @Schema(description = "Indica se a integração com o WeHelp está habilitada.", example = "true")
    private boolean habilitada;

    @Schema(description = "Indica se o CPF será utilizado como código interno no WeHelp.", example = "false")
    private boolean cpfCodigoInternoWeHelp;

    @Schema(description = "Chave de autenticação ou identificação para integração com o WeHelp.", example = "chave-secreta-123")
    private String chave;

    @Schema(description = "Detalhes da empresa associada à configuração de integração WeHelp.")
    private EmpresaDTO empresa;

    public boolean isHabilitada() {
        return habilitada;
    }

    public void setHabilitada(boolean habilitada) {
        this.habilitada = habilitada;
    }

    public boolean isCpfCodigoInternoWeHelp() {
        return cpfCodigoInternoWeHelp;
    }

    public void setCpfCodigoInternoWeHelp(boolean cpfCodigoInternoWeHelp) {
        this.cpfCodigoInternoWeHelp = cpfCodigoInternoWeHelp;
    }

    public String getChave() {
        return chave;
    }

    public void setChave(String chave) {
        this.chave = chave;
    }

    public EmpresaDTO getEmpresa() {
        return empresa;
    }

    public void setEmpresa(EmpresaDTO empresa) {
        this.empresa = empresa;
    }
}
