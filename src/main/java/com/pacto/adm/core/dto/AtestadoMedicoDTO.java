package com.pacto.adm.core.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;

@JsonInclude
@Schema(name = "Atestado Médico", description = "Informações do atestado médico")
public class AtestadoMedicoDTO {

    @Schema(description = "Código identificador do tipo de atestado médico", example = "291")
    private Integer tipoAtestado;

    @Schema(description = "Data que foi feito o lançamento do atestado", example = "2025-04-08T14:30:00Z")
    private Date dataLancamento;

    @Schema(description = "Código do contrato vinculado ao atestado", example = "1")
    private Integer codigoContrato;

    @Schema(description = "Vigência do atestado descrita por extenso", example = "do dia oito de abril de dois mil e vinte e cinco até o dia dez de abril de dois mil e vinte e cinco")
    private String vigenciaPorExtenso;

    @Schema(description = "URL do arquivo do atestado", example = "www.pactosolucoes.com.br/arquivos/atestado-medico.png")
    private String urlArquivo;

    @Schema(description = "Nome do arquivo do atestado", example = "atestado-medico")
    private String nomeArquivo;

    @Schema(description = "Formato do arquivo do atestado", example = "IMAGEM")
    private String formatoArquivo;

    @Schema(description = "Atestado de apitdão física vinculado ao atestado médico")
    private AtestadoDTO atestadoAptidaoFisica;

    @Schema(description = "Informações do atestado vinculado ao contrato")
    private AtestadoContratoDTO atestadoContrato;

    public Integer getTipoAtestado() {
        return tipoAtestado;
    }

    public void setTipoAtestado(Integer tipoAtestado) {
        this.tipoAtestado = tipoAtestado;
    }

    public Date getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(Date dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public Integer getCodigoContrato() {
        return codigoContrato;
    }

    public void setCodigoContrato(Integer codigoContrato) {
        this.codigoContrato = codigoContrato;
    }

    public String getVigenciaPorExtenso() {
        return vigenciaPorExtenso;
    }

    public void setVigenciaPorExtenso(String vigenciaPorExtenso) {
        this.vigenciaPorExtenso = vigenciaPorExtenso;
    }

    public String getUrlArquivo() {
        return urlArquivo;
    }

    public void setUrlArquivo(String urlArquivo) {
        this.urlArquivo = urlArquivo;
    }

    public String getNomeArquivo() {
        return nomeArquivo;
    }

    public void setNomeArquivo(String nomeArquivo) {
        this.nomeArquivo = nomeArquivo;
    }

    public String getFormatoArquivo() {
        return formatoArquivo;
    }

    public void setFormatoArquivo(String formatoArquivo) {
        this.formatoArquivo = formatoArquivo;
    }

    public AtestadoDTO getAtestadoAptidaoFisica() {
        return atestadoAptidaoFisica;
    }

    public void setAtestadoAptidaoFisica(AtestadoDTO atestadoAptidaoFisica) {
        this.atestadoAptidaoFisica = atestadoAptidaoFisica;
    }

    public AtestadoContratoDTO getAtestadoContrato() {
        return atestadoContrato;
    }

    public void setAtestadoContrato(AtestadoContratoDTO atestadoContrato) {
        this.atestadoContrato = atestadoContrato;
    }
}
