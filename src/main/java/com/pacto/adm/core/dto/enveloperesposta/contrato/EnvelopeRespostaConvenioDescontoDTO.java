package com.pacto.adm.core.dto.enveloperesposta.contrato;

import com.pacto.adm.core.dto.enveloperesposta.empresa.EnvelopeRespostaEmpresaDTO;
import com.pacto.adm.core.dto.enveloperesposta.usuario.EnvelopeRespostaUsuarioDTO;

public class EnvelopeRespostaConvenioDescontoDTO {
    public static final String atributos =
            "\"codigo\": 1, "
                    + "\"dataAssinatura\": \"2023-01-15\", "
                    + "\"dataAutorizacao\": \"2023-01-20\", "
                    + "\"dataFinalVigencia\": \"2024-01-15\", "
                    + "\"dataInicioVigencia\": \"2023-01-25\", "
                    + "\"descontoParcela\": 10.0, "
                    + "\"descricao\": \"Convênio Empresa XYZ\", "
                    + "\"empresa\": {" + EnvelopeRespostaEmpresaDTO.atributos + "}, "
                    + "\"isentarmatricula\": true, "
                    + "\"isentarRematricula\": false, "
                    + "\"responsavelAutorizacao\":{" + EnvelopeRespostaUsuarioDTO.atributos + "}";


}
