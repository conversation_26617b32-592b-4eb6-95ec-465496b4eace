package com.pacto.adm.core.dto;

public class ClienteInativoPeriodoAcessoDTO {

    private Integer codigo;
    private String situacao;
    private Integer codigoMatricula;
    private String matricula;
    private PessoaDTO pessoa;
    private String situacaoContrato;

    public ClienteInativoPeriodoAcessoDTO(
            Integer codigo, String situacao, Integer codigoMatricula,
            String matricula, PessoaDTO pessoa, String situacaoContrato
    ) {
        this.codigo = codigo;
        this.situacao = situacao;
        this.codigoMatricula = codigoMatricula;
        this.matricula = matricula;
        this.pessoa = pessoa;
        this.situacaoContrato = situacaoContrato;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public Integer getCodigoMatricula() {
        return codigoMatricula;
    }

    public void setCodigoMatricula(Integer codigoMatricula) {
        this.codigoMatricula = codigoMatricula;
    }

    public String getMatricula() {
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }

    public PessoaDTO getPessoa() {
        return pessoa;
    }

    public void setPessoa(PessoaDTO pessoa) {
        this.pessoa = pessoa;
    }

    public String getSituacaoContrato() {
        return situacaoContrato;
    }

    public void setSituacaoContrato(String situacaoContrato) {
        this.situacaoContrato = situacaoContrato;
    }
}
