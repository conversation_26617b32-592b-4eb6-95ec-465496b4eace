package com.pacto.adm.core.dto.enveloperesposta.integracoes;

import com.pacto.adm.core.dto.enveloperesposta.empresa.EnvelopeRespostaEmpresaDTO;
import com.pacto.adm.core.dto.integracoes.ConfiguracaoIntegracaoEnvioAcessoPratiqueDTO;
import io.swagger.v3.oas.annotations.media.Schema;

public class EnvelopeRespostaConfiguracaoIntegracaoEnvioAcessoPratiqueDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private ConfiguracaoIntegracaoEnvioAcessoPratiqueDTO content;

    public ConfiguracaoIntegracaoEnvioAcessoPratiqueDTO getContent() {
        return content;
    }

    public void setContent(ConfiguracaoIntegracaoEnvioAcessoPratiqueDTO content) {
        this.content = content;
    }

    public static final String atributos =
            "\"codigo\": 1, "
                    + "\"habilitada\": true, "
                    + "\"empresa\": {" + EnvelopeRespostaEmpresaDTO.atributos + "}";

    public static final String atributosSemEmpresa =
            "\"codigo\": 1, "
                    + "\"habilitada\": true, "
                    + "\"empresa\": \"INFORMAÇÕES DA EMPRESA\"";

    public static final String resposta = "{\"content\": {" + atributos + "}}";
    public static final String requestBody = "{" + atributos + "}";


}
