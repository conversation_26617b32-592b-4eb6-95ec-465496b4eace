package com.pacto.adm.core.dto;

import com.pacto.adm.core.dto.empresa.EmpresaDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Schema (description = "Informações do Recibo de Pagamento")
public class ReciboPagamentoDTO {

    @Schema(description = "Código único identificador do recibo de pagamento", example = "1")
    private Integer codigo;

    @Schema(description = "Valor total do recibo de pagamento", example = "299.99")
    private BigDecimal valorTotal;

    @Schema(description = "Nome da pessoa responsável pelo pagamento", example = "João Silva")
    private String nomePessoaPagador;

    @Schema(description = "Data do recibo de pagamento", example = "2023-10-15")
    private Date data;

    @Schema(description = "Detalhes da empresa associada ao recibo de pagamento")
    private EmpresaDTO empresa;

    @Schema(description = "Lista de pagamentos associados ao recibo")
    private List<MovPagamentoDTO> pagamentos;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public BigDecimal getValorTotal() {
        return valorTotal;
    }

    public void setValorTotal(BigDecimal valorTotal) {
        this.valorTotal = valorTotal;
    }

    public String getNomePessoaPagador() {
        return nomePessoaPagador;
    }

    public void setNomePessoaPagador(String nomePessoaPagador) {
        this.nomePessoaPagador = nomePessoaPagador;
    }

    public Date getData() {
        return data;
    }

    public void setData(Date data) {
        this.data = data;
    }

    public EmpresaDTO getEmpresa() {
        return empresa;
    }

    public void setEmpresa(EmpresaDTO empresa) {
        this.empresa = empresa;
    }

    public List<MovPagamentoDTO> getPagamentos() {
        return pagamentos;
    }

    public void setPagamentos(List<MovPagamentoDTO> pagamentos) {
        this.pagamentos = pagamentos;
    }
}
