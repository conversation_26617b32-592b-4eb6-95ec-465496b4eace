package com.pacto.adm.core.dto.modalidade;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(name = "Modalidade Empresa", description = "Informações sobre a associação entre modalidade e empresa.")
public class ModalidadeEmpresaDTO {

    @Schema(description = "Código único identificador da associação entre modalidade e empresa.", example = "110")
    private Integer codigo;

    @Schema(description = "Código da empresa associada à modalidade.", example = "2001")
    private Integer empresa;

    @Schema(description = "Nome da empresa associada à modalidade.", example = "Academia Top Fit")
    private String nomeEmpresa;

    @Schema(description = "Código da modalidade associada à empresa.", example = "90")
    private Integer modalidade;

    public ModalidadeEmpresaDTO() {}

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public String getNomeEmpresa() {
        return nomeEmpresa;
    }

    public void setNomeEmpresa(String nomeEmpresa) {
        this.nomeEmpresa = nomeEmpresa;
    }

    public Integer getModalidade() {
        return modalidade;
    }

    public void setModalidade(Integer modalidade) {
        this.modalidade = modalidade;
    }



}
