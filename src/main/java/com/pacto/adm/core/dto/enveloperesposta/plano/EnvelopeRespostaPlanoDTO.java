package com.pacto.adm.core.dto.enveloperesposta.plano;

import com.pacto.adm.core.dto.PlanoDTO;
import io.swagger.v3.oas.annotations.media.Schema;


public class EnvelopeRespostaPlanoDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private PlanoDTO content;

    public PlanoDTO getContent() {
        return content;
    }

    public void setContent(PlanoDTO content) {
        this.content = content;
    }


    public static final String atributos =
            "\"codigo\": 1, "
                    + "\"descricao\": \"Plano Básico Mensal\", "
                    + "\"quantidadeCompartilhamentos\": 5, "
                    + "\"restringeVendaPorCategoria\": true, "
                    + "\"categorias\": [1, 2, 3], "
                    + "\"bloquearRecompra\": false, "
                    + "\"permitirTransferenciaDeCredito\": false";

    public final static String resposta = "{\"content\": {" + atributos + "}}";

    public final static String requestBody = "{" + atributos + "}";

}
