package com.pacto.adm.core.dto.enveloperesposta.cliente.dadosauxiliares;

import com.pacto.adm.core.dto.ClienteDadosAuxiliaresDTO;
import com.pacto.adm.core.dto.enveloperesposta.pagamento.EnvelopeRespostaMovParcelaDTO;
import io.swagger.v3.oas.annotations.media.Schema;


//@Schema(name = "Resposta Dados Auxiliares de Cliente", description = "Representação das respostas das requisições que devolvem dados auxiliares de um cliente")
public class EnvelopeRespostaClienteDadosAuxiliaresDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private ClienteDadosAuxiliaresDTO content;

    public ClienteDadosAuxiliaresDTO getContent() {
        return content;
    }

    public void setContent(ClienteDadosAuxiliaresDTO content) {
        this.content = content;
    }

    public static final String atributos =
            "\"dataInclusaoSpc\": 1745808000000, "
                    + "\"parcelasSpc\": [{" + EnvelopeRespostaMovParcelaDTO.atributos + "}]";

    public final static String resposta = "{\"content\": {" + atributos + "}}";


}
