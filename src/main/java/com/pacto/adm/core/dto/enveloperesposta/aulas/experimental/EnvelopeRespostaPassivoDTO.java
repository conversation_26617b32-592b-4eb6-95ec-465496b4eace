package com.pacto.adm.core.dto.enveloperesposta.aulas.experimental;

import com.pacto.adm.core.dto.enveloperesposta.cliente.EnvelopeRespostaClienteDTO;
import com.pacto.adm.core.dto.enveloperesposta.cliente.questionarios.EnvelopeRespostaEventoDTO;
import com.pacto.adm.core.dto.enveloperesposta.contrato.EnvelopeRespostaContratoDTO;
import com.pacto.adm.core.dto.enveloperesposta.empresa.EnvelopeRespostaEmpresaDTO;
import com.pacto.adm.core.dto.enveloperesposta.usuario.EnvelopeRespostaUsuarioDTO;
import com.pacto.adm.core.dto.passivo.PassivoDTO;
import io.swagger.v3.oas.annotations.media.Schema;

//@Schema(name = "Respostas Aluguel de Armários", description = "Representação das respostas contendo uma lista de aluguel de armários")
public class EnvelopeRespostaPassivoDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private PassivoDTO content;

    public PassivoDTO getContent() {
        return content;
    }

    public void setContent(PassivoDTO content) {
        this.content = content;
    }

    public static final String atributos =
            "\"codigo\": 501, "
                    + "\"dia\": \"2024-04-15T00:00:00Z\", "
                    + "\"email\": \"<EMAIL>\", "
                    + "\"idLead\": 1234, "
                    + "\"lead\": true, "
                    + "\"metaExtra\": false, "
                    + "\"nome\": \"João da Silva\", "
                    + "\"nomeConsulta\": \"João S.\", "
                    + "\"observacao\": \"Interessado em plano anual.\", "
                    + "\"origemSistema\": 1, "
                    + "\"telefoneCelular\": \"(11) 912345678\", "
                    + "\"telefoneResidencial\": \"(11) 34567890\", "
                    + "\"telefoneTrabalho\": \"(11) 31234567\", "
                    + "\"urlRd\": \"https://rd.com.br/lead/uuid\", "
                    + "\"uuid\": \"550e8400-e29b-41d4-a716-************\", "
                    + "\"cliente\": {" + EnvelopeRespostaClienteDTO.atributos + "}, "
                    + "\"colaboradorResponsavel\": {" + EnvelopeRespostaUsuarioDTO.atributos + "}, "
                    + "\"contrato\": {" + EnvelopeRespostaContratoDTO.atributos + "}, "
                    + "\"empresa\": {" + EnvelopeRespostaEmpresaDTO.atributos + "}, "
                    + "\"evento\": {" + EnvelopeRespostaEventoDTO.atributos + "}, "
                    + "\"objecao\": {" + EnvelopeRespostaObjecaoDTO.atributos + "}, "
                    + "\"responsavelCadastro\": {" + EnvelopeRespostaUsuarioDTO.atributos + "}";

    public final static String resposta = "{\"content\": {" + atributos + "}}";

    public final static String requestBody = "{" + atributos + "}";
}
