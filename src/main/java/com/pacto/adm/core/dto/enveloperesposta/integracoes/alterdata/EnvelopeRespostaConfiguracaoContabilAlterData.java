package com.pacto.adm.core.dto.enveloperesposta.integracoes.alterdata;

import com.pacto.adm.core.dto.integracoes.ConfiguracaoIntegracaoSistemaContabilAlterDataDTO;
import io.swagger.v3.oas.annotations.media.Schema;

public class EnvelopeRespostaConfiguracaoContabilAlterData {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private ConfiguracaoIntegracaoSistemaContabilAlterDataDTO content;

    public ConfiguracaoIntegracaoSistemaContabilAlterDataDTO getContent() {
        return content;
    }

    public void setContent(ConfiguracaoIntegracaoSistemaContabilAlterDataDTO content) {
        this.content = content;
    }

    public static final String atributos =
            "\"habilitarExportacaoAlterData\": true";

    public static final String resposta = "{\"content\": {" + atributos + "}}";
    public static final String requestBody = "{" + atributos + "}";

}
