package com.pacto.adm.core.dto.enveloperesposta.integracoes;

import com.pacto.adm.core.dto.enveloperesposta.empresa.EnvelopeRespostaEmpresaDTO;
import com.pacto.adm.core.dto.integracoes.ConfiguracaoIntegracaoCDLSPCDTO;
import io.swagger.v3.oas.annotations.media.Schema;

public class EnvelopeRespostaConfiguracaoIntegracaoCDLSPCDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private ConfiguracaoIntegracaoCDLSPCDTO content;

    public ConfiguracaoIntegracaoCDLSPCDTO getContent() {
        return content;
    }

    public void setContent(ConfiguracaoIntegracaoCDLSPCDTO content) {
        this.content = content;
    }

    public static final String atributos =
            "\"consultarNovoCadastroSPC\": true, "
                    + "\"pesquisaAutomaticaSPC\": false, "
                    + "\"codigoAssociadoSPC\": 123456, "
                    + "\"operadorSPC\": \"operador123\", "
                    + "\"senhaSPC\": \"senha123\", "
                    + "\"empresa\": {" + EnvelopeRespostaEmpresaDTO.atributos + "}";

    public static final String atributosSemEmpresa =
            "\"consultarNovoCadastroSPC\": true, "
                    + "\"pesquisaAutomaticaSPC\": false, "
                    + "\"codigoAssociadoSPC\": 123456, "
                    + "\"operadorSPC\": \"operador123\", "
                    + "\"senhaSPC\": \"senha123\", "
                    + "\"empresa\": \"INFORMAÇÕES DA EMPRESA\"";

    public final static String resposta = "{\"content\": {" + atributos + "}}";
    public final static String requestBody = "{" + atributos + "}";


}
